<template>
  <mt-dialog
    ref="dialog"
    css-class="condition-setting-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
    width="600px"
  >
    <div class="dialog-content">
      <div class="setting-section">
        <div class="section-header">
          <h4>{{ $t('可用搜索条件') }}</h4>
          <div class="select-all-wrapper">
            <mt-checkbox
              v-model="selectAll"
              @change="handleSelectAllChange"
              :indeterminate="isIndeterminate"
            >
              {{ $t('全选') }}
            </mt-checkbox>
          </div>
        </div>
        <div class="available-conditions">
          <div
            v-for="condition in availableConditions"
            :key="condition.field"
            class="condition-item"
            :class="{
              active: isConditionVisible(condition.field),
              required: isRequiredCondition(condition.field)
            }"
            @click="toggleCondition(condition)"
          >
            <i
              class="mt-icons"
              :class="isConditionVisible(condition.field) ? 'mt-icon-MT_Check' : 'mt-icon-MT_Add'"
            ></i>
            <span>{{ condition.label }}</span>
          </div>
        </div>
      </div>

      <div class="setting-section">
        <h4>
          {{ $t('已选择的搜索条件') }} <span class="count">({{ visibleConditions.length }})</span>
        </h4>
        <div class="selected-conditions">
          <draggable
            v-model="visibleConditions"
            group="conditions"
            @start="onDragStart"
            @end="onDragEnd"
            :options="{ animation: 150 }"
          >
            <div
              v-for="(element, index) in visibleConditions"
              :key="element.field"
              class="selected-condition-item"
              :class="{
                dragging: isDragging,
                required: isRequiredCondition(element.field)
              }"
            >
              <i class="drag-handle mt-icons mt-icon-MT_Menu"></i>
              <span class="condition-label">{{ element.label }}</span>
              <span v-if="isRequiredCondition(element.field)" class="required-tag">{{
                $t('必需')
              }}</span>
              <i
                v-if="!isRequiredCondition(element.field)"
                class="remove-btn mt-icons mt-icon-MT_Close"
                @click="removeCondition(index)"
              ></i>
              <i
                v-else
                class="lock-icon mt-icons mt-icon-MT_Lock"
                :title="$t('该搜索条件不可隐藏')"
              ></i>
            </div>
          </draggable>
          <div v-if="visibleConditions.length === 0" class="empty-state">
            {{ $t('请从左侧选择搜索条件') }}
          </div>
        </div>
      </div>

      <div class="setting-tips">
        <i class="mt-icons mt-icon-MT_Info"></i>
        <span>{{ $t('提示：拖拽可调整搜索条件显示顺序，每行最多显示5个条件') }}</span>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import MtDialog from '@mtech-ui/dialog'
import MtCheckbox from '@mtech-ui/checkbox'
import draggable from 'vuedraggable'

export default {
  components: {
    MtDialog,
    MtCheckbox,
    draggable
  },
  props: {
    dialogData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogTitle: this.$t('设置搜索条件'),
      availableConditions: [],
      visibleConditions: [],
      conditionOrder: [],
      requiredConditions: [], // 不可隐藏的条件
      isDragging: false,
      selectAll: false, // 全选状态
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirmSetting,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    // 是否为半选状态
    isIndeterminate() {
      const selectedCount = this.visibleConditions.length
      const totalCount = this.availableConditions.length
      return selectedCount > 0 && selectedCount < totalCount
    }
  },
  watch: {
    // 监听已选择条件的变化，更新全选状态
    visibleConditions: {
      handler: function () {
        this.updateSelectAllState()
      },
      deep: true
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.initData()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
    },

    initData() {
      this.availableConditions = this.dialogData.availableConditions || []
      this.visibleConditions = [...(this.dialogData.visibleConditions || [])]
      this.conditionOrder = [...(this.dialogData.conditionOrder || [])]
      this.requiredConditions = this.dialogData.requiredConditions || []

      // 如果没有已选择的条件，默认选择所有条件
      if (this.visibleConditions.length === 0 && this.availableConditions.length > 0) {
        this.visibleConditions = [...this.availableConditions]
      }

      // 更新全选状态
      this.updateSelectAllState()
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    },

    isConditionVisible(field) {
      return this.visibleConditions.some((condition) => condition.field === field)
    },

    isRequiredCondition(field) {
      return this.requiredConditions.includes(field)
    },

    toggleCondition(condition) {
      const index = this.visibleConditions.findIndex((item) => item.field === condition.field)
      if (index > -1) {
        // 检查是否为必需字段，必需字段不能被移除
        if (this.isRequiredCondition(condition.field)) {
          this.$toast({ content: this.$t('该搜索条件不可隐藏'), type: 'warning' })
          return
        }
        this.visibleConditions.splice(index, 1)
      } else {
        this.visibleConditions.push({ ...condition })
      }

      // 更新全选状态
      this.$nextTick(() => {
        this.updateSelectAllState()
      })
    },

    removeCondition(index) {
      const condition = this.visibleConditions[index]
      if (this.isRequiredCondition(condition.field)) {
        this.$toast({ content: this.$t('该搜索条件不可隐藏'), type: 'warning' })
        return
      }
      this.visibleConditions.splice(index, 1)

      // 更新全选状态
      this.$nextTick(() => {
        this.updateSelectAllState()
      })
    },

    onDragStart() {
      this.isDragging = true
    },

    onDragEnd() {
      this.isDragging = false

      // 拖拽结束后，更新全选状态
      this.$nextTick(() => {
        this.updateSelectAllState()
      })
    },

    // 更新全选状态
    updateSelectAllState() {
      const selectedCount = this.visibleConditions.length
      const totalCount = this.availableConditions.length
      this.selectAll = selectedCount === totalCount && totalCount > 0
    },

    // 处理全选变化
    handleSelectAllChange({ checked }) {
      if (checked) {
        // 全选：选择所有可用条件
        this.visibleConditions = [...this.availableConditions]
      } else {
        // 全不选：只保留必需条件
        this.visibleConditions = this.availableConditions.filter((condition) =>
          this.isRequiredCondition(condition.field)
        )
      }

      // 同时更新可用条件区域的选中状态
      this.$nextTick(() => {
        this.updateSelectAllState()
      })
    },

    confirmSetting() {
      const result = {
        visibleConditions: this.visibleConditions,
        conditionOrder: this.visibleConditions.map((item) => item.field)
      }

      this.$emit('confirmSuccess', result)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.condition-setting-dialog {
  .dialog-content {
    padding: 20px;

    .setting-section {
      margin-bottom: 24px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h4 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: #333;

          .count {
            color: #666;
            font-weight: normal;
          }
        }

        .select-all-wrapper {
          ::v-deep .mt-checkbox {
            .e-checkbox-wrapper {
              .e-label {
                font-size: 13px;
                color: #666;
              }
            }
          }
        }
      }

      .available-conditions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .condition-item {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          border: 1px solid #e9e9e9;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;
          background: #fff;

          &:hover {
            border-color: #2783fe;
            background: #f8f9ff;
          }

          &.active {
            border-color: #2783fe;
            background: #2783fe;
            color: #fff;
          }

          .mt-icons {
            margin-right: 6px;
            font-size: 12px;
          }

          span {
            font-size: 13px;
          }

          .required-tag {
            margin-left: 6px;
            padding: 2px 6px;
            background: #ff4d4f;
            color: #fff;
            font-size: 10px;
            border-radius: 2px;
            font-weight: 500;
          }

          &.required {
            border-color: #ff4d4f;
            background: #fff2f0;

            &.active {
              background: #ff4d4f;
              border-color: #ff4d4f;
            }

            &:hover {
              border-color: #ff4d4f;
              background: #fff2f0;

              &.active {
                background: #ff4d4f;
              }
            }
          }
        }
      }

      .selected-conditions {
        min-height: 120px;
        border: 1px dashed #e9e9e9;
        border-radius: 4px;
        padding: 12px;

        .selected-condition-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          margin-bottom: 8px;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          cursor: move;
          transition: all 0.3s;

          &:hover {
            background: #e9ecef;
            border-color: #dee2e6;
          }

          &.dragging {
            opacity: 0.8;
            transform: rotate(2deg);
          }

          .drag-handle {
            margin-right: 8px;
            color: #999;
            cursor: grab;

            &:active {
              cursor: grabbing;
            }
          }

          .condition-label {
            flex: 1;
            font-size: 13px;
            color: #333;
          }

          .remove-btn {
            color: #999;
            cursor: pointer;
            padding: 2px;

            &:hover {
              color: #f56c6c;
            }
          }

          .lock-icon {
            color: #ff4d4f;
            padding: 2px;
          }

          .required-tag {
            margin-left: 8px;
            margin-right: 8px;
            padding: 2px 6px;
            background: #ff4d4f;
            color: #fff;
            font-size: 10px;
            border-radius: 2px;
            font-weight: 500;
          }

          &.required {
            background: #fff2f0;
            border-color: #ffccc7;

            .drag-handle {
              color: #ff4d4f;
            }

            .condition-label {
              color: #ff4d4f;
              font-weight: 500;
            }
          }
        }

        .empty-state {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 80px;
          color: #999;
          font-size: 13px;
        }
      }
    }

    .setting-tips {
      display: flex;
      align-items: center;
      padding: 12px;
      background: #f8f9ff;
      border: 1px solid #e6f7ff;
      border-radius: 4px;

      .mt-icons {
        margin-right: 8px;
        color: #2783fe;
        font-size: 14px;
      }

      span {
        font-size: 12px;
        color: #666;
      }
    }
  }
}
</style>
