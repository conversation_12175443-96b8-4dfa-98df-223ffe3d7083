# CollapseSearchNew 组件

高度可配置的智能搜索组件，支持展开/收起、模板管理、条件设置等功能。

## 🌟 功能特性

### 🔍 智能搜索体验
- ✅ **智能展开收起**: 展开时显示全部搜索条件，收起时只显示前5个条件
- ✅ **智能布局**: 一行显示5个搜索条件，超过自动换行
- ✅ **回车搜索**: 支持在搜索条件中按回车键快速触发搜索
- ✅ **实时响应**: 搜索条件变化时实时更新界面状态

### 📋 搜索模板管理
- ✅ **模板保存**: 将当前搜索条件和显示配置保存为模板
- ✅ **模板切换**: 快速切换不同的搜索模板
- ✅ **默认模板**: 支持设置默认模板，刷新页面时自动应用
- ✅ **模板操作**: 支持重命名、删除、设为默认等操作
- ✅ **模板优先级**: 刷新页面时按 默认模板 > 当前模板 > 非模板状态 的优先级恢复
- ✅ **展开时显示**: 搜索模板功能只在展开状态下可见

### ⚙️ 搜索条件设置
- ✅ **条件显示控制**: 自定义哪些搜索条件显示/隐藏
- ✅ **拖拽排序**: 支持拖拽调整搜索条件的显示顺序
- ✅ **必需条件**: 支持设置不可隐藏的必需搜索条件
- ✅ **条件分组**: 每行显示5个搜索条件，自动换行
- ✅ **展开时显示**: 条件设置功能只在展开状态下可见

### 💾 状态持久化
- ✅ **本地存储**: 搜索条件状态自动保存到 sessionStorage
- ✅ **服务器同步**: 支持将用户设置同步到服务器
- ✅ **状态恢复**: 页面刷新时自动恢复上次的搜索状态
- ✅ **数据格式兼容**: 支持新旧数据格式的自动转换

### 🎨 界面设计
- ✅ **响应式布局**: 适配不同屏幕尺寸
- ✅ **平滑动画**: 展开/收起、按钮显示/隐藏使用CSS动画
- ✅ **视觉层次**: 清晰的功能分区和视觉引导
- ✅ **完美对齐**: 文字和图标完美垂直居中对齐

### 🔧 重置功能优化
- ✅ **智能重置**: 重置功能只重置查询条件值，不重置模板选择
- ✅ **保持状态**: 重置后保持当前模板和条件显示配置

## Props

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `backgroundColor` | String | `#f9f9f9` | 搜索区域背景色 |
| `showButton` | Boolean | `true` | 是否显示操作按钮 |
| `defaultMaxHeight` | String/Number | `60` | 默认最大高度 |
| `maxHeight` | String | `600px` | 展开时最大高度 |
| `minRows` | String/Number | `1` | 最小行数 |
| `isGridDisplay` | Boolean | `false` | 是否为网格布局 |
| `expended` | Boolean | `false` | 是否默认展开 |

### 搜索模板属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enableSearchTemplate` | Boolean | `false` | 是否启用搜索模板功能 |
| `gridId` | String | `''` | 网格ID，用于存储搜索模板 |
| `defaultSearchTemplates` | Array | `[]` | 默认搜索模板（开发者预设） |
| `searchFormData` | Object | `{}` | 当前搜索表单数据 |

### 搜索条件设置属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enableSearchConditionSetting` | Boolean | `false` | 是否启用搜索条件设置功能 |
| `availableConditions` | Array | `[]` | 可用的搜索条件配置 |
| `requiredConditions` | Array | `[]` | 不可隐藏的搜索条件字段名数组 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `reset` | - | 重置搜索条件 |
| `search` | - | 执行搜索 |
| `templateSearch` | `searchRule` | 使用模板搜索 |
| `getSearchConditions` | `callback` | 获取当前搜索条件 |
| `applySearchConditions` | `conditions` | 应用搜索条件 |
| `conditionSettingChange` | `result` | 搜索条件设置变化 |
| `visible-conditions-change` | `conditions` | 可见搜索条件发生变化时触发 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| `default` | `{ visibleConditions, isExpanded }` | 搜索表单内容，提供当前可见的搜索条件和展开状态 |
| `buttons-bar` | - | 自定义操作按钮 |

### Slot参数说明

- `visibleConditions`: 当前应该显示的搜索条件数组
  - 展开时：返回所有 `availableConditions`
  - 收起时：返回前5个 `availableConditions`
- `isExpanded`: 当前展开状态（布尔值）

## 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getCurrentSearchConditions` | - | Object | 获取当前搜索条件 |
| `setSearchConditions` | conditions | - | 设置搜索条件 |
| `clearCurrentTemplate` | - | - | 清空当前选中的搜索模板 |
| `getCurrentVisibleConditions` | - | Array | 获取当前可见的搜索条件 |
| `toggleExpanded` | - | - | 切换展开/收起状态 |

## 使用示例

### 基础用法 - 展开收起控制

```vue
<template>
  <collapse-search-new
    :expended="isExpended"
    :available-conditions="availableConditions"
    @reset="handleReset"
    @search="handleSearch"
    @visible-conditions-change="handleVisibleConditionsChange"
  >
    <template #default="{ visibleConditions }">
      <mt-form :model="searchForm" class="search-conditions-grid">
        <mt-form-item
          v-for="condition in visibleConditions"
          :key="condition.field"
          :label="condition.label"
        >
          <component
            :is="getComponentType(condition.type)"
            v-model="searchForm[condition.field]"
            v-bind="getComponentProps(condition)"
          />
        </mt-form-item>
      </mt-form>
    </template>
  </collapse-search-new>
</template>

<script>
export default {
  data() {
    return {
      isExpended: false, // 默认收起状态
      searchForm: {
        productName: '',
        category: '',
        status: '',
        // ... 更多字段
      },
      availableConditions: [
        { field: 'productName', label: '产品名称', type: 'input' },
        { field: 'category', label: '产品类别', type: 'select' },
        { field: 'status', label: '产品状态', type: 'select' },
        { field: 'minPrice', label: '最低价格', type: 'number' },
        { field: 'maxPrice', label: '最高价格', type: 'number' },
        { field: 'createTime', label: '创建时间', type: 'date' },
        { field: 'creator', label: '创建人', type: 'input' },
        { field: 'supplier', label: '供应商', type: 'input' },
        // ... 更多条件
      ]
    }
  },
  methods: {
    handleReset() {
      // 重置搜索表单
    },
    handleSearch() {
      // 执行搜索
    },
    handleVisibleConditionsChange(conditions) {
      console.log('当前显示的搜索条件:', conditions)
      // 收起时只显示前5个，展开时显示全部
    },
    getComponentType(type) {
      // 根据类型返回对应的组件
    },
    getComponentProps(condition) {
      // 根据条件返回组件属性
    }
  }
}
</script>
```

### 传统用法 - 一行五个搜索条件

```vue
<template>
  <collapse-search-new
    :expended="true"
    @reset="handleReset"
    @search="handleSearch"
  >
    <mt-form :model="searchForm">
      <mt-form-item label="产品名称">
        <mt-input v-model="searchForm.productName" placeholder="请输入产品名称" />
      </mt-form-item>
      <mt-form-item label="产品状态">
        <mt-select v-model="searchForm.status" :data-source="statusOptions" />
      </mt-form-item>
      <mt-form-item label="产品类别">
        <mt-select v-model="searchForm.category" :data-source="categoryOptions" />
      </mt-form-item>
      <mt-form-item label="创建人">
        <mt-input v-model="searchForm.creator" placeholder="请输入创建人" />
      </mt-form-item>
      <mt-form-item label="创建时间">
        <mt-date-picker v-model="searchForm.createTime" />
      </mt-form-item>
      <!-- 超过5个会自动换行 -->
      <mt-form-item label="更新人">
        <mt-input v-model="searchForm.updater" placeholder="请输入更新人" />
      </mt-form-item>
    </mt-form>
  </collapse-search-new>
</template>

<script>
import CollapseSearchNew from '@/components/CollapseSearchNew'

export default {
  components: {
    CollapseSearchNew
  },
  data() {
    return {
      searchForm: {
        name: '',
        status: ''
      },
      statusOptions: [
        { text: '启用', value: '1' },
        { text: '禁用', value: '0' }
      ]
    }
  },
  methods: {
    handleReset() {
      this.searchForm = {
        name: '',
        status: ''
      }
    },
    handleSearch() {
      // 执行搜索逻辑
      console.log('搜索条件:', this.searchForm)
    }
  }
}
</script>
```

### 搜索条件控制功能

```vue
<template>
  <collapse-search-new
    :expended="isExpended"
    :available-conditions="availableConditions"
    :required-conditions="requiredConditions"
    :enable-search-condition-setting="true"
    :grid-id="'condition-control-grid'"
    @reset="handleReset"
    @search="handleSearch"
    @conditionSettingChange="handleConditionSettingChange"
  >
    <template #default="{ visibleConditions }">
      <mt-form :model="searchForm" class="search-conditions-grid">
        <mt-form-item
          v-for="condition in visibleConditions"
          :key="condition.field"
          :label="condition.label"
        >
          <component
            :is="getComponentType(condition.type)"
            v-model="searchForm[condition.field]"
            v-bind="getComponentProps(condition)"
          />
        </mt-form-item>
      </mt-form>
    </template>
  </collapse-search-new>
</template>

<script>
export default {
  data() {
    return {
      isExpended: false,
      searchForm: {
        productName: '',
        category: '',
        status: '',
        creator: ''
      },
      // 定义所有可用的搜索条件
      availableConditions: [
        { field: 'productName', label: '产品名称', type: 'input' },
        { field: 'category', label: '产品类别', type: 'select' },
        { field: 'status', label: '产品状态', type: 'select' },
        { field: 'creator', label: '创建人', type: 'input' }
      ],
      // 定义不可隐藏的必需条件
      requiredConditions: ['productName', 'status']
    }
  },
  methods: {
    handleConditionSettingChange(result) {
      console.log('搜索条件设置变化:', result)
      // result.visibleConditions 包含用户选择的条件
      // result.actualVisibleConditions 包含当前实际显示的条件
    },
    getComponentType(type) {
      const typeMap = {
        input: 'mt-input',
        select: 'mt-select'
      }
      return typeMap[type] || 'mt-input'
    },
    getComponentProps(condition) {
      // 根据条件类型返回组件属性
      return {}
    }
  }
}
</script>
```

### 完整功能 - 搜索模板 + 条件设置

```vue
<template>
  <collapse-search-new
    :expended="true"
    :enable-search-template="true"
    :enable-search-condition-setting="true"
    :grid-id="'advanced-search-grid'"
    :default-search-templates="defaultTemplates"
    :available-conditions="availableConditions"
    :search-form-data="searchForm"
    @reset="handleReset"
    @search="handleSearch"
    @templateSearch="handleTemplateSearch"
    @getSearchConditions="handleGetSearchConditions"
    @applySearchConditions="handleApplySearchConditions"
    @conditionSettingChange="handleConditionSettingChange"
  >
    <mt-form :model="searchForm">
      <mt-form-item
        v-for="condition in visibleConditions"
        :key="condition.field"
        :label="condition.label"
      >
        <component
          :is="getComponentType(condition.type)"
          v-model="searchForm[condition.field]"
          v-bind="getComponentProps(condition)"
        />
      </mt-form-item>
    </mt-form>
  </collapse-search-new>
</template>

<script>
import CollapseSearchNew from '@/components/CollapseSearchNew'

export default {
  components: {
    CollapseSearchNew
  },
  data() {
    return {
      searchForm: {
        username: '',
        department: '',
        status: ''
      },
      defaultTemplates: [
        {
          templateName: '热销产品',
          searchRule: {
            status: '1',
            category: 'electronics'
          }
        }
      ],

      // 可用搜索条件配置
      availableConditions: [
        { field: 'productName', label: '产品名称', type: 'input' },
        { field: 'category', label: '产品类别', type: 'select', options: 'categoryOptions' },
        { field: 'status', label: '产品状态', type: 'select', options: 'statusOptions' },
        { field: 'minPrice', label: '最低价格', type: 'number' },
        { field: 'createTimeRange', label: '创建时间', type: 'dateRange' }
      ],

      // 当前显示的搜索条件
      visibleConditions: [],
      departmentOptions: [
        { text: 'IT部门', value: 'IT' },
        { text: '财务部门', value: 'Finance' }
      ],
      statusOptions: [
        { text: '启用', value: '1' },
        { text: '禁用', value: '0' }
      ]
    }
  },
  methods: {
    handleReset() {
      this.searchForm = {
        username: '',
        department: '',
        status: ''
      }
    },
    handleSearch() {
      console.log('搜索条件:', this.searchForm)
      // 执行搜索逻辑
    },
    handleTemplateSearch(searchRule) {
      console.log('使用模板搜索:', searchRule)
      // 应用模板搜索条件
      this.handleApplySearchConditions(searchRule)
      // 执行搜索
      this.handleSearch()
    },
    handleGetSearchConditions(callback) {
      // 返回当前搜索条件
      callback(this.searchForm)
    },
    handleApplySearchConditions(conditions) {
      // 应用搜索条件
      this.searchForm = { ...this.searchForm, ...conditions }
    },

    handleConditionSettingChange(result) {
      // 搜索条件设置变化
      this.visibleConditions = result.visibleConditions
    },

    getComponentType(type) {
      // 根据类型返回对应的组件名
      const typeMap = {
        input: 'mt-input',
        select: 'mt-select',
        number: 'mt-input-number',
        date: 'mt-date-picker',
        dateRange: 'mt-date-range-picker'
      }
      return typeMap[type] || 'mt-input'
    },

    getComponentProps(condition) {
      // 根据条件配置返回组件属性
      const props = { placeholder: `请输入${condition.label}` }
      if (condition.type === 'select' && condition.options) {
        props.dataSource = this[condition.options]
      }
      return props
    }
  }
}
</script>
```

## 📊 数据结构

### 搜索模板数据结构

```javascript
const defaultSearchTemplates = [
  {
    templateName: '模板名称',
    searchConditions: {         // 搜索条件值（新格式，推荐）
      field1: 'value1',
      field2: 'value2'
    },
    searchRule: {              // 搜索条件值（旧格式，兼容）
      field1: 'value1',
      field2: 'value2'
    },
    visibleConditions: [       // 可见的搜索条件字段（新增）
      'field1',
      'field2',
      'field3'
    ],
    conditionOrder: [          // 搜索条件显示顺序（可选）
      'field1',
      'field2',
      'field3'
    ]
  }
]
```

### 搜索条件配置数据结构

```javascript
const availableConditions = [
  {
    field: 'productName',        // 字段名（推荐使用）
    label: '产品名称',           // 显示标签
    type: 'input',              // 组件类型：input, select, number, date, dateRange
    options: 'categoryOptions', // 选项数据源（仅select类型需要）
    placeholder: '请输入',      // 占位符（可选）
    required: false             // 是否必填（可选）
  }
]
```

### 字段名称兼容性

组件支持两种字段名称属性，提供更好的兼容性：

#### 使用 field 属性（推荐）
```javascript
const availableConditions = [
  { field: 'productName', label: '产品名称', type: 'input' },
  { field: 'category', label: '产品类别', type: 'select' }
]
```

#### 使用 prop 属性（兼容性支持）
```javascript
const availableConditions = [
  { prop: 'productName', label: '产品名称', type: 'input' },
  { prop: 'category', label: '产品类别', type: 'select' }
]
```

#### 混合使用
```javascript
const availableConditions = [
  { field: 'productName', label: '产品名称', type: 'input' },
  { prop: 'category', label: '产品类别', type: 'select' }  // 兼容旧配置
]
```

> **注意：** 当同时存在 `field` 和 `prop` 属性时，优先使用 `field` 属性。

### 模板数据格式兼容性

组件支持新旧两种数据格式：

#### 新格式（推荐）
```javascript
{
  templateName: '采购订单查询',
  searchConditions: {          // 使用 searchConditions
    itemCode: 'ABC123',
    status: 'active'
  },
  visibleConditions: [         // 新增：可见条件配置
    'itemCode',
    'itemName',
    'status'
  ]
}
```

#### 旧格式（兼容）
```javascript
{
  templateName: '采购订单查询',
  searchRule: {               // 使用 searchRule（向后兼容）
    itemCode: 'ABC123',
    status: 'active'
  }
}
```

### visibleConditions 数据格式

组件内部统一使用字段名数组格式：

#### 标准格式（字段名数组）
```javascript
visibleConditions: ['itemCode', 'itemName', 'status']
```

#### 兼容格式（对象数组，自动转换）
```javascript
visibleConditions: [
  {field: 'itemCode', label: '物料编码', type: 'input'},
  {field: 'itemName', label: '物料名称', type: 'input'},
  {field: 'status', label: '状态', type: 'select'}
]
```

### 支持的组件类型

- `input`: 文本输入框 (mt-input)
- `select`: 下拉选择框 (mt-select)
- `number`: 数字输入框 (mt-input-number)
- `date`: 日期选择器 (mt-date-picker)
- `dateRange`: 日期范围选择器 (mt-date-range-picker)

## API 接口

组件使用以下 API 接口进行数据持久化：

- `POST /lowcodeWeb/tenant/user-memory/save` - 保存用户内存数据
- `GET /lowcodeWeb/tenant/user-memory/get` - 获取用户内存数据

## ⚠️ 注意事项

### 基础配置
1. **布局特性**: 组件自动实现一行5个搜索条件，超过5个自动换行
2. **搜索模板**: 启用搜索模板功能时，必须提供 `gridId` 属性
3. **条件设置**: 启用搜索条件设置时，需要提供 `availableConditions` 配置
4. **响应式**: 组件支持响应式布局，在不同屏幕尺寸下自动调整列数

### 功能限制
5. **模板功能显示**: 搜索模板相关功能只在展开状态下显示
6. **条件设置显示**: 搜索条件设置功能只在展开状态下显示
7. **重置行为**: 重置功能只重置查询条件值，不会重置模板选择

### 数据管理
8. **数据持久化**: 搜索模板和条件设置会保存在 sessionStorage 和服务器端
9. **数据格式兼容**: 组件自动处理新旧数据格式的转换
10. **唯一标识**: 确保 `gridId` 在整个应用中唯一，避免数据冲突

### 性能优化
11. **大量条件**: 当搜索条件很多时，建议设置 `maxRows` 限制显示行数
12. **拖拽排序**: 搜索条件支持拖拽调整显示顺序
13. **初始化时序**: 组件会自动处理模板数据加载和搜索条件初始化的时序问题

## 样式定制

组件提供了丰富的 CSS 类名，可以通过覆盖样式进行定制：

```scss
.collapse-search-container {
  // 操作按钮栏
  .action-bar {
    .left-actions {
      // 左侧操作区域
    }

    .right-actions {
      // 右侧按钮区域

      .action-btn {
        // 操作按钮样式
      }
    }
  }

  // 搜索区域
  .search-area {
    .search-conditions-grid {
      // 搜索条件网格布局
      grid-template-columns: repeat(5, 1fr); // 一行5个

      // 自定义响应式断点
      @media (max-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}
```

## 🚀 主要改进

### v2.1.0 最新改进

#### 🎨 界面优化
- **展开时显示高级功能**: 搜索模板和条件设置功能只在展开状态下可见，保持界面简洁
- **智能重置**: 重置功能只重置查询条件值，保持模板选择和条件配置不变
- **优化初始化时序**: 确保模板数据加载完成后再初始化搜索条件状态

#### 📋 模板管理增强
- **默认模板优先**: 页面刷新时优先应用默认模板，提供一致的用户体验
- **模板优先级**: 默认模板 > 当前模板 > 非模板状态 的清晰优先级
- **数据格式兼容**: 自动处理新旧模板数据格式的转换和兼容

#### 🔧 技术改进
- **数据格式统一**: 统一使用字段名数组格式存储 `visibleConditions`
- **兼容性处理**: 自动检测和转换对象数组格式为标准字段名数组
- **错误处理**: 完善的错误处理和降级机制

### 历史改进

#### 🎯 布局优化
- **智能网格**: 使用 CSS Grid 实现一行5个搜索条件的布局
- **响应式设计**: 不同屏幕尺寸自动调整列数
- **统一操作栏**: 所有操作按钮集中在同一行

#### 🔧 功能增强
- **下拉模板选择**: 优化搜索模板选择体验
- **条件设置**: 可视化设置搜索条件显示/隐藏
- **拖拽排序**: 支持拖拽调整搜索条件顺序

#### 💡 用户体验
- **直观操作**: 简化的界面和清晰的操作流程
- **即时反馈**: 流畅的动画和状态提示
- **数据持久**: 用户设置自动保存和恢复

## 🔄 更新日志

### v2.1.0 (2025-06-25)
#### 新增功能
- ✨ 搜索模板管理功能完善
- ✨ 默认模板优先级支持
- ✨ 展开时才显示高级功能

#### 功能优化
- 🎨 重置功能只重置查询条件，不重置模板选择
- 📊 优化数据格式兼容性，支持新旧格式自动转换
- ⚡ 改进初始化时序，确保模板数据加载完成后再初始化搜索条件

#### 技术改进
- 🏗️ 重构组件架构，提高可维护性
- 🔒 增强数据格式验证和错误处理
- 📝 完善组件文档和使用示例

### v2.0.0
- 🎉 全新的CollapseSearchNew组件
- 📱 响应式设计，支持展开/收起模式
- 🎨 全新的UI设计和交互体验
