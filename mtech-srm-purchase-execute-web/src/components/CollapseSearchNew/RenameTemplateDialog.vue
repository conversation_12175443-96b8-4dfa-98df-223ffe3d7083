<template>
  <mt-dialog
    ref="dialog"
    css-class="rename-template-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
    :height="250"
    :width="440"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formData" :rules="dialogRules" autocomplete="off">
        <mt-form-item prop="name" :label="$t('模板名')">
          <mt-input
            maxlength="16"
            v-model="formData.name"
            :show-clear-button="true"
            :disabled="false"
            :placeholder="$t('请输入新的模板名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import MtDialog from '@mtech-ui/dialog'
import { API } from '@mtech-common/http'

const saveUserMemory = '/lowcodeWeb/tenant/user-memory/save'

export default {
  components: {
    MtDialog
  },
  props: {
    dialogData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogTitle: this.$t('重命名搜索模板'),
      formData: { name: '' },
      dialogRules: {
        name: [
          { required: true, message: this.$t('请输入模板名称'), trigger: 'blur' },
          { min: 1, max: 16, message: this.$t('模板名称长度为1-16个字符'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.renameTemplate,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    // 设置当前模板名称
    if (this.dialogData.currentName) {
      this.formData.name = this.dialogData.currentName
    }
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$emit('handleRenameDialogShow', false)
    },
    renameTemplate() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 检查新名称是否与现有模板重复（排除当前模板）
          const existingNames = this.dialogData.templateNames.filter(
            (name) => name !== this.dialogData.currentName
          )

          if (existingNames.includes(this.formData.name)) {
            this.$toast({ content: this.$t('模板名不能重复'), type: 'warning' })
            return
          }

          // 如果名称没有变化，直接关闭
          if (this.formData.name === this.dialogData.currentName) {
            this.handleClose()
            return
          }

          // 获取当前网格内存数据
          let _gridInfo = JSON.parse(sessionStorage.getItem(this.dialogData.gridId) || '{}')
          let _gridMemory = _gridInfo.gridMemory || _gridInfo

          // 更新搜索模板名称
          if (_gridMemory?.searchTemplates?.length > 0) {
            const templateIndex = _gridMemory.searchTemplates.findIndex(
              (t) => t.templateName === this.dialogData.currentName
            )

            if (templateIndex !== -1) {
              _gridMemory.searchTemplates[templateIndex].templateName = this.formData.name

              // 如果是当前选中的模板，更新当前模板名称
              if (_gridMemory.currentSearchTemplate === this.dialogData.currentName) {
                _gridMemory.currentSearchTemplate = this.formData.name
              }

              // 如果是默认模板，更新默认模板名称
              if (_gridMemory.defaultTemplate === this.dialogData.currentName) {
                _gridMemory.defaultTemplate = this.formData.name
              }
            }
          }

          // 统一存储格式
          sessionStorage.setItem(
            this.dialogData.gridId,
            JSON.stringify({ gridMemory: _gridMemory })
          )

          // 调用API保存到服务器
          API.post(saveUserMemory, {
            gridId: this.dialogData.gridId,
            gridMemory: _gridMemory
          })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('重命名成功'), type: 'success' })
                this.$emit('confirmSuccess')
                this.handleClose()
              } else {
                this.$toast({ content: res.message || this.$t('重命名失败'), type: 'error' })
              }
            })
            .catch((error) => {
              console.error('重命名搜索模板失败:', error)
              this.$toast({ content: this.$t('重命名失败'), type: 'error' })
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rename-template-dialog {
  .dialog-content {
    padding: 20px;
    min-width: 400px;

    .mt-form-item {
      margin-bottom: 20px;

      .mt-input {
        width: 100%;
      }
    }
  }
}
</style>
