<template>
  <div class="test-page">
    <h2>搜索模板功能完整测试</h2>

    <!-- 搜索组件 -->
    <div class="test-section">
      <h3>搜索组件测试</h3>
      <collapse-search-new
        :expended="isExpended"
        :enable-search-template="true"
        :enable-search-condition-setting="true"
        :grid-id="'ea247607-9c16-428d-aad1-6c6bcbbb578a'"
        :default-search-templates="defaultTemplates"
        :available-conditions="availableConditions"
        :required-conditions="requiredConditions"
        :search-form-data="searchForm"
        @reset="handleReset"
        @search="handleSearch"
        @templateSearch="handleTemplateSearch"
        @getSearchConditions="handleGetSearchConditions"
        @applySearchConditions="handleApplySearchConditions"
        @restoreSearchFormData="handleRestoreSearchFormData"
        @conditionSettingChange="handleConditionSettingChange"
        @visible-conditions-change="handleVisibleConditionsChange"
      >
        <template #default="{ visibleConditions: currentVisibleConditions }">
          <mt-form :model="searchForm" class="search-conditions-grid">
            <mt-form-item
              v-for="condition in currentVisibleConditions"
              :key="condition.field"
              :label="condition.label"
            >
              <component
                :is="getComponentType(condition.type)"
                v-model="searchForm[condition.field]"
                v-bind="getComponentProps(condition)"
              />
            </mt-form-item>
          </mt-form>
        </template>
      </collapse-search-new>
    </div>

    <!-- 搜索表单详细展示 -->
    <div class="test-section">
      <h3>搜索表单详细展示</h3>
      <div class="form-detail">
        <div class="form-row">
          <div class="form-item">
            <label>物料编码：</label>
            <mt-input
              v-model="searchForm.itemCode"
              placeholder="请输入物料编码"
              :show-clear-button="true"
            />
          </div>
          <div class="form-item">
            <label>物料名称：</label>
            <mt-input
              v-model="searchForm.itemName"
              placeholder="请输入物料名称"
              :show-clear-button="true"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <label>供应商编码：</label>
            <mt-input
              v-model="searchForm.supplierCode"
              placeholder="请输入供应商编码"
              :show-clear-button="true"
            />
          </div>
          <div class="form-item">
            <label>供应商名称：</label>
            <mt-input
              v-model="searchForm.supplierName"
              placeholder="请输入供应商名称"
              :show-clear-button="true"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <label>采购组织：</label>
            <mt-select
              v-model="searchForm.purchaseOrg"
              :data-source="purchaseOrgOptions"
              :fields="{ text: 'text', value: 'value' }"
              placeholder="请选择采购组织"
              :show-clear-button="true"
            />
          </div>
          <div class="form-item">
            <label>状态：</label>
            <mt-select
              v-model="searchForm.status"
              :data-source="statusOptions"
              :fields="{ text: 'text', value: 'value' }"
              placeholder="请选择状态"
              :show-clear-button="true"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <label>创建日期：</label>
            <mt-date-picker
              v-model="searchForm.createDate"
              placeholder="请选择创建日期"
              :show-clear-button="true"
            />
          </div>
          <div class="form-item">
            <label>金额范围：</label>
            <div class="amount-range">
              <mt-numeric-textbox
                v-model="searchForm.amountFrom"
                placeholder="最小金额"
                :show-clear-button="true"
              />
              <span>-</span>
              <mt-numeric-textbox
                v-model="searchForm.amountTo"
                placeholder="最大金额"
                :show-clear-button="true"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试控制按钮 -->
    <div class="test-controls">
      <div class="control-group">
        <h4>基础操作</h4>
        <button @click="isExpended = !isExpended">
          {{ isExpended ? '收起' : '展开' }} 搜索区域
        </button>
        <button @click="fillTestData">填充测试数据</button>
        <button @click="clearFormData">清空表单数据</button>
      </div>

      <div class="control-group">
        <h4>模板操作</h4>
        <button @click="clearTemplates">清空所有模板</button>
        <button @click="addPresetTemplate">添加预设模板</button>
        <button @click="setDefaultTemplate">设置默认模板</button>
        <button @click="clearDefaultTemplate">清除默认模板</button>
      </div>

      <div class="control-group">
        <h4>条件设置测试</h4>
        <button @click="testConditionSettings">测试条件设置</button>
        <button @click="resetConditionSettings">重置条件设置</button>
        <button @click="showCurrentConditions">显示当前条件</button>
      </div>

      <div class="control-group">
        <h4>模板功能测试</h4>
        <button @click="testTemplateSwitch">测试模板切换</button>
        <button @click="testSaveTemplate">测试保存模板</button>
        <button @click="simulatePageRefresh">模拟页面刷新</button>
      </div>
    </div>

    <!-- 调试信息 -->
    <div class="test-info">
      <div class="info-section">
        <h4>组件状态：</h4>
        <div class="status-grid">
          <div class="status-item">
            <label>展开状态:</label>
            <span>{{ isExpended ? '展开' : '收起' }}</span>
          </div>
          <div class="status-item">
            <label>当前模板:</label>
            <span>{{ currentTemplate || '无' }}</span>
          </div>
          <div class="status-item">
            <label>默认模板:</label>
            <span>{{ defaultTemplate || '无' }}</span>
          </div>
          <div class="status-item">
            <label>总条件数量:</label>
            <span>{{ availableConditions.length }}</span>
          </div>
          <div class="status-item">
            <label>可见条件数量:</label>
            <span>{{ currentVisibleConditions.length }}</span>
          </div>
          <div class="status-item">
            <label>必需条件:</label>
            <span>{{ requiredConditions.join(', ') || '无' }}</span>
          </div>
        </div>
        <p>
          <strong>功能说明：</strong
          >点击"设置搜索条件"按钮可以控制搜索条件的显示/隐藏，必需条件不可隐藏
        </p>
      </div>

      <div class="info-section">
        <h4>当前可见搜索条件：</h4>
        <div class="conditions-display">
          <div
            v-for="(condition, index) in currentVisibleConditions"
            :key="condition.field"
            class="condition-item"
          >
            <span class="condition-index">{{ index + 1 }}.</span>
            <span class="condition-field">{{ condition.field }}</span>
            <span class="condition-label">{{ condition.label }}</span>
            <span class="condition-type">{{ condition.type }}</span>
          </div>
          <div v-if="currentVisibleConditions.length === 0" class="no-conditions">暂无可见条件</div>
        </div>
      </div>

      <div class="info-section">
        <h4>搜索表单数据：</h4>
        <pre>{{ JSON.stringify(searchForm, null, 2) }}</pre>
      </div>

      <div class="info-section">
        <h4>搜索模板列表：</h4>
        <div class="templates-display">
          <div v-if="defaultTemplates.length > 0">
            <h5>默认模板:</h5>
            <div
              v-for="template in defaultTemplates"
              :key="template.templateName"
              class="template-item"
            >
              <span class="template-name">{{ template.templateName }}</span>
              <span class="template-type">默认</span>
              <span class="template-conditions"
                >{{ Object.keys(template.searchConditions || {}).length }}个条件</span
              >
            </div>
          </div>
          <div v-if="userTemplates.length > 0">
            <h5>用户模板:</h5>
            <div
              v-for="template in userTemplates"
              :key="template.templateName"
              class="template-item"
            >
              <span class="template-name">{{ template.templateName }}</span>
              <span class="template-type">用户</span>
              <span class="template-conditions"
                >{{ Object.keys(template.searchConditions || {}).length }}个条件</span
              >
            </div>
          </div>
          <div v-if="defaultTemplates.length === 0 && userTemplates.length === 0">
            <span class="no-templates">暂无模板</span>
          </div>
        </div>
      </div>

      <div class="info-section">
        <h4>最后一次搜索条件：</h4>
        <pre>{{ JSON.stringify(lastSearchConditions, null, 2) }}</pre>
      </div>

      <div class="info-section">
        <h4>事件日志：</h4>
        <div class="event-log-header">
          <button @click="clearEventLogs" class="clear-logs-btn">清空日志</button>
          <span class="log-count">共 {{ eventLogs.length }} 条日志</span>
        </div>
        <div class="event-log">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
            <span class="log-data">{{ log.data }}</span>
          </div>
          <div v-if="eventLogs.length === 0" class="no-logs">暂无事件日志</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CollapseSearchNew from '@/components/CollapseSearchNew'

export default {
  name: 'CollapseSearchTest',
  components: {
    CollapseSearchNew
  },
  data() {
    return {
      isExpended: false,
      searchForm: {
        itemCode: '',
        itemName: '',
        supplierCode: '',
        supplierName: '',
        purchaseOrg: '',
        status: '',
        createDate: null,
        amountFrom: null,
        amountTo: null,
        productName: '',
        category: '',
        creator: '',
        warehouse: '',
        brand: '',
        // 测试兼容性字段（使用 prop 定义的字段）
        testPropField1: '',
        testPropField2: ''
      },

      lastSearchConditions: {},
      eventLogs: [],
      visibleConditions: [],
      currentVisibleConditions: [],
      currentTemplate: '',
      defaultTemplate: '',
      userTemplates: [],

      defaultTemplates: [
        {
          templateName: '热销产品',
          searchRule: {
            status: '1',
            category: 'electronics'
          },
          searchConditions: {
            status: '1',
            category: 'electronics',
            productName: '热销商品'
          },
          visibleConditions: ['productName', 'category', 'status'],
          conditionOrder: ['productName', 'category', 'status']
        },
        {
          templateName: '新品上架',
          searchRule: {
            status: '1',
            createDate: new Date()
          },
          searchConditions: {
            status: '1',
            createDate: new Date(),
            category: 'electronics'
          },
          visibleConditions: ['productName', 'category', 'status', 'createDate'],
          conditionOrder: ['productName', 'category', 'status', 'createDate']
        }
      ],

      availableConditions: [
        { field: 'itemCode', label: '物料编码', type: 'input' },
        { field: 'itemName', label: '物料名称', type: 'input' },
        { field: 'supplierCode', label: '供应商编码', type: 'input' },
        { field: 'supplierName', label: '供应商名称', type: 'input' },
        { field: 'purchaseOrg', label: '采购组织', type: 'select', options: 'purchaseOrgOptions' },
        { field: 'status', label: '状态', type: 'select', options: 'statusOptions' },
        { field: 'createDate', label: '创建日期', type: 'date' },
        { field: 'amountFrom', label: '最小金额', type: 'number' },
        { field: 'amountTo', label: '最大金额', type: 'number' },
        { field: 'productName', label: '产品名称', type: 'input' },
        { field: 'category', label: '产品类别', type: 'select', options: 'categoryOptions' },
        { field: 'creator', label: '创建人', type: 'input' },
        { field: 'warehouse', label: '仓库', type: 'input' },
        { field: 'brand', label: '品牌', type: 'input' },
        // 测试兼容性：使用 prop 字段而不是 field
        { prop: 'testPropField1', label: '测试字段1(prop)', type: 'input' },
        {
          prop: 'testPropField2',
          label: '测试字段2(prop)',
          type: 'select',
          options: 'statusOptions'
        }
      ],

      // 不可隐藏的搜索条件
      requiredConditions: ['itemCode', 'supplierName'],

      purchaseOrgOptions: [
        { text: '采购组织A', value: 'ORG_A' },
        { text: '采购组织B', value: 'ORG_B' },
        { text: '采购组织C', value: 'ORG_C' }
      ],

      categoryOptions: [
        { text: '电子产品', value: 'electronics' },
        { text: '服装鞋帽', value: 'clothing' },
        { text: '家居用品', value: 'home' }
      ],

      statusOptions: [
        { text: '启用', value: '1' },
        { text: '禁用', value: '0' },
        { text: '草稿', value: 'DRAFT' },
        { text: '已提交', value: 'SUBMITTED' },
        { text: '已审批', value: 'APPROVED' }
      ]
    }
  },
  methods: {
    // 重置搜索条件
    handleReset() {
      this.addEventLog('reset', '重置搜索条件')
      Object.keys(this.searchForm).forEach((key) => {
        if (typeof this.searchForm[key] === 'string') {
          this.searchForm[key] = ''
        } else {
          this.searchForm[key] = null
        }
      })
      this.lastSearchConditions = {}
    },

    // 执行搜索
    handleSearch() {
      this.addEventLog('search', '手动执行搜索')
      this.lastSearchConditions = { ...this.searchForm }
      console.log('执行搜索:', this.searchForm)
    },

    // 模板搜索（兼容旧版本）
    handleTemplateSearch(searchRule) {
      this.addEventLog('templateSearch', `模板搜索: ${JSON.stringify(searchRule)}`)
      this.handleApplySearchConditions(searchRule)
    },

    // 获取当前搜索条件（保存模板时调用）
    handleGetSearchConditions(callback) {
      this.addEventLog('getSearchConditions', '获取搜索条件')

      // 过滤掉空值
      const conditions = Object.keys(this.searchForm).reduce((acc, key) => {
        const value = this.searchForm[key]
        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value
        }
        return acc
      }, {})

      this.addEventLog('getSearchConditions', `返回条件: ${JSON.stringify(conditions)}`)
      callback(conditions)
    },

    // 应用搜索条件（切换模板时调用）
    handleApplySearchConditions(conditions) {
      this.addEventLog('applySearchConditions', `应用搜索条件: ${JSON.stringify(conditions)}`)
      this.lastSearchConditions = conditions
      console.log('应用搜索条件:', conditions)
    },

    // 恢复搜索表单数据（新增功能）
    handleRestoreSearchFormData(conditions) {
      this.addEventLog('restoreSearchFormData', `恢复表单数据: ${JSON.stringify(conditions)}`)

      // 重置搜索表单
      Object.keys(this.searchForm).forEach((key) => {
        if (typeof this.searchForm[key] === 'string') {
          this.searchForm[key] = ''
        } else {
          this.searchForm[key] = null
        }
      })

      // 恢复保存的搜索条件值
      Object.keys(conditions).forEach((key) => {
        if (this.searchForm.hasOwnProperty(key)) {
          this.searchForm[key] = conditions[key]
        }
      })
    },

    // 搜索条件设置变化
    handleConditionSettingChange(result) {
      this.addEventLog('conditionSettingChange', `搜索条件设置变化: ${JSON.stringify(result)}`)
      this.visibleConditions = result.visibleConditions
      console.log('搜索条件设置变化:', result)
    },

    // 可见搜索条件变化
    handleVisibleConditionsChange(conditions) {
      this.addEventLog('visibleConditionsChange', `可见搜索条件变化: ${JSON.stringify(conditions)}`)
      console.log('可见搜索条件发生变化:', conditions)
    },

    // 获取组件类型
    getComponentType(type) {
      const typeMap = {
        input: 'mt-input',
        select: 'mt-select',
        number: 'mt-input-number',
        date: 'mt-date-picker',
        dateRange: 'mt-date-range-picker'
      }
      return typeMap[type] || 'mt-input'
    },

    // 获取组件属性
    getComponentProps(condition) {
      const props = {
        placeholder: `请输入${condition.label}`,
        showClearButton: true
      }

      if (condition.type === 'select' && condition.options) {
        props.dataSource = this[condition.options]
        props.fields = { text: 'text', value: 'value' }
        props.placeholder = `请选择${condition.label}`
      }

      if (condition.type === 'number') {
        props.showSpinButton = false
      }

      if (condition.type === 'dateRange') {
        props.placeholder = `请选择${condition.label}范围`
      }

      return props
    },

    // 填充测试数据
    fillTestData() {
      this.addEventLog('fillTestData', '填充测试数据')
      this.searchForm = {
        itemCode: 'ITEM001',
        itemName: '测试物料',
        supplierCode: 'SUP001',
        supplierName: '测试供应商',
        purchaseOrg: 'ORG_A',
        status: 'APPROVED',
        createDate: new Date(),
        amountFrom: 1000,
        amountTo: 5000,
        productName: '测试产品',
        category: 'electronics',
        creator: '张三',
        warehouse: '仓库A',
        brand: '品牌A',
        // 测试兼容性字段
        testPropField1: '测试prop字段1',
        testPropField2: 'APPROVED'
      }
    },

    // 清空表单数据
    clearFormData() {
      this.addEventLog('clearFormData', '清空表单数据')
      this.handleReset()
    },

    // 清空所有模板
    clearTemplates() {
      this.addEventLog('clearTemplates', '清空所有模板')
      sessionStorage.removeItem('test-search-template-grid')
      console.log('已清空模板数据')
    },

    // 添加预设模板
    addPresetTemplate() {
      this.addEventLog('addPresetTemplate', '添加预设模板')
      // 模拟添加一个预设模板到 sessionStorage
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')

      const presetTemplate = {
        templateName: `预设模板_${Date.now()}`,
        searchRule: {
          itemCode: 'PRESET001',
          status: '1'
        },
        searchConditions: {
          itemCode: 'PRESET001',
          itemName: '预设物料',
          status: '1',
          purchaseOrg: 'ORG_A'
        },
        visibleConditions: ['itemCode', 'itemName', 'status', 'purchaseOrg'],
        conditionOrder: ['itemCode', 'itemName', 'status', 'purchaseOrg']
      }

      if (!gridMemory.searchTemplates) {
        gridMemory.searchTemplates = []
      }
      gridMemory.searchTemplates.push(presetTemplate)

      sessionStorage.setItem('test-search-template-grid', JSON.stringify(gridMemory))
      console.log('已添加预设模板:', presetTemplate)
    },

    // 设置默认模板
    setDefaultTemplate() {
      this.addEventLog('setDefaultTemplate', '设置默认模板')
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')

      if (this.defaultTemplates.length > 0) {
        gridMemory.defaultTemplate = this.defaultTemplates[0].templateName
        this.defaultTemplate = this.defaultTemplates[0].templateName
        sessionStorage.setItem('test-search-template-grid', JSON.stringify(gridMemory))
        console.log('已设置默认模板:', this.defaultTemplates[0].templateName)
      }
    },

    // 清除默认模板
    clearDefaultTemplate() {
      this.addEventLog('clearDefaultTemplate', '清除默认模板')
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')
      delete gridMemory.defaultTemplate
      this.defaultTemplate = ''
      sessionStorage.setItem('test-search-template-grid', JSON.stringify(gridMemory))
      console.log('已清除默认模板')
    },

    // 测试条件设置
    testConditionSettings() {
      this.addEventLog('testConditionSettings', '测试条件设置功能')
      console.log('当前可见条件:', this.currentVisibleConditions)
      console.log('可用条件:', this.availableConditions)
    },

    // 重置条件设置
    resetConditionSettings() {
      this.addEventLog('resetConditionSettings', '重置条件设置')
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')
      delete gridMemory.visibleConditions
      delete gridMemory.conditionOrder
      delete gridMemory.userSelectedConditions
      sessionStorage.setItem('test-search-template-grid', JSON.stringify(gridMemory))
      console.log('已重置条件设置')
    },

    // 显示当前条件
    showCurrentConditions() {
      this.addEventLog('showCurrentConditions', '显示当前条件信息')
      console.log('当前可见条件:', this.currentVisibleConditions)
      console.log('当前表单数据:', this.searchForm)
    },

    // 测试模板切换
    testTemplateSwitch() {
      this.addEventLog('testTemplateSwitch', '测试模板切换功能')
      if (this.defaultTemplates.length > 0) {
        this.currentTemplate = this.defaultTemplates[0].templateName
        console.log('切换到模板:', this.defaultTemplates[0].templateName)
      }
    },

    // 测试保存模板
    testSaveTemplate() {
      this.addEventLog('testSaveTemplate', '测试保存模板功能')
      this.fillTestData()
      console.log('已填充测试数据，可以尝试保存为模板')
    },

    // 模拟页面刷新
    simulatePageRefresh() {
      this.addEventLog('simulatePageRefresh', '模拟页面刷新')
      // 模拟页面刷新后的状态恢复
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')
      if (gridMemory.defaultTemplate) {
        this.defaultTemplate = gridMemory.defaultTemplate
        console.log('页面刷新后恢复默认模板:', gridMemory.defaultTemplate)
      }
      if (gridMemory.currentSearchTemplate) {
        this.currentTemplate = gridMemory.currentSearchTemplate
        console.log('页面刷新后恢复当前模板:', gridMemory.currentSearchTemplate)
      }
    },

    // 清空事件日志
    clearEventLogs() {
      this.eventLogs = []
      console.log('已清空事件日志')
    },

    // 设置默认模板
    setDefaultTemplate() {
      this.addEventLog('setDefaultTemplate', '设置默认模板')
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')

      if (this.defaultTemplates.length > 0) {
        gridMemory.defaultTemplate = this.defaultTemplates[0].templateName
        this.defaultTemplate = this.defaultTemplates[0].templateName
        sessionStorage.setItem('test-search-template-grid', JSON.stringify(gridMemory))
        console.log('已设置默认模板:', this.defaultTemplates[0].templateName)
      }
    },

    // 清除默认模板
    clearDefaultTemplate() {
      this.addEventLog('clearDefaultTemplate', '清除默认模板')
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')
      delete gridMemory.defaultTemplate
      this.defaultTemplate = ''
      sessionStorage.setItem('test-search-template-grid', JSON.stringify(gridMemory))
      console.log('已清除默认模板')
    },

    // 测试条件设置
    testConditionSettings() {
      this.addEventLog('testConditionSettings', '测试条件设置功能')
      console.log('当前可见条件:', this.currentVisibleConditions)
      console.log('可用条件:', this.availableConditions)
    },

    // 重置条件设置
    resetConditionSettings() {
      this.addEventLog('resetConditionSettings', '重置条件设置')
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')
      delete gridMemory.visibleConditions
      delete gridMemory.conditionOrder
      delete gridMemory.userSelectedConditions
      sessionStorage.setItem('test-search-template-grid', JSON.stringify(gridMemory))
      console.log('已重置条件设置')
    },

    // 显示当前条件
    showCurrentConditions() {
      this.addEventLog('showCurrentConditions', '显示当前条件信息')
      console.log('当前可见条件:', this.currentVisibleConditions)
      console.log('当前表单数据:', this.searchForm)
    },

    // 测试模板切换
    testTemplateSwitch() {
      this.addEventLog('testTemplateSwitch', '测试模板切换功能')
      if (this.defaultTemplates.length > 0) {
        this.currentTemplate = this.defaultTemplates[0].templateName
        console.log('切换到模板:', this.defaultTemplates[0].templateName)
      }
    },

    // 测试保存模板
    testSaveTemplate() {
      this.addEventLog('testSaveTemplate', '测试保存模板功能')
      this.fillTestData()
      console.log('已填充测试数据，可以尝试保存为模板')
    },

    // 模拟页面刷新
    simulatePageRefresh() {
      this.addEventLog('simulatePageRefresh', '模拟页面刷新')
      // 模拟页面刷新后的状态恢复
      const gridMemory = JSON.parse(sessionStorage.getItem('test-search-template-grid') || '{}')
      if (gridMemory.defaultTemplate) {
        this.defaultTemplate = gridMemory.defaultTemplate
        console.log('页面刷新后恢复默认模板:', gridMemory.defaultTemplate)
      }
      if (gridMemory.currentSearchTemplate) {
        this.currentTemplate = gridMemory.currentSearchTemplate
        console.log('页面刷新后恢复当前模板:', gridMemory.currentSearchTemplate)
      }
    },

    // 清空事件日志
    clearEventLogs() {
      this.eventLogs = []
      console.log('已清空事件日志')
    },

    // 添加事件日志
    addEventLog(event, data) {
      const time = new Date().toLocaleTimeString()
      this.eventLogs.unshift({
        time,
        event,
        data: typeof data === 'string' ? data : JSON.stringify(data)
      })

      // 限制日志数量
      if (this.eventLogs.length > 50) {
        this.eventLogs = this.eventLogs.slice(0, 50)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  h2,
  h3 {
    color: #333;
    margin-bottom: 20px;
  }

  .test-section {
    margin-bottom: 30px;
    border: 1px solid #e9e9e9;
    border-radius: 8px;
    padding: 20px;
    background: #fff;

    .form-detail {
      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 16px;

        .form-item {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 8px;

          label {
            min-width: 100px;
            font-weight: 500;
            color: #333;
          }

          .amount-range {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;

            span {
              color: #666;
            }
          }
        }
      }
    }
  }

  .test-controls {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .control-group {
      border: 1px solid #e9e9e9;
      border-radius: 6px;
      padding: 15px;
      background: #fafafa;

      h4 {
        margin: 0 0 10px 0;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: center;

      button {
        padding: 6px 12px;
        border: 1px solid #2783fe;
        background: #fff;
        color: #2783fe;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s ease;

        &:hover {
          background: #2783fe;
          color: #fff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(39, 131, 254, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  .test-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;

    .info-section {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
        border-bottom: 1px solid #e9e9e9;
        padding-bottom: 8px;
      }

      p {
        margin: 6px 0;
        color: #666;
        font-size: 14px;
      }

      pre {
        background: #fff;
        padding: 12px;
        border-radius: 4px;
        border: 1px solid #e9e9e9;
        font-size: 12px;
        overflow-x: auto;
        max-height: 200px;
        overflow-y: auto;
      }

      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin-bottom: 10px;

        .status-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #e9e9e9;

          label {
            font-weight: 500;
            color: #666;
            min-width: 80px;
          }

          span {
            color: #333;
            font-weight: 500;
          }
        }
      }

      .conditions-display {
        .condition-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 8px;
          margin-bottom: 4px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #e9e9e9;

          .condition-index {
            font-weight: bold;
            color: #2783fe;
            min-width: 20px;
          }

          .condition-field {
            font-family: monospace;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            min-width: 120px;
          }

          .condition-label {
            color: #333;
            font-weight: 500;
            min-width: 100px;
          }

          .condition-type {
            color: #666;
            font-size: 12px;
            background: #e9e9e9;
            padding: 2px 6px;
            border-radius: 3px;
          }
        }

        .no-conditions {
          color: #999;
          font-style: italic;
          text-align: center;
          padding: 20px;
        }
      }

      .templates-display {
        h5 {
          margin: 10px 0 5px 0;
          color: #666;
          font-size: 14px;
        }

        .template-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 8px;
          margin-bottom: 4px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #e9e9e9;

          .template-name {
            font-weight: 500;
            color: #333;
            min-width: 120px;
          }

          .template-type {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;

            // &:contains('默认') {
            //   background: #e6f7ff;
            //   color: #1890ff;
            // }

            // &:contains('用户') {
            //   background: #f6ffed;
            //   color: #52c41a;
            // }
          }

          .template-conditions {
            color: #666;
            font-size: 12px;
          }
        }

        .no-templates {
          color: #999;
          font-style: italic;
          text-align: center;
          padding: 20px;
        }
      }

      .event-log-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .clear-logs-btn {
          padding: 4px 8px;
          border: 1px solid #ff4d4f;
          background: #fff;
          color: #ff4d4f;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.3s ease;

          &:hover {
            background: #ff4d4f;
            color: #fff;
          }
        }

        .log-count {
          color: #666;
          font-size: 12px;
        }
      }
    }

    .event-log {
      max-height: 300px;
      overflow-y: auto;
      background: #fff;
      border: 1px solid #e9e9e9;
      border-radius: 4px;

      .log-item {
        display: flex;
        padding: 8px 12px;
        border-bottom: 1px solid #f5f5f5;
        font-size: 12px;

        &:last-child {
          border-bottom: none;
        }

        .log-time {
          min-width: 80px;
          color: #999;
          font-family: monospace;
        }

        .log-event {
          min-width: 150px;
          color: #2783fe;
          font-weight: 500;
        }

        .log-data {
          flex: 1;
          color: #666;
          word-break: break-all;
        }
      }

      .no-logs {
        padding: 20px;
        text-align: center;
        color: #999;
        font-style: italic;
      }
    }
  }
}

// 全局样式覆盖
::v-deep .search-conditions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;

  .mt-form-item {
    margin-bottom: 0;
  }
}
</style>
