<template>
  <mt-multi-select
    v-if="multiple === true"
    css-class="remote-select"
    ref="multiSelectRef"
    v-model="value"
    v-bind="$attrs"
    v-on="$listeners"
    :show-clear-button="clearable"
    :data-source="options"
    :fields="{ text: 'label', value: fields.value }"
    :placeholder="placeholder"
    :allow-filtering="true"
    :filtering="filterMethodThrottle"
    filter-type="Contains"
    :show-drop-down-icon="true"
    :popup-width="popupWidth"
    @select="select"
    @removed="removed"
    @open="open"
    @close="close"
  ></mt-multi-select>
  <mt-select
    v-else
    ref="selectRef"
    css-class="remote-select"
    v-model="value"
    :show-clear-button="clearable"
    :data-source="options"
    :fields="{ text: 'label', value: fields.value }"
    :placeholder="placeholder"
    :allow-filtering="true"
    :filtering="filterMethodThrottle"
    filter-type="Contains"
    @open="open"
    @close="close"
    v-bind="$attrs"
    v-on="$listeners"
  >
  </mt-select>
</template>

<script>
import Vue from 'vue'
import { API } from '@mtech-common/http'
import { CheckBoxPlugin } from '@syncfusion/ej2-vue-buttons'
import { cloneDeep, debounce } from 'lodash'

Vue.use(CheckBoxPlugin)

const SELECTALL_LIMIT = 100
const DEBOUNCE_TIME = 500 // 防抖时间500ms
const REQUEST_INTERVAL = 300 // 请求间隔时间300ms
const CACHE_EXPIRY = 5 * 60 * 1000 // 缓存过期时间5分钟

export default {
  name: 'RemoteAutocomplete',
  model: {
    prop: 'modelVal',
    event: 'syncChange'
  },
  props: {
    modelVal: {
      type: [String, Number, Array],
      default: null
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 数据源匹配对象
    fields: {
      type: Object,
      default: () => {
        return {
          text: 'text',
          value: 'value'
        }
      }
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 请求url
    url: {
      type: String,
      default: ''
    },
    // 请求方法
    method: {
      type: String,
      default: 'post'
    },
    // 请求传入的参数
    params: {
      type: [Object, Array],
      default: () => null
    },
    // 输入框查询的key值
    paramsKey: {
      type: String,
      default: 'fuzzyParam'
    },
    // 通过rules查询时候的查询字段
    searchFields: {
      type: Array,
      default: () => null
    },
    popupWidth: {
      type: String,
      default: '100%'
    },
    recordsPosition: {
      type: String,
      default: 'data.records'
    },
    // 额外的rules params
    ruleParams: {
      type: Array,
      default: () => {
        return []
      }
    },
    placeholder: {
      type: String,
      default: null
    },
    clearable: {
      type: Boolean,
      default: true
    },
    dataLimit: {
      // 查询条数
      type: Number,
      default: 20
    },
    loadData: {
      type: Boolean,
      default: false
    },
    // 是否拼接value-label
    isGenerate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      options: [], // 数据源
      page: 1, //当前查询页数
      total: 0, //总条数
      checkedList: [], //多选缓存当前list
      isFill: false, //数据回填标识，初始value回填时判断
      observe: null,
      queryText: null, //当前下拉列表的查询条件
      isInitDataLoaded: false, // 初始下拉数据是否加载
      debounceInit: null,
      lastRequestTime: 0, // 上次请求时间
      isRequesting: false, // 是否正在请求中
      debouncedRemoteMethod: null, // 防抖后的远程方法
      dataCache: new Map(), // 数据缓存
      cacheTimestamps: new Map(), // 缓存时间戳
      scrollContainer: null, // 滚动容器
      lastScrollPosition: 0, // 记录上次滚动位置
      scrollHandler: null, // 滚动事件处理函数
      isLoadingMore: false, // 是否正在加载更多
      scrollDebounceTimer: null, // 滚动防抖定时器
      lastLoadedPage: 0, // 最后加载的页码
      uid: null // 组件唯一id
    }
  },
  computed: {
    headerTemplate() {
      const parentVm = this
      const headerVue = Vue.component('headerTemplate', {
        template: `<div class="select-all-container"><ejs-checkbox v-model="checked" :checked="checked" @change="toggleSelectAll" label='全选' /></div>`,
        data() {
          return {
            checked: false
          }
        },
        methods: {
          toggleSelectAll(e) {
            if (e.checked && parentVm.options?.length > SELECTALL_LIMIT) {
              this.$toast({
                content: '当前数据过多，无法全选，请输入更多内容以缩小数据范围',
                type: 'warning'
              })
              this.$nextTick(() => {
                this.checked = false
              })
              return
            }
            parentVm.$refs.multiSelectRef.ejsRef.selectAll(e.checked)
          }
        }
      })
      return function () {
        return {
          template: headerVue
        }
      }
    },
    value: {
      get() {
        return this.modelVal
      },
      set(val) {
        this.$emit('syncChange', val)
      }
    },
    // 防抖
    filterMethodThrottle() {
      let time = null
      if (this.dataSource.length !== 0) return null
      return (query) => {
        if (time) {
          clearTimeout(time)
        }
        time = setTimeout(() => {
          this.remoteMethod(query, 'filter')
          clearTimeout(time)
        }, 500)
      }
    }
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.options = this.arrSet([...val])
        }
      },
      immediate: true
    },
    options: {
      handler(val) {
        this.$emit('getOptions', val)
      }
    },
    /* 监听loadData的变化 */
    loadData: {
      handler(newValue) {
        if (newValue) {
          this.remoteMethod()
        }
      },
      immediate: true,
      deep: true
    },
    modelVal: {
      handler(newValue) {
        // 解决在mounted中拿不到初始化的数据，导致数据未匹配的情况
        if (newValue && !this.isInitDataLoaded) {
          this.debounceInit()
        }
      },
      immediate: true
    }
  },
  created() {
    // 生成唯一id
    this.uid = 'loadMore_' + Date.now() + Math.random().toString(36).substr(2, 9)
    // 创建防抖方法
    this.debouncedRemoteMethod = debounce(this.remoteMethod, DEBOUNCE_TIME)
  },
  mounted() {
    this.debounceInit = debounce(function () {
      this.init()
    }, 1000)
    this.debounceInit()
  },
  methods: {
    init() {
      // 初始modelValue非空，则回填select数据
      this.modelVal && (this.isFill = true)
      this.modelVal?.length === 0 && (this.isFill = false)
      if (this.dataSource.length === 0) this.remoteMethod(this.modelVal || '', 'init')
    },

    remoteMethod(e = { text: '' }, type) {
      console.log('开始远程加载数据，类型：', type, '页码：', this.page, '搜索文本：', e.text)

      // 修改搜索条件判断逻辑
      if (type === 'filter') {
        // 只在过滤模式下重置页码
        this.page = 1
        console.log('过滤模式，重置页码为1')
      }

      // 非url接口请求，直接赋值
      if (!this.url) {
        console.log('未配置URL，清空选项')
        this.options = []
        return
      }

      // 检查请求频率
      const now = Date.now()
      if (now - this.lastRequestTime < REQUEST_INTERVAL) {
        console.log('请求过于频繁，已跳过，距离上次请求：', now - this.lastRequestTime, 'ms')
        // 如果是加载更多，延迟执行
        if (type === 'add') {
          setTimeout(() => {
            this.remoteMethod(e, type)
          }, REQUEST_INTERVAL)
        }
        return
      }

      // 如果正在请求中，则返回
      if (this.isRequesting) {
        console.log('正在请求中，已跳过')
        return
      }

      // 检查缓存
      const cacheKey = `${e.text}_${this.page}`
      if (this.dataCache.has(cacheKey)) {
        const cacheData = this.dataCache.get(cacheKey)
        const cacheTime = this.cacheTimestamps.get(cacheKey)
        if (now - cacheTime < CACHE_EXPIRY) {
          console.log('使用缓存数据')
          this.handleResponse(cacheData, e, type)
          return
        }
      }

      this.lastRequestTime = now
      this.isRequesting = true

      const params = this.getParams(e)
      console.log('请求参数：', params)
      this.isFill = false

      API[this.method](this.url, params || {})
        .then((response) => {
          console.log('接口返回数据：', response)
          if (response.code === 200 && response.data) {
            // 更新缓存
            this.dataCache.set(cacheKey, response)
            this.cacheTimestamps.set(cacheKey, now)
            this.handleResponse(response, e, type)
          } else {
            console.warn('接口返回异常：', response)
            this.isRequesting = false
          }
        })
        .catch((error) => {
          console.error('请求失败：', error)
          this.isRequesting = false
        })
    },

    getUniqueKey(item) {
      return item?.id || item?.[this.fields.value]
    },

    generateLabel(data) {
      const textFields = this.fields.text.split('-')
      return textFields.length > 1
        ? `${data[textFields[0]]}-${data[textFields[1]]}`
        : `${data[this.fields.value]} - ${data[this.fields.text]}`
    },

    processDataItem(item) {
      return {
        ...item,
        label: this.isGenerate ? this.generateLabel(item) : item[this.fields.text]
      }
    },

    isDataExists(item, list) {
      const uniqueKey = this.getUniqueKey(item)
      return list.some((existingItem) => this.getUniqueKey(existingItem) === uniqueKey)
    },

    restoreDataFromCache(missingItems) {
      const restoredItems = []
      this.dataCache.forEach((cacheData) => {
        const cacheList = this.deepGet(cacheData, this.recordsPosition || 'data.records')
        if (cacheList) {
          cacheList.forEach((item) => {
            if (
              missingItems.some(
                (missingItem) => this.getUniqueKey(missingItem) === this.getUniqueKey(item)
              )
            ) {
              restoredItems.push(this.processDataItem(item))
            }
          })
        }
      })
      return restoredItems
    },

    handleResponse(response, e, type) {
      console.log('处理响应数据：', response, '类型：', type)
      const res = cloneDeep(response)
      this.isInitDataLoaded = true
      let _total = res.data?.total || 0
      let _option = []
      const _dataList = this.deepGet(res, this.recordsPosition || 'data.records')
      console.log('解析后的数据列表：', _dataList, '总数：', _total, '当前页码：', this.page)

      // 修改数据合并逻辑
      if (type === 'add') {
        // 追加模式：合并现有数据和新数据
        _option = [...this.options]
        if (_dataList?.length > 0) {
          console.log('开始合并数据，当前数据量：', _option.length)
          _dataList.forEach((item) => {
            if (!this.isDataExists(item, _option)) {
              _option.push(this.processDataItem(item))
              console.log('添加新数据项：', item)
            } else {
              console.log('跳过重复数据项：', item)
            }
          })
          console.log('合并后数据量：', _option.length)
        }
      } else {
        // 过滤模式：保留已选中的数据，添加新数据
        _option = [...this.checkedList]

        if (_dataList?.length > 0) {
          _dataList.forEach((item) => {
            if (!this.isDataExists(item, _option)) {
              _option.push(this.processDataItem(item))
              console.log('添加新数据项：', item)
            }
          })
        }

        // 确保已选中的数据都在列表中
        if (this.checkedList?.length > 0) {
          const missingSelectedItems = this.checkedList.filter(
            (checkedItem) => !this.isDataExists(checkedItem, _option)
          )

          if (missingSelectedItems.length > 0) {
            console.log('发现未在列表中的已选数据，尝试从缓存中恢复')
            const restoredItems = this.restoreDataFromCache(missingSelectedItems)
            _option.push(...restoredItems)
            console.log('从缓存恢复数据量：', restoredItems.length)
          }
        }

        console.log(
          '过滤后数据量：',
          _option.length,
          '已选中数据量：',
          this.checkedList?.length || 0
        )
      }

      this.total = _total
      this.options = _option
      console.log('更新后的选项列表：', {
        总数据量: this.options.length,
        总数: this.total,
        新数据条数: _dataList?.length,
        当前页码: this.page,
        已选中数据: this.checkedList
      })

      if (type === 'init') {
        this.checkedList = this.options.filter((i) => this.modelVal?.includes(i[this.fields.value]))
      }

      this.queryText = e.text

      this.$nextTick(() => {
        if (e.updateData && typeof e.updateData == 'function') {
          e.updateData(this.options)
        }
        if (type && type !== 'init') {
          this.checkAndUpdateLoadMore()
          // 恢复滚动位置
          if (type === 'add' && this.scrollContainer) {
            this.scrollContainer.scrollTop = this.lastScrollPosition
          }
        }
        this.isRequesting = false
      })
    },

    select(val) {
      if (!this.isDataExists(val?.itemData, this.checkedList)) {
        this.checkedList.push(val?.itemData)
        // 更新 modelVal
        const newValue = this.checkedList.map((item) => item[this.fields.value])
        this.$emit('syncChange', newValue)
        this.$emit('multiChange', this.checkedList)
      }
    },

    removed(val) {
      this.checkedList = this.checkedList.filter(
        (item) => this.getUniqueKey(item) !== this.getUniqueKey(val.itemData)
      )
      // 更新 modelVal
      const newValue = this.checkedList.map((item) => item[this.fields.value])
      this.$emit('syncChange', newValue)
      this.$emit('multiChange', this.checkedList)
    },

    checkAndUpdateLoadMore() {
      console.log('检查是否需要更新加载更多元素', {
        当前数据量: this.options.length,
        总数: this.total,
        数据限制: this.dataLimit,
        当前页码: this.page,
        最后加载页码: this.lastLoadedPage
      })

      // 如果当前数据量已经达到或超过总数，移除加载更多
      if (this.options.length >= this.total) {
        console.log('数据已加载完毕，移除加载更多元素')
        this.removeLoadMoreElement()
        return
      }

      // 如果数据量超过限制，添加加载更多
      if (Number(this.total) > this.dataLimit) {
        console.log('需要添加加载更多元素')
        this.$nextTick(() => {
          this.setObserveTarget()
        })
      } else if (this.options.length === 0) {
        // 如果没有数据，尝试加载第一页
        console.log('没有数据，尝试加载第一页')
        this.page = 1
        this.lastLoadedPage = 0
        this.remoteMethod({ text: this.queryText || '' }, 'init')
      }
    },

    deepGet(obj, keys, defaultVal) {
      return keys.split(/\./).reduce((o, j) => (o || {})[j], obj) || defaultVal
    },

    arrSet(arr) {
      const obj = {}
      return arr.reduce((setArr, item) => {
        const id = item.id
        const label = this.isGenerate ? this.generateLabel(item) : item[this.fields.text]

        if (!obj[id]) {
          obj[id] = true
          setArr.push({
            ...item,
            label
          })
        } else {
          const index = setArr.findIndex((i) => i.id === id)
          if (index !== -1) {
            setArr[index] = {
              ...setArr[index],
              ...item,
              label
            }
          }
        }
        return setArr
      }, [])
    },

    getParams(e) {
      // 1.通过rules查询
      if (this.searchFields) {
        let searchRules = this.getSearchRules(e.text)
        return {
          condition: 'and',
          page: { current: this.page, size: this.dataLimit },
          rules: [...searchRules]
        }
      }

      // 2.通过paramsKey查询数据
      return {
        ...this.params,
        page: { current: this.page, size: this.dataLimit },
        [this.paramsKey]: this.isFill ? this.modelVal : e.text
      }
    },

    getSearchRules(value) {
      const rules = []

      if (this.isFill) {
        rules.push({
          condition: 'or',
          label: '',
          field: this.fields.value,
          type: 'string',
          operator: this.multiple ? 'in' : 'contains',
          value: this.modelVal
        })
      } else if (this.searchFields) {
        this.searchFields.forEach((field) => {
          rules.push({
            condition: 'or',
            label: '',
            field,
            type: 'string',
            operator: 'contains',
            value: value || ''
          })
        })
      }

      return [
        {
          condition: 'and',
          rules: [...rules, ...this.ruleParams]
        }
      ]
    },

    getScrollContainer() {
      // 首先尝试查找已存在的容器
      const selectors = [
        '.e-multi-select-list-wrapper .e-content.e-dropdownbase',
        '.e-popup .e-content.e-dropdownbase',
        '.remote-select .e-multi-select-list-wrapper .e-content.e-dropdownbase',
        '.remote-select .e-popup .e-content.e-dropdownbase',
        '.e-popup .e-listbox .e-content',
        '.e-popup .e-listbox-wrapper .e-content'
      ]

      for (const selector of selectors) {
        const container = document.querySelector(selector)
        if (container) {
          console.log('找到滚动容器：', selector)
          return container
        }
      }

      // 如果上述选择器都没找到，尝试查找最近的滚动容器
      const dropdown = document.querySelector('.remote-select')
      if (dropdown) {
        const scrollContainer = dropdown.querySelector('.e-content')
        if (scrollContainer) {
          console.log('通过备用方法找到滚动容器')
          return scrollContainer
        }
      }

      // 尝试查找弹出层
      const popup = document.querySelector('.e-popup')
      if (popup) {
        const scrollContainer = popup.querySelector('.e-content')
        if (scrollContainer) {
          console.log('通过弹出层找到滚动容器')
          return scrollContainer
        }
      }

      console.warn('未找到滚动容器')
      return null
    },

    setObserveTarget() {
      console.log('设置滚动监听')
      if (!this.scrollContainer) {
        this.scrollContainer = this.getScrollContainer()
        if (!this.scrollContainer) {
          console.warn('未找到滚动容器')
          return
        }
      }

      this.removeLoadMoreElement()

      if (this.options.length >= this.total) {
        console.log('数据已加载完毕，不需要添加加载更多元素')
        return
      }

      // 创建加载更多元素
      const loadMoreEl = document.createElement('div')
      loadMoreEl.id = this.uid // 使用组件唯一id
      Object.assign(loadMoreEl.style, {
        textAlign: 'center',
        lineHeight: '30px',
        color: '#585e6b',
        margin: '0',
        padding: '5px 0',
        height: '30px',
        position: 'relative',
        zIndex: 1,
        backgroundColor: '#fff',
        width: '100%',
        display: 'block',
        borderTop: '1px solid #e8e8e8',
        minHeight: '30px',
        visibility: 'visible',
        opacity: '1'
      })
      loadMoreEl.textContent = this.isRequesting ? '加载中...' : '加载更多...'

      // 确保滚动容器有足够的高度
      if (this.scrollContainer.scrollHeight < this.scrollContainer.clientHeight) {
        this.scrollContainer.style.minHeight = '200px'
      }

      // 将加载更多元素添加到滚动容器的末尾，使用唯一标识符避免影响其他组件
      const currentContainer = this.scrollContainer.closest('.remote-select')
      if (currentContainer) {
        this.scrollContainer.appendChild(loadMoreEl)
        console.log('加载更多元素已添加到当前组件容器')
      } else {
        console.warn('未找到当前组件的容器，无法添加加载更多元素')
      }

      // 移除旧的滚动监听
      if (this.scrollHandler) {
        this.scrollContainer.removeEventListener('scroll', this.scrollHandler)
      }

      // 创建新的滚动监听
      this.scrollHandler = () => {
        // 清除之前的防抖定时器
        if (this.scrollDebounceTimer) {
          clearTimeout(this.scrollDebounceTimer)
        }

        // 设置新的防抖定时器
        this.scrollDebounceTimer = setTimeout(() => {
          if (this.isRequesting || this.isLoadingMore) {
            console.log('正在请求中或加载更多中，跳过滚动处理')
            return
          }

          const containerHeight = this.scrollContainer.clientHeight
          const scrollTop = this.scrollContainer.scrollTop
          const scrollHeight = this.scrollContainer.scrollHeight
          const threshold = 50 // 距离底部50px时触发加载

          console.log('滚动位置：', {
            scrollTop,
            scrollHeight,
            containerHeight,
            distanceToBottom: scrollHeight - scrollTop - containerHeight,
            当前页码: this.page,
            已加载数据量: this.options.length,
            总数据量: this.total,
            最后加载页码: this.lastLoadedPage
          })

          if (scrollHeight - scrollTop - containerHeight < threshold) {
            if (this.options.length >= this.total) {
              console.log('数据已加载完毕')
              this.removeLoadMoreElement()
              return
            }

            // 修改页码判断逻辑
            const nextPage = this.page + 1
            console.log('检查页码：', {
              当前页码: this.page,
              下一页: nextPage,
              最后加载页码: this.lastLoadedPage,
              是否已加载: nextPage <= this.lastLoadedPage
            })

            if (nextPage <= this.lastLoadedPage) {
              console.log('该页数据已加载，跳过')
              return
            }

            console.log('触发加载更多，页码：', nextPage)
            this.lastScrollPosition = scrollTop
            this.page = nextPage
            this.isLoadingMore = true
            this.lastLoadedPage = nextPage

            // 更新加载更多元素状态
            const loadMoreEl = document.getElementById(this.uid)
            if (loadMoreEl) {
              loadMoreEl.textContent = '加载中...'
            }

            // 使用 Promise 处理加载更多
            new Promise((resolve) => {
              this.remoteMethod({ text: this.queryText }, 'add')
              resolve()
            })
              .then(() => {
                // 加载完成后，自动滚动到新数据的位置
                this.$nextTick(() => {
                  if (this.scrollContainer) {
                    const newScrollHeight = this.scrollContainer.scrollHeight
                    const scrollDiff = newScrollHeight - scrollHeight
                    if (scrollDiff > 0) {
                      this.scrollContainer.scrollTop = scrollTop + scrollDiff
                    }
                  }
                  this.isLoadingMore = false
                  // 检查是否需要继续加载更多
                  this.checkAndUpdateLoadMore()
                })
              })
              .catch(() => {
                this.isLoadingMore = false
                // 恢复页码
                this.page = this.lastLoadedPage
                // 检查是否需要继续加载更多
                this.checkAndUpdateLoadMore()
              })
          }
        }, 200) // 200ms 的防抖时间
      }

      // 添加滚动监听
      this.scrollContainer.addEventListener('scroll', this.scrollHandler)
      console.log('滚动监听已添加')
    },

    initScroll() {
      console.log('开始初始化滚动加载')

      // 移除旧的滚动监听
      if (this.scrollHandler) {
        this.scrollContainer.removeEventListener('scroll', this.scrollHandler)
      }

      // 创建新的滚动监听
      this.scrollHandler = () => {
        if (this.scrollDebounceTimer) {
          clearTimeout(this.scrollDebounceTimer)
        }

        this.scrollDebounceTimer = setTimeout(() => {
          if (this.isRequesting || this.isLoadingMore) {
            return
          }

          const containerHeight = this.scrollContainer.clientHeight
          const scrollTop = this.scrollContainer.scrollTop
          const scrollHeight = this.scrollContainer.scrollHeight
          const threshold = 50

          if (scrollHeight - scrollTop - containerHeight < threshold) {
            this.handleLoadMore()
          }
        }, 200)
      }

      // 添加滚动监听
      this.scrollContainer.addEventListener('scroll', this.scrollHandler)
      console.log('滚动监听已添加')

      // 检查是否需要添加加载更多元素
      this.checkAndUpdateLoadMore()

      // 确保滚动容器有足够的高度
      if (this.scrollContainer.scrollHeight < this.scrollContainer.clientHeight) {
        this.scrollContainer.style.minHeight = '200px'
      }
    },

    open() {
      console.log('下拉框开始打开')
      // 重置状态
      this.lastLoadedPage = 0
      this.isLoadingMore = false
      this.isRequesting = false
      this.page = 1 // 重置页码

      // 使用 MutationObserver 监听弹框的添加
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.addedNodes.length) {
            const popup = document.querySelector('.e-popup')
            if (popup) {
              console.log('检测到弹框已添加')
              observer.disconnect()
              this.initScrollWithRetry()
              break
            }
          }
        }
      })

      // 开始观察文档变化
      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 设置超时，防止无限等待
      setTimeout(() => {
        observer.disconnect()
        this.initScrollWithRetry()
      }, 1000)

      // 设置输入框监听
      this.$nextTick(() => {
        const inputEl = document.querySelector(
          '.remote-select.e-multi-select-list-wrapper .e-input-filter, .remote-select .e-input-filter'
        )

        if (inputEl) {
          const handleInput = (event) => {
            console.log('搜索输入：', event.target.value)
            this.page = 1
            this.lastScrollPosition = 0
            this.lastLoadedPage = 0

            const selectedItems = [...this.checkedList]
            this.options = selectedItems

            this.remoteMethod({ text: event.target.value }, 'filter')
            this.queryText = event.target.value
          }

          inputEl.removeEventListener('input', handleInput)
          inputEl.addEventListener('input', handleInput)
          this._inputHandler = handleInput
        }

        // 立即加载初始数据
        if (!this.isInitDataLoaded) {
          console.log('首次加载数据')
          this.remoteMethod({ text: '' }, 'init')
        } else {
          console.log('重新检查加载更多状态')
          this.checkAndUpdateLoadMore()
        }
      })
    },

    initScrollWithRetry(retryCount = 0) {
      console.log('尝试初始化滚动加载，第', retryCount + 1, '次')

      if (retryCount >= 5) {
        console.warn('达到最大重试次数，放弃初始化滚动加载')
        return
      }

      this.scrollContainer = this.getScrollContainer()

      if (!this.scrollContainer) {
        console.log('未找到滚动容器，将在100ms后重试')
        setTimeout(() => {
          this.initScrollWithRetry(retryCount + 1)
        }, 100)
        return
      }

      console.log('成功找到滚动容器，开始设置监听')
      this.initScroll()
    },

    close() {
      // 移除滚动监听
      if (this.scrollContainer && this.scrollHandler) {
        this.scrollContainer.removeEventListener('scroll', this.scrollHandler)
        this.scrollHandler = null
      }

      // 清除防抖定时器
      if (this.scrollDebounceTimer) {
        clearTimeout(this.scrollDebounceTimer)
        this.scrollDebounceTimer = null
      }

      this.removeLoadMoreElement()
      this.page = 1 // 重置页码
      this.lastScrollPosition = 0 // 重置滚动位置
      this.scrollContainer = null // 清空滚动容器引用
    },

    removeLoadMoreElement() {
      const loadMoreEl = document.getElementById(this.uid)
      if (loadMoreEl) {
        console.log('移除加载更多元素')
        loadMoreEl.remove()
      }
    }
  }
}
</script>

<style lang="scss">
.remote-select {
  .select-all-container {
    height: 32px;
    display: flex;
    align-items: center;
    padding-left: 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
  }

  .e-multi-select-list-wrapper {
    .e-input-filter {
      padding: 8px;
      border-bottom: 1px solid #e8e8e8;
    }
  }

  #loadMore {
    cursor: pointer;
    transition: background-color 0.3s;
    &:hover {
      background-color: #f5f5f5;
    }
  }
}
</style>
