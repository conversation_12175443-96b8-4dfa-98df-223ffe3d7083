const Router = [
  // 委外协同

  // 供方
  {
    path: 'supplier-picking', // 供方-领料管理
    name: 'supplier-picking',
    component: () => import('@/views/outsourcing/supplier/picking/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supplier-add-picking', // 供方-创建领料单
    name: 'supplier-add-picking',
    component: () => import('@/views/outsourcing/supplier/addPicking/index.vue')
  },
  // {
  //   path: "supplier-detail-picking", // 供方-领料单详情页
  //   name: "supplier-detail-picking",
  //   component: () =>
  //     import("@/views/outsourcing/supplier/addDetailPicking/index.vue"),
  // },
  {
    path: 'supplier-add-cancel-picking', // 供方-创建委外退货单
    name: 'supplier-add-cancel-picking',
    component: () => import('@/views/outsourcing/supplier/addCancelPicking/index.vue')
  },
  {
    path: 'supplier-cancel-picking-work', // 供方-退货管理-加工方
    name: 'supplier-cancel-picking-work',
    component: () => import('@/views/outsourcing/supplier/cancelPickingWork/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supplier-cancel-pickingDetail-work', // 供方-退货管理-委外退货详情
    name: 'supplier-cancel-pickingDetail-work',
    component: () => import('@/views/outsourcing/supplier/cancelPickingDetailWork/index.vue')
  },
  {
    path: 'supplier-cancel-picking-raw', // 供方-退货管理-原材料方
    name: 'supplier-cancel-picking-raw',
    component: () => import('@/views/outsourcing/supplier/cancelPickingRaw/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'supplier-cancel-pickingDetail-raw', // 供方-退货管理-委外退货详情-原材料方
    name: 'supplier-cancel-pickingDetail-raw',
    component: () => import('@/views/outsourcing/supplier/cancelPickingDetailRaw/index.vue')
  },
  {
    path: 'supplier-transfers', // 供方-调拨管理
    name: 'supplier-transfers',
    component: () => import('@/views/outsourcing/supplier/transfers/index.vue')
  },
  {
    path: 'supplier-add-transfers', // 供方-创建调拨管理
    name: 'supplier-add-transfers',
    component: () => import('@/views/outsourcing/supplier/addTransfers/index.vue')
  },
  {
    path: 'supplier-transfers-Detail', // 供方-调拨管理详情
    name: 'supplier-transfers-Detail',
    component: () => import('@/views/outsourcing/supplier/transfersDetail/index.vue')
  },
  {
    path: 'supplier-transfers-v2', // 供方-调拨管理
    name: 'supplier-transfers-v2',
    component: () => import('@/views/outsourcing/supplier/transfersV2/index.vue')
  },
  {
    path: 'supplier-add-transfers-v2', // 供方-创建调拨管理
    name: 'supplier-add-transfers-v2',
    component: () => import('@/views/outsourcing/supplier/addTransfersV2/index.vue')
  },
  {
    path: 'supplier-transfers-detail-v2', // 供方-调拨管理详情
    name: 'supplier-transfers-detail-v2',
    component: () => import('@/views/outsourcing/supplier/transfersDetailV2/index.vue')
  },
  {
    path: 'supplier-transfersIn-Detail', // 供方-供应商调入详情
    name: 'supplier-transfersIn-Detail',
    component: () => import('@/views/outsourcing/supplier/transfersInDetail/index.vue')
  },
  {
    path: 'supplier-transfers-in', // 供方-调拨管理-调入
    name: 'supplier-transfers-in',
    component: () => import('@/views/outsourcing/supplier/transfersIn/index.vue')
  },
  //采方
  {
    path: 'purchase-detailPiking', // 采方-委外待领料明细
    name: 'purchase-detailPiking',
    component: () => import('@/views/outsourcing/purchase/detailPiking/index.vue') //
  },
  {
    path: 'purchase-transfers', // 采方-委外调拨
    name: 'purchase-transfers',
    component: () => import('@/views/outsourcing/purchase/purchaseTransfers/index.vue') //
  },
  {
    path: 'purchase-transfers-detail', // 采方-委外调拨详情
    name: 'purchase-transfers-detail',
    component: () => import('@/views/outsourcing/purchase/purchaseTransfersDetail/index.vue') //
  },
  {
    path: 'purchase-transfers-v2', // 采方-委外调拨-v2
    name: 'purchase-transfersv-v2',
    component: () => import('@/views/outsourcing/purchase/purchaseTransfersV2/index.vue') //
  },
  {
    path: 'purchase-transfers-detail-v2', // 采方-委外调拨详情-v2
    name: 'purchase-transfers-detail-v2',
    component: () => import('@/views/outsourcing/purchase/purchaseTransfersDetailV2/index.vue') //
  },
  {
    path: 'purchase-stay-detail', // 采方-委外待领料明细
    name: 'purchase-stay-detail',
    component: () => import('@/views/outsourcing/purchase/stayDetail/index.vue') // 暂未开发
  },

  {
    path: 'purchase-list-picking', // 采方-委外领料管理列表-头视图
    name: 'purchase-list-picking',
    component: () => import('@/views/outsourcing/purchase/purchaseListPicking/index.vue')
  },

  {
    path: 'purchase-detail-picking', // 采方-委外领料管理详情
    name: 'purchase-detail-picking',
    component: () => import('@/views/outsourcing/purchase/purchaseDetailPicking/index.vue')
  },
  {
    path: 'purchase-detail-toBePicked', // 采方-委外待领料明细
    name: 'purchase-detail-toBePicked',
    component: () => import('@/views/outsourcing/purchase/purchaseDetailToBePicked/index.vue')
  },
  {
    path: 'purchase-list-returnCargo', // 采方-委外退货管理 -头视图
    name: 'purchase-list-returnCargo',
    component: () => import('@/views/outsourcing/purchase/purchaseReturnCargo/index.vue')
  },

  {
    path: 'purchase-detail-returnCargo', // 采方-委外退货管理 -详情页
    name: 'purchase-detail-returnCargo',
    component: () => import('@/views/outsourcing/purchase/purchaseDetailReturnCargo/index.vue')
  },
  {
    path: 'purchase-pickquantity-config', // 采访 - 委外领料数量限制配置
    name: 'purchase-pickquantity-config',
    component: () => import('@/views/outsourcing/purchase/pickquantityConfig/index.vue')
  },

  // 8.19 二期 new
  {
    path: 'new-supplier-picking', // 供方-领料管理
    name: 'new-supplier-picking',
    component: () => import('@/views/outsourcingNew/supplier/picking/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-supplier-add-picking', // 供方-创建领料单
    name: 'new-supplier-add-picking',
    component: () => import('@/views/outsourcingNew/supplier/addPicking/index.vue')
  },
  {
    path: 'new-supplier-add-cancel-picking', // 供方-创建委外退货单
    name: 'new-supplier-add-cancel-picking',
    component: () => import('@/views/outsourcingNew/supplier/addCancelPicking/index.vue')
  },
  {
    path: 'new-supplier-add-cancel-picking-gc', // 供方-创建委外退货单
    name: 'new-supplier-add-cancel-picking-gc',
    component: () => import('@/views/outsourcingNew/supplier/addCancelPicking/index.vue')
  },
  {
    path: 'new-supplier-add-cancel-picking-gt', // 供方-创建委外退货单 gt
    name: 'new-supplier-add-cancel-picking-gt',
    component: () => import('@/views/outsourcingNew/supplier/addCancelPicking/index.vue')
  },
  {
    path: 'new-supplier-cancel-picking-work', // 供方-退货管理-加工方
    name: 'new-supplier-cancel-picking-work',
    component: () => import('@/views/outsourcingNew/supplier/cancelPickingWork/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-supplier-cancel-picking-work-gc', // 供方-退货管理-加工方 gc
    name: 'new-supplier-cancel-picking-work-gc',
    component: () => import('@/views/outsourcingNew/supplier/cancelPickingWork/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-supplier-cancel-pickingDetail-work', // 供方-退货管理-委外退货详情
    name: 'new-supplier-cancel-pickingDetail-work',
    component: () => import('@/views/outsourcingNew/supplier/cancelPickingDetailWork/index.vue')
  },
  {
    path: 'new-supplier-cancel-picking-raw', // 供方-退货管理-原材料方
    name: 'new-supplier-cancel-picking-raw',
    component: () => import('@/views/outsourcingNew/supplier/cancelPickingRaw/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-supplier-cancel-picking-raw-gc', // 供方-退货管理-原材料方 gc
    name: 'new-supplier-cancel-picking-raw-gc',
    component: () => import('@/views/outsourcingNew/supplier/cancelPickingRaw/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-supplier-cancel-pickingDetail-raw', // 供方-退货管理-委外退货详情-原材料方
    name: 'new-supplier-cancel-pickingDetail-raw',
    component: () => import('@/views/outsourcingNew/supplier/cancelPickingDetailRaw/index.vue')
  },
  {
    path: 'new-supplier-transfers', // 供方-调拨管理
    name: 'new-supplier-transfers',
    component: () => import('@/views/outsourcingNew/supplier/transfers/index.vue')
  },
  {
    path: 'new-supplier-transfers-gc', // 供方-调拨管理 gc
    name: 'new-supplier-transfers-gc',
    component: () => import('@/views/outsourcingNew/supplier/transfers/index.vue')
  },
  {
    path: 'new-supplier-add-transfers', // 供方-创建调拨管理
    name: 'new-supplier-add-transfers',
    component: () => import('@/views/outsourcingNew/supplier/addTransfers/index.vue')
  },
  {
    path: 'new-supplier-add-transfers-gc', // 供方-创建调拨管理 gc
    name: 'new-supplier-add-transfers-gc',
    component: () => import('@/views/outsourcingNew/supplier/addTransfers/index.vue')
  },
  {
    path: 'new-supplier-transfers-Detail', // 供方-调拨管理详情
    name: 'new-supplier-transfers-Detail',
    component: () => import('@/views/outsourcingNew/supplier/transfersDetail/index.vue')
  },
  {
    path: 'new-supplier-transfersIn-Detail', // 供方-供应商调入详情
    name: 'new-supplier-transfersIn-Detail',
    component: () => import('@/views/outsourcingNew/supplier/transfersInDetail/index.vue')
  },
  {
    path: 'new-supplier-transfers-in', // 供方-调拨管理-调入
    name: 'new-supplier-transfers-in',
    component: () => import('@/views/outsourcingNew/supplier/transfersIn/index.vue')
  },
  {
    path: 'new-supplier-transfers-in-gc', // 供方-调拨管理-调入
    name: 'new-supplier-transfers-in-gc',
    component: () => import('@/views/outsourcingNew/supplier/transfersIn/index.vue')
  },
  {
    path: 'steel-demand-management', // 供方-钢材需求管理
    name: 'steel-demand-management',
    component: () => import('@/views/outsourcingNew/supplier/steelDemandManagement/index.vue')
  },
  {
    path: 'steel-demand-management-detail', // 供方-钢材需求管理详情
    name: 'steel-demand-management-detail',
    component: () =>
      import('@/views/outsourcingNew/supplier/steelDemandManagement/detail/index.vue')
  },
  {
    path: 'steel-request-detail', // 采-钢材需求单详情
    name: 'steel-request-detail',
    component: () =>
      import('@/views/purchaseNewCoordination/pickingManagement/components/requestDetails.vue')
  },
  {
    path: 'new-supplier-inventory-list', // 供方-委外盘点单列表
    name: 'new-supplier-inventory-list',
    component: () => import('@/views/outsourcingNew/supplier/inventoryList/index.vue')
  },
  {
    path: 'new-supplier-inventory-detail', // 供方-委外盘点单详情
    name: 'new-supplier-inventory-detail',
    component: () => import('@/views/outsourcingNew/supplier/inventoryList/details/index.vue')
  },
  //采方
  {
    path: 'new-purchase-detailPiking', // 采方-委外待领料明细
    name: 'new-purchase-detailPiking',
    component: () => import('@/views/outsourcingNew/purchase/detailPiking/index.vue') //
  },
  {
    path: 'new-purchase-transfers', // 采方-委外调拨
    name: 'new-purchase-transfers',
    component: () => import('@/views/outsourcingNew/purchase/purchaseTransfers/index.vue') //
  },
  {
    path: 'new-purchase-transfers-gc', // 采方-委外调拨 gc 仅仅查和保存需要做区分
    name: 'new-purchase-transfers-gc',
    component: () => import('@/views/outsourcingNew/purchase/purchaseTransfers/index.vue') //
  },
  {
    path: 'new-purchase-transfers-detail', // 采方-委外调拨详情
    name: 'new-purchase-transfers-detail',
    component: () => import('@/views/outsourcingNew/purchase/purchaseTransfersDetail/index.vue') //
  },
  {
    path: 'new-purchase-stay-detail', // 采方-委外待领料明细
    name: 'new-purchase-stay-detail',
    component: () => import('@/views/outsourcingNew/purchase/stayDetail/index.vue') // 暂未开发
  },

  {
    path: 'new-purchase-list-picking', // 采方-委外领料管理列表-头视图
    name: 'new-purchase-list-picking',
    component: () => import('@/views/outsourcingNew/purchase/purchaseListPicking/index.vue')
  },

  {
    path: 'new-purchase-detail-picking', // 采方-委外领料管理详情
    name: 'new-purchase-detail-picking',
    component: () => import('@/views/outsourcingNew/purchase/purchaseDetailPicking/index.vue')
  },
  {
    path: 'new-purchase-detail-toBePicked', // 采方-委外待领料明细
    name: 'new-purchase-detail-toBePicked',
    component: () => import('@/views/outsourcingNew/purchase/purchaseDetailToBePicked/index.vue')
  },
  {
    path: 'new-purchase-list-returnCargo-gc', // 采方-委外退货管理 -头视图  仅仅查和保存需要做区分
    name: 'new-purchase-list-returnCargo-gc',
    component: () => import('@/views/outsourcingNew/purchase/purchaseReturnCargo/index.vue')
  },
  {
    path: 'new-purchase-list-returnCargo', // 采方-委外退货管理 -头视图
    name: 'new-purchase-list-returnCargo',
    component: () => import('@/views/outsourcingNew/purchase/purchaseReturnCargo/index.vue')
  },
  {
    path: 'new-purchase-detail-returnCargo', // 采方-委外退货管理 -详情页
    name: 'new-purchase-detail-returnCargo',
    component: () => import('@/views/outsourcingNew/purchase/purchaseDetailReturnCargo/index.vue')
  },
  {
    path: 'new-purchase-pickquantity-config', // 采方 - 委外领料数量限制配置
    name: 'new-purchase-pickquantity-config',
    component: () => import('@/views/outsourcingNew/purchase/pickquantityConfig/index.vue')
  },
  {
    path: 'new-purchase-inventory-list', // 采方 - 盘点单列表
    name: 'new-purchase-inventory-list',
    component: () => import('@/views/outsourcingNew/purchase/InventoryList/index.vue')
  },
  {
    path: 'new-purchase-inventory-detail', // 采方 - 盘点单列表 - 详情
    name: 'new-purchase-inventory-detail',
    component: () => import('@/views/outsourcingNew/purchase/InventoryList/details/index.vue')
  },
  {
    path: 'new-purchase-create-returnCargo', // 采方-委外退货管理 - 创建退货单
    name: 'new-purchase-create-returnCargo',
    component: () =>
      import('@/views/outsourcingNew/purchase/purchaseReturnCargo/createReturnBill.vue')
  },
  {
    path: 'new-purchase-create-transfers', // 采方-空调-委外调拨 - 创建调拨单
    name: 'new-purchase-create-transfers',
    component: () => import('@/views/outsourcingNew/purchase/purchaseTransfers/transferBill.vue'), //
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'balance-inventory', // 采方-加工商结存库存查询
    name: 'balance-inventory',
    component: () => import('@/views/outsourcingNew/purchase/balanceInventory/index.vue')
  },
  {
    path: 'balance-inventory-sup', // 供方-加工商结存库存查询
    name: 'balance-inventory-sup',
    component: () => import('@/views/outsourcingNew/supplier/balanceInventory/index.vue')
  },
  {
    path: 'substitute-material-application', // 采方-替代料申请管理
    name: 'substitute-material-application',
    component: () =>
      import('@/views/outsourcingNew/purchase/substituteMaterialApplication/index.vue')
  },
  {
    path: 'substitute-material-application-sup', // 供方-替代料申请管理
    name: 'substitute-material-application-sup',
    component: () =>
      import('@/views/outsourcingNew/supplier/substituteMaterialApplication/index.vue')
  }
]

export default Router
