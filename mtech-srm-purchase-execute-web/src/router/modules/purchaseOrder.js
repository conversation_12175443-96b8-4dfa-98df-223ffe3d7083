const Router = [
  // 采购订单管理
  {
    path: 'purchase-coordination', // 采购订单协同 -- 采方
    name: 'purchase-coordination',
    meta: {
      keepAlive: true
    },
    component: () => import('@/views/purchaseOrder/purchaseCoordination/index.vue')
  },
  {
    path: 'purchase-coordination-new', // 采购订单协同 -- 采方   重构版
    name: 'purchase-coordination-new',
    meta: {
      keepAlive: true
    },
    component: () => import('@/views/purchaseOrder/purchaseCoordinationNew/index.vue')
  },
  // 采购订单历史数据
  {
    path: 'purchase-coordination-history', // 采购订单历史数据 -- 采方
    name: 'purchase-coordination-history',
    meta: {
      keepAlive: true
    },
    component: () => import('@/views/purchaseOrder/purchaseCoordinationHistory/index.vue')
  },
  {
    path: 'purchase-coordination-history-detail', // 采购订单历史详情 - 采方
    name: 'purchase-coordination-history-detail',
    component: () => import('@/views/purchaseOrder/purchaseCoordinationHistoryDetail/index.vue')
  },
  {
    path: 'purchase-coordination-detail', // 采购订单协同 -- 采方
    name: 'purchase-coordination-detail',
    component: () => import('@/views/purchaseOrder/purchaseCoordinationDetail/index.vue')
  },
  {
    path: 'sales-coordination', // 售后订单协同 -- 采方
    name: 'sales-coordination',
    component: () => import('@/views/purchaseOrder/salesCoordination/index.vue')
  },
  {
    path: 'sales-coordination-detail', // 售后订单协同 -- 采方 (不关联售后订单)
    name: 'sales-coordination-detail',
    component: () => import('@/views/purchaseOrder/salesCoordinationDetail/index.vue')
  },
  {
    path: 'related-after-sales', // 售后订单协同 -- 关联创建售后订单
    name: 'related-after-sales',
    component: () => import('@/views/purchaseOrder/relatedAfterSales/index.vue')
  },
  {
    path: 'custom-receipt', // 客户订单接收（采购订单协同） -- 供方
    name: 'custom-receipt',
    component: () => import('@/views/purchaseOrder/customOrderReceipt/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'custom-receipt-new', // 客户订单接收（采购订单协同） -- 供方   重构版
    name: 'custom-receipt-new',
    component: () => import('@/views/purchaseOrder/customOrderReceiptNew/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'custom-receipt-detail', // 客户订单接收（采购订单协同） -- 供方
    name: 'custom-receipt-detail',
    component: () => import('@/views/purchaseOrder/customOrderReceiptDetail/index.vue')
  },
  {
    path: 'new-mode-custom-receipt', // 客户订单接收（采购订单协同-新模式） -- 供方
    name: 'new-mode-custom-receipt',
    component: () => import('@/views/purchaseOrder/newModeCustomOrderReceipt/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'new-mode-custom-receipt-detail', // 客户订单接收（采购订单协同-新模式） -- 供方
    name: 'new-mode-custom-receipt-detail',
    component: () => import('@/views/purchaseOrder/newModeCustomOrderReceiptDetail/index.vue')
  },
  {
    path: 'custom-order', // 客户售后订单（售后订单协同） -- 供方
    name: 'custom-order',
    component: () => import('@/views/purchaseOrder/customSalesOrder/index.vue')
  },
  {
    path: 'custom-order-detail', // 客户售后订单（售后订单协同） -- 供方
    name: 'custom-order-detail',
    component: () => import('@/views/purchaseOrder/customSalesOrderDetail/index.vue')
  },
  {
    path: 'order-config', // 系统配置- 订单配置
    name: 'order-config',
    component: () => import('@/views/purchaseOrder/systemConfig/orderConfig/index.vue')
  },
  {
    path: 'order-strategy-config', // 系统配置- 采购执行配置
    name: 'order-strategy-config',
    component: () => import('@/views/purchaseOrder/systemConfig/orderStrategyConfig/index.vue')
  },
  {
    path: 'supply-occupy-config', // 系统配置- 供应商预占库存
    name: 'supply-occupy-config',
    component: () => import('@/views/purchaseOrder/systemConfig/sypplyOccupyConfig/index.vue')
  },
  {
    path: 'after-sale-config', // 系统配置- 售后原因维护
    name: 'after-sale-config',
    component: () => import('@/views/purchaseOrder/systemConfig/afterSaleConfig/index.vue')
  },
  {
    path: 'acceptance-config', // 系统配置- 验收项配置
    name: 'acceptance-config',
    component: () => import('@/views/purchaseOrder/systemConfig/acceptanceConfig/index.vue')
  },
  {
    path: 'order-fields-config', // 系统配置 - 订单字段配置
    name: 'order-fields-config',
    component: () => import('@/views/purchaseOrder/systemConfig/orderFieldsConfig/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: 'order-fields-detail', // 系统配置 - 订单字段配置 - 详情
    name: 'order-fields-detail',
    component: () => import('@/views/purchaseOrder/systemConfig/orderFieldsConfig/configDetail.vue')
  },
  {
    path: 'order-combine-config', // 系统配置 - 采购订单并单配置
    name: 'order-combine-config',
    component: () => import('@/views/purchaseOrder/systemConfig/combineConfig/index.vue')
  },
  {
    path: 'supplier-sales-order', // 采方-供方销售订单查询
    name: 'supplier-sales-order',
    component: () => import('@/views/purchaseOrder/supplierSalesOrder/index.vue')
  },
  {
    path: 'sales-order', // 供方-销售订单查询
    name: 'sales-order',
    component: () => import('@/views/purchaseOrder/salesOrder/index.vue')
  },
  {
    path: 'outsourced-order', // 采方-委外订单管理
    name: 'outsourced-order',
    component: () => import('@/views/purchaseOrder/outsourcedOrderManagement/index.vue')
  },
  {
    path: 'outsourced-order-sup', // 供方-委外订单管理
    name: 'outsourced-order-sup',
    component: () => import('@/views/purchaseOrder/outsourcedOrderManagementSup/index.vue')
  }
]

export default Router
