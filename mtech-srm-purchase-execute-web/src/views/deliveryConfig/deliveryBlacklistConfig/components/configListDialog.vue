<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules" autocomplete="off">
      <mt-form-item prop="siteCode" :label="$t('工厂')" class="">
        <debounce-filter-select
          v-model="formData.siteCode"
          :request="postSiteFuzzyQuery"
          :data-source="siteOptions"
          :show-clear-button="true"
          :fields="{ text: 'theCodeName', value: 'siteCode' }"
          :value-template="siteCodeValueTemplate"
          @change="siteCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="name" :label="$t('司机姓名')" class="">
        <mt-input
          maxlength="50"
          v-model="formData.name"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="idCard" :label="$t('司机身份证号')" class="">
        <mt-input
          v-model="formData.idCard"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="license" :label="$t('车牌号')" class="">
        <mt-input
          v-model="formData.license"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="contact" :label="$t('司机手机号')" class="">
        <mt-input
          v-model="formData.contact"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="remark" :label="$t('备注')" class="">
        <mt-input
          maxlength="200"
          v-model="formData.remark"
          :show-clear-button="true"
          :disabled="false"
          :placeholder="$t('请输入')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { DialogActionType, CreateType, ConfigStatus } from '../config/constant'
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { RegExpMap } from '@/utils/constant'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

export default {
  components: {
    DebounceFilterSelect
  },
  data() {
    const { idCardReg, phoneNumReg, numberPlateReg } = RegExpMap
    const identityNumberValidator = (rule, value, callback) => {
      if (!value && !this.formData.contact && !this.formData.license) {
        callback(new Error(this.$t('司机身份证号、车牌号、司机手机号，必输其一')))
      } else if (value && !idCardReg.test(value)) {
        callback(new Error(this.$t('身份证号格式错误')))
      } else {
        this.$refs.ruleForm.clearValidate(['contact'])
        this.$refs.ruleForm.clearValidate(['license'])
        this.$refs.ruleForm.clearValidate(['idCard'])
        callback()
      }
    }
    const phoneNumberValidator = (rule, value, callback) => {
      if (!value && !this.formData.idCard && !this.formData.license) {
        callback(new Error(this.$t('司机身份证号、车牌号、司机手机号，必输其一')))
      } else if (value && !phoneNumReg.test(value)) {
        callback(new Error(this.$t('请输入正确的手机号')))
      } else {
        this.$refs.ruleForm.clearValidate(['contact'])
        this.$refs.ruleForm.clearValidate(['license'])
        this.$refs.ruleForm.clearValidate(['idCard'])
        callback()
      }
    }
    const licenseValidator = (rule, value, callback) => {
      if (!value && !this.formData.idCard && !this.formData.contact) {
        callback(new Error(this.$t('司机身份证号、车牌号、司机手机号，必输其一')))
      } else if (value && !numberPlateReg.test(value)) {
        callback(new Error(this.$t('请输入正确的车牌号')))
      } else {
        this.$refs.ruleForm.clearValidate(['contact'])
        this.$refs.ruleForm.clearValidate(['license'])
        this.$refs.ruleForm.clearValidate(['idCard'])
        callback()
      }
    }
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      dialogTitle: '',
      selectData: null, // 当前编辑的数据
      rules: {
        // 工厂
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        // 司机姓名
        name: [
          {
            required: true,
            message: this.$t('请输入司机姓名'),
            trigger: 'blur'
          }
        ],
        // 司机身份证号
        idCard: [
          {
            validator: identityNumberValidator,
            trigger: 'blur'
          }
        ],
        // 车牌号
        license: [
          {
            validator: licenseValidator,
            trigger: 'blur'
          }
        ],
        // 司机手机号
        contact: [{ validator: phoneNumberValidator, trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      actionType: DialogActionType.Add, // 默认类型：新增
      formData: {
        id: '',

        siteName: '', // 工厂
        siteId: '', // 工厂id
        siteCode: '', // 工厂编码

        createType: '', // 创建方式
        status: '', // 配置状态
        name: '', // 司机姓名
        idCard: '', // 司机身份证号
        license: '', // 车牌号
        contact: '', // 司机手机号
        remark: '', // 备注
        tenantId: '' // 租户id
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      this.$refs.dialog.ejsRef.show()
      const { title, actionType, selectData } = entryInfo
      this.dialogTitle = title // 弹框名称
      this.actionType = actionType // 弹框模式
      this.selectData = selectData // 行数据
      this.formData = this.initForm(selectData)
      if (this.actionType === DialogActionType.Edit) {
        if (selectData.idCard && selectData.idCard.includes('**')) {
          this.$API.deliveryConfig
            .checkDeliveryConfigInfo({ key: selectData.encryptMap.idCard || '' })
            .then((res) => {
              if (res && res.code === 200) {
                this.formData.idCard = res.data || ''
              }
            })
        }
        if (selectData.contact && selectData.contact.includes('**')) {
          this.$API.deliveryConfig
            .checkDeliveryConfigInfo({ key: selectData.encryptMap.contact || '' })
            .then((res) => {
              if (res && res.code === 200) {
                this.formData.contact = res.data || ''
              }
            })
        }
      }

      // 编辑时获取所选择的下拉数据源
      this.handleGetSelectOptionsDuringEdit(selectData)
    },
    initForm(selectData) {
      let formData = null
      this.$refs.ruleForm.clearValidate()
      const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      if (this.actionType === DialogActionType.Add) {
        // 新增
        formData = {
          id: '',

          siteName: '', // 工厂
          siteId: '', // 工厂id
          siteCode: '', // 工厂编码
          companyId: '', // 公司id
          companyCode: '', // 公司code
          companyName: '', // 公司名称
          createType: CreateType.manual, // 创建方式 默认 手动
          status: ConfigStatus.active, // 配置状态 启用
          name: '', // 司机姓名
          idCard: '', // 司机身份证号
          license: '', // 车牌号
          contact: '', // 司机手机号
          remark: '', // 备注
          tenantId // 租户id
        }
      } else if (this.actionType === DialogActionType.Edit) {
        // 编辑
        formData = {
          id: selectData.id,

          siteName: selectData.siteName, // 工厂
          siteId: selectData.siteId, // 工厂id
          siteCode: selectData.siteCode, // 工厂编码

          createType: selectData.createType, // 创建方式
          status: selectData.status, // 配置状态
          name: selectData.name, // 司机姓名
          idCard: selectData.idCard, // 司机身份证号
          license: selectData.license, // 车牌号
          contact: selectData.contact, // 司机手机号
          remark: selectData.remark, // 备注
          tenantId // 租户id
        }
      }
      return formData
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 新增、编辑
          this.postBuyerDriverBlacklistSave()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 工厂 change
    siteCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formData.siteId = itemData.id
        this.formData.siteCode = itemData.siteCode
        this.formData.siteName = itemData.siteName
        this.formData.companyId = itemData.parentId
        this.formData.companyCode = itemData.parentCode
        this.formData.companyName = itemData.parentName
      } else {
        this.formData.siteId = ''
        this.formData.siteCode = ''
        this.formData.siteName = ''
        this.formData.companyId = ''
        this.formData.companyCode = ''
        this.formData.companyName = ''
      }
    },
    // 保存司机
    postBuyerDriverBlacklistSave() {
      const params = {
        id: this.formData.id || undefined, // 不传 id 就是新增
        // 工厂
        siteCode: this.formData.siteCode,
        siteId: this.formData.siteId,
        siteName: this.formData.siteName,
        companyId: this.formData.companyId,
        companyCode: this.formData.companyCode,
        companyName: this.formData.companyName,

        createType: this.formData.createType, // 创建方式
        status: this.formData.status, // 配置状态
        name: this.formData.name, // 司机姓名
        idCard: this.formData.idCard, // 司机身份证号
        license: this.formData.license, // 车牌号
        contact: this.formData.contact, // 司机手机号
        remark: this.formData.remark, // 备注
        tenantId: this.formData.tenantId // 租户id
      }
      this.apiStartLoading()
      this.$API.deliveryConfig
        .postBuyerDriverBlacklistSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$emit('confirm')
            this.handleClose()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 获取主数据-工厂
    postSiteFuzzyQuery(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        dataLimit: 100,
        fuzzyParam: text
      }
      this.$API.masterData
        .postSiteFuzzyQuery(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 编辑时获取所选择的下拉数据源
    handleGetSelectOptionsDuringEdit(selectData) {
      if (this.actionType === DialogActionType.Edit) {
        // siteCode 工厂
        this.postSiteFuzzyQuery({
          text: selectData.siteCode,
          setSelectData: () => {
            // api获取数据后重新赋值，防止没有赋上值得情况
            this.formData.siteCode = selectData.siteCode
          }
        })
      } else if (this.actionType === DialogActionType.Add) {
        // 获取主数据-工厂模糊查询
        this.postSiteFuzzyQuery({ text: undefined })
      }
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss" scoped></style>
