import { i18n } from '@/main.js'

export const Tab = {
  statements: 'statements', // 可开票单据
  statementDetails: 'statementDetails', // 可开票明细
  invoices: 'invoices' // 发票列表
}

export const ConstantType = {
  Add: '1', // 新增
  Edit: '2', // 编辑
  Look: '3' // 查看
}

// 发票编辑弹框 显示类型
// export const InvoiceListDialogActionType = {
//   apply: "0", // 申请开票
//   add: "1", // 发票新增
//   edit: "2", // 发票编辑
// };

// 表格 checkbox
export const ColumnCheckbox = {
  width: '50',
  type: 'checkbox',
  showInColumnChooser: false
}

// 可开票明细 tab 表格 Toolbar
export const StatementDetailsToolbar = [
  {
    id: 'ApplyInvoice',
    icon: 'icon_solid_Createproject',
    permission: ['O_02_0623'],
    title: i18n.t('申请开票')
  }
]

// 可开票单据 tab 单据开票状态 cellTools
export const BillInvoiceStatusCellTools = [
  {
    id: 'UploadInvoice',
    icon: 'icon_list_download rotate-180',
    title: i18n.t('上传发票'),
    permission: ['O_02_0622'],
    visibleCondition: (data) =>
      data.invoiceStatus == BillInvoiceStatus.toBeBilled ||
      data.invoiceStatus == BillInvoiceStatus.partialUpload ||
      data.invoiceStatus == BillInvoiceStatus.finishedUploadingPendingSubmission // 开票状态 == 未完成 || 部分开票 || 已全部开票待提交
  }
  // {
  //   id: "EditBillInvoice",
  //   icon: "icon_list_edit",
  //   title: i18n.t("编辑"),
  //   // permission: ["O_02_0119"],
  //   visibleCondition: (data) =>
  //     data.invoiceStatus == BillInvoiceStatus.invoiceAll, // 开票状态 == 全部开票
  // },
  // {
  //   id: "SubmitBill",
  //   icon: "icon_Share_2",
  //   title: i18n.t("提交"),
  //   // permission: ["O_02_0119"],
  //   visibleCondition: (data) =>
  //     data.invoiceStatus == BillInvoiceStatus.invoiceAll, // 开票状态 == 全部开票
  // },
]

// 发票列表 tab 发票状态 cellTools
export const InvoiceStatusCellTools = [
  {
    id: 'EditInvoice',
    icon: 'icon_list_edit',
    title: i18n.t('编辑'),
    permission: ['O_02_0624'],
    visibleCondition: (data) =>
      data.status == InvoiceStatus.pendingSubmission || data.status == InvoiceStatus.returned // 发票状态 == 待提交 || 已退回
  },
  {
    id: 'DeleteInvoice',
    icon: 'icon_list_delete',
    title: i18n.t('删除'),
    permission: ['O_02_0627'],
    visibleCondition: (data) => data.status == InvoiceStatus.pendingSubmission // 待提交
  },
  {
    id: 'SubmitInvoice',
    icon: 'icon_Share_2',
    title: i18n.t('提交'),
    permission: ['O_02_0625'],
    visibleCondition: (data) =>
      data.status == InvoiceStatus.pendingSubmission || data.status == InvoiceStatus.returned // 待提交 || 已退回
  },
  {
    id: 'CloseInvoice',
    icon: 'icon_list_close',
    title: i18n.t('关闭'),
    permission: ['O_02_0626'],
    visibleCondition: (data) => data.status == InvoiceStatus.returned // 已退回
  }
]

// 发票状态 0-待提交、1-待审核、2-待财务审核、3-完成、4-已退回、5-已删除
export const InvoiceStatus = {
  pendingSubmission: 0, // 待提交
  pendingReview: 1, // 待审核
  pendingFinancialReview: 2, // 待财务审核
  complete: 3, // 完成
  returned: 4, // 已退回
  deleted: 5 // 已删除
}
// 发票状态
export const InvoiceStatusConst = {
  [InvoiceStatus.pendingSubmission]: i18n.t('待提交'),
  [InvoiceStatus.pendingReview]: i18n.t('待确认'), // 待审核
  [InvoiceStatus.pendingFinancialReview]: i18n.t('采购已确认'), // 待财务审核
  [InvoiceStatus.complete]: i18n.t('财务已确认'), // 完成
  [InvoiceStatus.returned]: i18n.t('已退回'),
  [InvoiceStatus.deleted]: i18n.t('已删除')
}
// 发票状态 对应的 css class
export const InvoiceStatusCssClass = {
  [InvoiceStatus.pendingSubmission]: 'col-active',
  [InvoiceStatus.pendingReview]: 'col-active',
  [InvoiceStatus.pendingFinancialReview]: 'col-normal',
  [InvoiceStatus.complete]: 'col-normal',
  [InvoiceStatus.returned]: 'col-published',
  [InvoiceStatus.deleted]: 'col-inactive'
}
// 发票状态 对应的 Options
export const InvoiceStatusOptions = [
  {
    // 待提交
    value: InvoiceStatus.pendingSubmission,
    text: InvoiceStatusConst[InvoiceStatus.pendingSubmission],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingSubmission]
  },
  {
    // 待审核
    value: InvoiceStatus.pendingReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingReview]
  },
  {
    // 待财务审核
    value: InvoiceStatus.pendingFinancialReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingFinancialReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingFinancialReview]
  },
  {
    // 完成
    value: InvoiceStatus.complete,
    text: InvoiceStatusConst[InvoiceStatus.complete],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.complete]
  },
  {
    // 已退回
    value: InvoiceStatus.returned,
    text: InvoiceStatusConst[InvoiceStatus.returned],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.returned]
  },
  {
    // 已删除
    value: InvoiceStatus.deleted,
    text: InvoiceStatusConst[InvoiceStatus.deleted],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.deleted]
  }
]

// 单据开票状态 0待开票 10部分开票 1全部开票 11已全部开票待提交
export const BillInvoiceStatus = {
  toBeBilled: 0, // 未完成 (没有提交一张发票)
  partialUpload: 10, // 部分完成 部分开票
  finishedUploadingPendingSubmission: 11, // 部分完成 已全部开票，待提交
  fullyCompleted: 1 // 全部完成 全部开票，全部提交
}
// 单据开票状态 0待开票 10部分开票 1全部开票
export const BillInvoiceStatusConst = {
  [BillInvoiceStatus.toBeBilled]: i18n.t('未完成'),
  [BillInvoiceStatus.partialUpload]: i18n.t('部分开票'),
  [BillInvoiceStatus.finishedUploadingPendingSubmission]: i18n.t('待提交'),
  [BillInvoiceStatus.fullyCompleted]: i18n.t('全部完成')
}
// 单据开票状态 对应的 css class
export const BillInvoiceStatusCssClass = {
  [BillInvoiceStatus.toBeBilled]: 'col-active',
  [BillInvoiceStatus.partialUpload]: 'col-active',
  [BillInvoiceStatus.finishedUploadingPendingSubmission]: 'col-active',
  [BillInvoiceStatus.fullyCompleted]: 'col-inactive'
}
// 单据开票状态 0待开票 10部分开票 1全部开票 11已全部开票待提交
export const BillInvoiceStatusOptions = [
  {
    // 未完成 (没有提交一张发票)
    value: BillInvoiceStatus.toBeBilled,
    text: BillInvoiceStatusConst[BillInvoiceStatus.toBeBilled],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.toBeBilled]
  },
  {
    // 部分完成 部分开票
    value: BillInvoiceStatus.partialUpload,
    text: BillInvoiceStatusConst[BillInvoiceStatus.partialUpload],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.partialUpload]
  },
  {
    // 部分完成 已全部开票，待提交
    value: BillInvoiceStatus.finishedUploadingPendingSubmission,
    text: BillInvoiceStatusConst[BillInvoiceStatus.finishedUploadingPendingSubmission],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.finishedUploadingPendingSubmission]
  },
  {
    // 全部完成 全部开票，全部提交
    value: BillInvoiceStatus.fullyCompleted,
    text: BillInvoiceStatusConst[BillInvoiceStatus.fullyCompleted],
    cssClass: BillInvoiceStatusCssClass[BillInvoiceStatus.fullyCompleted]
  }
]

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0, // 否
  synching: 2 //同步中
}

// 同步状态 对应的 Options
export const SyncStatusOptions = [
  {
    value: SyncStatus.notSynced,
    text: i18n.t('未同步'),
    cssClass: 'col-notSynced'
  },
  {
    value: SyncStatus.synced,
    text: i18n.t('已同步'),
    cssClass: 'col-synced'
  },
  {
    value: SyncStatus.synching,
    text: i18n.t('同步中'),
    cssClass: 'col-synced'
  }
]

// 可开票单据 tab 表格列数据
export const StatementsColumnData = [
  {
    code: 'reconciliationCode',
    name: i18n.t('单据号')
  },
  {
    code: 'invoiceStatus',
    name: i18n.t('开票状态')
  },
  {
    code: 'companyName',
    name: i18n.t('客户公司')
  },
  {
    code: 'companyCode',
    name: i18n.t('客户公司编码')
  },
  // {
  //   code: "supplierName",
  //   name: i18n.t("供应商"),
  // },
  // {
  //   code: "supplierCode",
  //   name: i18n.t("供应商编码"),
  // },
  {
    code: 'siteName',
    name: i18n.t('地点/工厂名称')
  },
  {
    code: 'siteCode',
    name: i18n.t('地点/工厂代码')
  },
  {
    code: 'status',
    name: i18n.t('单据状态')
  },
  {
    code: 'headerTypeName',
    name: i18n.t('单据类型')
  },
  {
    code: 'reconciliationTypeName',
    name: i18n.t('对账类型')
  },
  {
    code: 'executeUntaxedTotalPrice',
    name: i18n.t('执行未税总价')
  },
  {
    code: 'taxAmount',
    name: i18n.t('税额')
  },
  {
    code: 'executeTaxedTotalPrice',
    name: i18n.t('执行含税总价')
  },
  {
    code: 'taxInvoiceBalance',
    name: i18n.t('含税发票差额')
  },
  {
    code: 'currencyName',
    name: i18n.t('币种')
  },
  {
    code: 'invoiceQuantity',
    name: i18n.t('发票张数')
  },
  {
    code: 'invoiceCode',
    name: i18n.t('发票代码')
  },
  {
    code: 'checkCode',
    name: i18n.t('后六位校验码')
  },
  {
    code: 'remark',
    name: i18n.t('采方备注')
  },
  {
    code: 'feedbackRemark',
    name: i18n.t('供方备注')
  },
  {
    code: 'createUserName',
    name: i18n.t('创建人')
  },
  {
    code: 'createTime',
    name: i18n.t('创建时间')
  }
]

// 发票列表 tab 表格列数据 invoices
export const InvoicesColumnData = [
  {
    code: 'status',
    name: i18n.t('发票状态')
  },
  {
    code: 'syncStatus',
    name: i18n.t('同步状态')
  },
  {
    code: 'theHeaderTypeName', // theHeaderTypeName FIXME: 暂时前端固定 对账单
    name: i18n.t('关联单据类型')
  },
  {
    code: 'reconciliationHeaderCodes',
    name: i18n.t('关联单据号')
  },
  {
    code: 'invoiceNum',
    name: i18n.t('发票号')
  },
  {
    code: 'invoiceCode',
    name: i18n.t('发票代码')
  },
  {
    code: 'companyName',
    name: i18n.t('公司名称')
  },
  {
    code: 'companyCode',
    name: i18n.t('公司编码')
  },
  {
    code: 'invoiceTaxedAmount',
    name: i18n.t('发票含税金额')
  },
  {
    code: 'invoiceUntaxAmount',
    name: i18n.t('发票未税金额')
  },
  {
    code: 'invoiceTaxAmount',
    name: i18n.t('税额')
  },
  {
    code: 'reconciliationItemCount',
    name: i18n.t('明细行')
  },
  {
    code: 'invoiceTime',
    name: i18n.t('开票日期')
  },
  {
    code: 'currencyName',
    name: i18n.t('币种')
  },
  {
    code: 'invoiceTypeName',
    name: i18n.t('发票类型')
  },
  {
    code: 'customerRemark',
    name: i18n.t('采方备注')
  },
  {
    code: 'supplierRemark',
    name: i18n.t('供方备注')
  },
  {
    code: 'attachementCount',
    name: i18n.t('发票附件')
  },
  {
    code: 'createUserName',
    name: i18n.t('创建人')
  },
  {
    code: 'createTime',
    name: i18n.t('创建时间')
  },
  {
    code: 'reviewTime',
    name: i18n.t('财务审核时间')
  }
]

// 来源类型（枚举） 0: 上游流入1:第三方接口
export const SourceTypeOptions = [
  {
    value: 0,
    text: i18n.t('上游流入'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('第三方接口'),
    cssClass: ''
  }
]

// 单据状态 0:待对账 1:已创建对账单
export const ReconciliationDetailsStatusOptions = [
  {
    value: 0,
    text: i18n.t('待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('已创建对账单'),
    cssClass: ''
  }
]
// 单据状态 0:待对账 1:已创建对账单
export const ReconciliationStatusOptions = [
  {
    value: -1,
    text: i18n.t('关闭'),
    cssClass: ''
  },
  {
    value: 0,
    text: i18n.t('未发布'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('发布待确认'),
    cssClass: ''
  },
  {
    value: 2,
    text: i18n.t('反馈正常'),
    cssClass: ''
  },
  {
    value: 3,
    text: i18n.t('反馈异常'),
    cssClass: ''
  },
  {
    value: 9,
    text: i18n.t('部分开票'),
    cssClass: ''
  },
  {
    value: 11,
    text: i18n.t('开票完成'),
    cssClass: ''
  }
]

// 提前开票 0:否 1:是
export const AdvanceInvoicingOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 冻结标记 0:否 1:是
export const FrozenStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否预付 0-否；1-是
export const PrePayStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否暂估价 0-否；1-是
export const ProvisionalEstimateStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]
// 是否执行价 0-是；1-否
export const ProvisionalEstimateStatusOptions1 = [
  {
    value: 0,
    text: i18n.t('是'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('否'),
    cssClass: ''
  }
]

// 待对账类型:0-采购待对账；1-销售待对账
export const ReconciliationDetailsTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('销售待对账'),
    cssClass: ''
  }
]

// 正式价标识 0-无正式价；1-有正式价
export const RealPriceStatusOptions = [
  {
    value: 0,
    text: i18n.t('无正式价'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('有正式价'),
    cssClass: ''
  }
]

// 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
export const InOutTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购入库'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('采购出库'),
    cssClass: ''
  },
  {
    value: 2,
    text: i18n.t('销售出库'),
    cssClass: ''
  },
  {
    value: 3,
    text: i18n.t('销售退回'),
    cssClass: ''
  }
]
