import { i18n } from '@/main.js'

export const billStatusOptions = [
  { text: i18n.t('待调拨'), value: '0' },
  { text: i18n.t('已完成'), value: '1' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'allotNo',
    title: i18n.t('调拨单号')
  },
  {
    field: 'billStatus',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = billStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'billType',
    title: i18n.t('单据类型')
  },
  {
    field: 'allotPurpose',
    title: i18n.t('调拨目的')
  },
  {
    field: 'ifNeedConfirmAccept',
    title: i18n.t('是否需要确认接收')
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  },
  {
    field: 'finishTime',
    title: i18n.t('完成时间'),
    minWidth: 120
  },
  {
    field: 'mouldNo',
    title: i18n.t('模具编号')
  },
  {
    field: 'outSide',
    title: i18n.t('调出方')
  },
  {
    field: 'outSideName',
    title: i18n.t('调出方名称')
  },
  {
    field: 'inSide',
    title: i18n.t('调入方')
  },
  {
    field: 'inSideName',
    title: i18n.t('调入方名称')
  },
  {
    field: 'createdAccountName',
    title: i18n.t('创建人')
  },
  {
    field: 'creationDate',
    title: i18n.t('创建时间'),
    minWidth: 120
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 120
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    fixed: 'right',
    slots: {
      default: 'operateDefault'
    }
  }
]
