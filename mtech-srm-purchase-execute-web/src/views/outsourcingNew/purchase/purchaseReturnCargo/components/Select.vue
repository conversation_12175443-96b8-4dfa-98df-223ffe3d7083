<template>
  <div>
    <!-- 物料编码和库存地点使用原有的弹框选择方式 -->
    <div
      v-if="data.column.field === 'itemCode' || data.column.field === 'warehouseCode'"
      class="in-cell"
    >
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="postChange"
        :data-source="dataSource"
        :fields="getFieldsConfig()"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>

    <!-- 采购订单号和采购订单行号使用纯下拉选择 -->
    <div v-else>
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="postChange"
        :data-source="dataSource"
        :fields="getFieldsConfig()"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
import { maxPageSize } from '@/utils/constant'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '3aee5144-1201-4eb5-8a0d-8b5792261a9a',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
              // {
              //   width: "150",
              //   field: "itemUnitDescription",
              //   headerText: this.$t("单位"),
              // },
              // {
              //   width: "150",
              //   field: "purchaseGroupName",
              //   headerText: this.$t("采购组"),
              // },
              // {
              //   width: "150",
              //   field: "batchCode",
              //   headerText: this.$t("批次号"),
              // },
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      codeArr: JSON.parse(sessionStorage.getItem('codeArr'))
    }
  },
  mounted() {
    this.codeArr = JSON.parse(sessionStorage.getItem('codeArr') || '{}')

    if (this.data.column.field === 'itemCode') {
      this.data.itemCode === null
        ? this.getCategoryItem('')
        : this.getCategoryItem(this.data.itemCode)

      this.getCategoryItem(this.data.itemCode)
    }
    if (this.data.column.field === 'warehouseCode') {
      this.getWarehouseCodeOptions()
    }

    // 根据字段类型初始化数据源
    if (this.data.column.field === 'orderCode') {
      this.getOrderCodeOptions()
    } else if (this.data.column.field === 'orderItemNo') {
      this.getorderItemNoOptions()
    }

    // 监听物料编码变化事件
    this.$bus.$on('itemCodeChange', this.handleItemCodeChange)
    // 监听采购订单号变化事件
    this.$bus.$on('orderCodeChange', this.handleOrderCodeChange)
  },
  methods: {
    // 根据字段类型返回不同的字段配置
    getFieldsConfig() {
      if (this.data.column.field === 'itemCode') {
        return { text: 'codeAndName', value: 'itemCode' }
      } else if (this.data.column.field === 'orderCode') {
        return { text: 'codeAndName', value: 'orderCode' }
      } else if (this.data.column.field === 'orderItemNo') {
        return { text: 'codeAndName', value: 'orderItemNo' }
      } else {
        return { text: 'codeAndName', value: 'value' }
      }
    },

    recordDoubleClick(args) {
      this.selectChange({ itemData: args.rowData })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      this.selectChange({ itemData: records[0] })
    },
    showDialog() {
      // 只有物料编码和库存地点字段才显示弹框
      if (this.data.column.field === 'itemCode') {
        this.pageConfig[0].grid.asyncConfig = {
          url: `masterDataManagement/auth/item-org-rel/pur-paged-query`,
          recordsPosition: 'data.records',
          condition: 'and',
          page: { current: 1, size: 20 },
          defaultRules: [
            {
              label: this.$t('物料编号'),
              field: 'itemCode',
              type: 'string',
              operator: 'contains',
              value: ''
            }
          ],
          organizationCode: this.codeArr?.siteCode ?? ''
        }
        this.$refs.dialog.ejsRef.show()
      }
    },
    // 获取库存地点
    getWarehouseCodeOptions() {
      let obj = {
        enterpriseId: this.codeArr.buyerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: this.codeArr.siteCode
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.siteAddressName //
          item.value = item.siteAddress //
        })
        this.dataSource = res.data.records.map((i) => {
          return {
            ...i,
            codeAndName: `${i.siteAddress} - ${i.siteAddressName}`
          }
        })
      })
    },
    // 模糊搜索
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text)
      } else if (this.data.column.field === 'orderCode') {
        this.getOrderCodeOptions(e.text)
      } else if (this.data.column.field === 'orderItemNo') {
        this.getorderItemNoOptions(e.text)
      }
    },

    // getCategoryItem(e) {
    //   //物料下拉
    //   let obj = {
    //     page: {
    //       current: 1,
    //       size: 20,
    //     },
    //     rules: [
    //       {
    //         field: "itemCode",
    //         operator: "likeright",
    //         value: e,
    //       },
    //     ],
    //     customerEnterpriseId: this.codeArr.buyerEnterpriseId,
    //     organizationCode: this.codeArr.siteCode,
    //   };
    //   this.$API.masterData.getOrgRel(obj).then((res) => {
    //     res.data.records.forEach((item) => {
    //       item.name = item.itemCode; // 	客户名称
    //       item.value = item.itemCode; // 	客户名称
    //     });
    //     this.dataSource =
    //       res.data.records.map((i) => {
    //         return {
    //           ...i,
    //           codeAndName: `${i.itemCode} - ${i.itemName}`,
    //         };
    //       }) || [];
    //   });
    // },
    getCategoryItem(e) {
      //物料下拉
      let obj = {
        page: {
          current: 1,
          size: 100
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
        ],
        organizationCode: this.codeArr?.siteCode ?? ''
      }
      this.$API.outsourcingNew.getitemCodeList(obj).then((res) => {
        this.dataSource =
          res.data.records.map((i) => {
            const item = {
              ...i,
              codeAndName: `${i.itemCode} - ${i.itemName}`,
              itemCode: i.itemCode, // 作为value字段
              name: i.itemCode,
              value: i.itemCode,
              text: `${i.itemCode} - ${i.itemName}`
            }
            return item
          }) || []
      })
    },
    selectChange(val) {
      console.log('selectChange val:', val)

      // 处理物料编码选择
      if (this.data.column.field === 'itemCode') {
        // 根据debounce-filter-select组件的实现，val.value是选中的值
        let itemCode = val.value || val.itemData?.itemCode

        if (itemCode) {
          this.$set(this.data, this.data.column.field, itemCode)

          // 清空采购订单号和采购订单行号
          this.$set(this.data, 'orderCode', '')
          this.$set(this.data, 'orderItemNo', '')

          // 调用接口获取物料相关信息
          let obj = {
            buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
            createType: 2,
            itemCode: itemCode,
            buyerOrgCode: this.codeArr.buyerOrgCode,
            isOutSale: this.codeArr.isOutSale,
            id: this.data?.id,
            siteCode: this.codeArr.siteCode,
            supplierCode: this.codeArr.supplierCode
          }

          this.$API.outsourcingNew
            .OutQuerySapOutDemand(obj)
            .then((res) => {
              if (res.data && res.data.length > 0) {
                this.$bus.$emit('itemNameChange', res.data[0].itemName) //传给物料名称
                this.$bus.$emit('planGroupNameChange', res.data[0].planGroupName) // 计划组
                this.$bus.$emit('buyerOrgNameChange', {
                  buyerGroupName: res.data[0].buyerGroupName,
                  buyerGroupCode: res.data[0].buyerGroupCode
                }) // 采购组
                this.$bus.$emit('basicUnitCodeChange', {
                  basicUnitCode: res.data[0].basicUnitCode,
                  basicUnitName: res.data[0].basicUnitName
                }) //传给单位
                this.$bus.$emit('maxDemandQuantityChange', res.data[0].maxReceiveQuantity) //传给可调拨数量
                this.$bus.$emit('supplierStockChange', res.data[0].supplierStock) //传给库存现有
              }
            })
            .catch((error) => {
              console.error('OutQuerySapOutDemand接口调用失败:', error)
            })

          // 触发采购订单号数据更新
          this.$bus.$emit('itemCodeChange', {
            itemCode: itemCode,
            rowIndex: this.data.addId || this.data.id
          })
        }

        this.handleClose()
      }

      // 采购订单号选择处理
      if (this.data.column.field === 'orderCode') {
        const orderCode = val.value || val.itemData?.orderCode || val.itemData?.value
        this.data.orderCode = orderCode
        console.log('选中的采购订单号:', orderCode)

        // 清空采购订单行号
        this.data.orderItemNo = ''

        // 触发采购订单行号数据更新
        this.$bus.$emit('orderCodeChange', {
          orderCode: orderCode,
          rowIndex: this.data.addId || this.data.id
        })
      }

      // 采购订单行号选择处理
      if (this.data.column.field === 'orderItemNo') {
        const orderItemNo = val.value || val.itemData?.orderItemNo || val.itemData?.value
        this.data.orderItemNo = orderItemNo
        console.log('选中的采购订单行号:', orderItemNo)
      }

      if (this.data.column.field === 'warehouseCode') {
        this.data.warehouseName = val.itemData.siteAddressName
        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'warehouseCode',
          itemInfo: {
            ...this.data
          }
        })
        this.handleClose()
      }
    },

    // 获取采购订单号选项
    getOrderCodeOptions(searchText = '') {
      if (!this.data.itemCode || !this.codeArr.supplierCode || !this.codeArr.siteCode) {
        console.log('缺少必要参数，无法获取采购订单号')
        this.dataSource = []
        return
      }

      const params = {
        itemCode: this.data.itemCode,
        supplierCode: this.codeArr.supplierCode,
        siteCode: this.codeArr.siteCode
      }

      console.log('获取采购订单号，参数:', params)
      console.log('搜索文本:', searchText)

      this.$API.outsourcing
        .getSapComponentDetails(params)
        .then((res) => {
          console.log('采购订单号接口返回:', res)
          if (res.data && Array.isArray(res.data)) {
            let orderCodes = res.data.map((item) => ({
              ...item,
              orderCode: item.orderCode || item.code,
              value: item.orderCode || item.code,
              text: item.orderCode || item.code,
              codeAndName: item.orderCode || item.code
            }))

            // 如果有搜索文本，进行过滤
            if (searchText) {
              orderCodes = orderCodes.filter(
                (item) => item.codeAndName && item.codeAndName.includes(searchText)
              )
              console.log('过滤后的采购订单号:', orderCodes)
            }

            this.dataSource = orderCodes
          } else {
            this.dataSource = []
          }
        })
        .catch((error) => {
          console.error('获取采购订单号失败:', error)
          this.dataSource = []
        })
    },

    // 获取采购订单行号选项
    getorderItemNoOptions(searchText = '') {
      if (!this.data.orderCode) {
        console.log('缺少采购订单号，无法获取采购订单行号')
        this.dataSource = []
        return
      }

      const params = {
        siteCode: this.codeArr.siteCode,
        supplierCode: this.codeArr.supplierCode,
        orderCode: this.data.orderCode
      }

      console.log('获取采购订单行号，参数:', params)
      console.log('搜索文本:', searchText)

      this.$API.outsourcing
        .getSapComponentDetails(params)
        .then((res) => {
          console.log('采购订单行号接口返回:', res)
          if (res.data && Array.isArray(res.data)) {
            let orderItemNos = res.data.map((item) => ({
              ...item,
              orderItemNo: item.orderItemNo || item.itemCode || item.code,
              value: item.orderItemNo || item.itemCode || item.code,
              text: item.orderItemNo || item.itemCode || item.code,
              codeAndName: item.orderItemNo || item.itemCode || item.code
            }))

            // 如果有搜索文本，进行过滤
            if (searchText) {
              orderItemNos = orderItemNos.filter(
                (item) => item.codeAndName && item.codeAndName.includes(searchText)
              )
              console.log('过滤后的采购订单行号:', orderItemNos)
            }

            this.dataSource = orderItemNos
          } else {
            this.dataSource = []
          }
        })
        .catch((error) => {
          console.error('获取采购订单行号失败:', error)
          this.dataSource = []
        })
    },

    // 处理物料编码变化事件
    handleItemCodeChange(data) {
      console.log('收到物料编码变化事件:', data)
      console.log('当前组件行索引:', this.data.addId || this.data.id)
      console.log('当前字段:', this.data.column.field)

      if (data.rowIndex === (this.data.addId || this.data.id)) {
        console.log('匹配到当前行，开始处理物料编码变化')

        // 如果当前字段就是物料编码，不需要处理，避免干扰当前的选择操作
        if (this.data.column.field === 'itemCode') {
          console.log('当前字段是物料编码，跳过处理避免干扰')
          return
        }

        // 更新物料编码
        this.data.itemCode = data.itemCode

        // 清空当前行的采购订单号和采购订单行号
        this.data.orderCode = ''
        this.data.orderItemNo = ''

        // 清空数据源并重新获取数据
        this.dataSource = []

        // 根据当前字段类型重新获取数据
        if (this.data.column.field === 'orderCode') {
          console.log('当前是采购订单号字段，重新获取数据')
          // 延迟调用，确保物料编码已经更新
          this.$nextTick(() => {
            this.getOrderCodeOptions()
          })
        } else if (this.data.column.field === 'orderItemNo') {
          console.log('当前是采购订单行号字段，清空数据源')
          // 采购订单行号字段也需要清空
          this.dataSource = []
        }
      }
    },

    // 处理采购订单号变化事件
    handleOrderCodeChange(data) {
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderItemNo'
      ) {
        // 清空当前行的采购订单行号
        this.data.orderItemNo = ''
        this.dataSource = []

        // 重新获取采购订单行号数据
        this.getorderItemNoOptions()
      }
    }
  },
  deactivated() {
    this.$bus.$off('maxDemandQuantityChange')
    this.$bus.$off('warehouseChange')
    this.$bus.$off('itemNameChange')
    this.$bus.$off('warehouseChange2')
    this.$bus.$off('itemCodeChange', this.handleItemCodeChange)
    this.$bus.$off('orderCodeChange', this.handleOrderCodeChange)
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
