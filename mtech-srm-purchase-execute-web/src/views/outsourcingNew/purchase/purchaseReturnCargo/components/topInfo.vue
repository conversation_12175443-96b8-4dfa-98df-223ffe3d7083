<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="middle-blank">
        <div style="display: flex">
          <div class="infos mr20">{{ $t('新建') }}</div>
          <div class="infos mr20">{{ $t('制单人：') }}{{ getCurrentUserInfo() }}</div>
          <div class="infos mr20">{{ $t('制单日期：') }}{{ getCurrentTime() }}</div>
        </div>
      </div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="submit">{{ $t('保存') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="resoveClick">{{
        $t('提交')
      }}</mt-button>
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="buyerEnterpriseId" :label="$t('公司')">
          <mt-select
            ref="businessRef"
            v-model="topInfo.buyerEnterpriseId"
            @change="companyClick"
            :disabled="isDisabled2"
            :data-source="companyList"
            :allow-filtering="true"
            :filtering="getSourceCompany1"
            :fields="{ text: 'name', value: 'code' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('工厂')">
          <debounce-filter-select
            v-model="topInfo.siteCode"
            :value-template="siteCodeValueTemplate"
            :data-source="siteOptions"
            :is-active="true"
            :disabled="isDisabled2"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="siteOrgClick"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item :label="$t('加工供应商')">
          <debounce-filter-select
            v-model="topInfo.supplierCode"
            :request="getSupplier"
            :data-source="supplierOptions"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :value-template="supplierCodeValueTemplate"
            :show-clear-button="true"
            :allow-filtering="true"
            @change="supplierCodeChange"
            :placeholder="$t('请选择')"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item :label="$t('原材料供应商')">
          <debounce-filter-select
            :disabled="topInfo.isOutDirect == '0' || isDisabled2"
            v-model="topInfo.materialSupplierCode"
            :request="getSourceMaterialSupplier"
            :data-source="materialList"
            :value-template="supplierCodeValueTemplate"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            @change="materialClick"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>

        <mt-form-item :label="$t('原材料供应商描述')">
          <mt-input type="text" disabled v-model="topInfo.materialSupplierName"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('处理方式')" prop="cancelType">
          <!-- <mt-input type="text" v-model="topInfo.cancelType"></mt-input> -->
          <mt-radio
            v-model="topInfo.cancelType"
            :disabled="isDisabled2"
            :data-source="cancelData"
            @change="radioChange"
          ></mt-radio>
        </mt-form-item>

        <mt-form-item :label="$t('退货地址')">
          <mt-input type="text" v-model="topInfo.cancelAddress"></mt-input>

          <!-- <mt-select
            :show-clear-button="true"
            v-model="topInfo.cancelAddress"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :filtering="getWarehouseCodeOptions"
            :data-source="cancelList"
            :fields="{ text: 'siteAddressName', value: 'siteAddress' }"
            :placeholder="$t('请选择')"
            @change="radioChange"
          ></mt-select> -->
        </mt-form-item>
        <mt-form-item :label="$t('库存地点')">
          <mt-select
            v-model="topInfo.warehouseCode"
            @change="warehouseClick"
            :data-source="cancelList"
            :fields="{ text: 'theCodeName', value: 'siteAddress' }"
            :placeholder="$t('请选择')"
            :allow-filtering="true"
          ></mt-select>
        </mt-form-item>

        <mt-form-item :label="$t('是否销售委外')" prop="isOutSale">
          <mt-radio
            v-model="topInfo.isOutSale"
            :data-source="radioData"
            :disabled="isDisabled"
            @change="radioChange"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item :label="$t('是否直退材料商')" prop="isOutDirect">
          <mt-radio
            v-model="topInfo.isOutDirect"
            @change="whitch"
            :disabled="isDisabled || isDisabled2"
            :data-source="radioData"
          ></mt-radio>
        </mt-form-item>

        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="topInfo.remark"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { Query } from '@syncfusion/ej2-data'
import Vue from 'vue'
import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'

const buyCodeSelectTemplate = () => {
  return {
    template: Vue.component('buyCodeSelectTemplate', {
      template: `
        <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span>{{data.customerCode}}+{{data.customerName}}</span>
            </div>
          </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}

const siteCodeSelectTemplate = () => {
  return {
    template: Vue.component('siteCodeSelectTemplate', {
      template: `
    <div>
      <div>{{data.siteCode}}-{{data.siteName}}</div>
    </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    headerInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      supplierOptions: [], // 供应商 下列选项
      typeData: [
        {
          label: this.$t('钢材'),
          value: 1
        },
        {
          label: this.$t('非钢材'),
          value: 0
        }
      ],
      isDisabled2: false,
      buyerItem: buyCodeSelectTemplate,
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }),
      siteCodeValueTemplate: siteCodeSelectTemplate, // 工厂
      radioData: [
        {
          label: this.$t('否'),
          value: '0'
        },
        {
          label: this.$t('是'),
          value: '1'
        }
      ],
      cancelData: [
        {
          label: this.$t('良品退货'),
          value: '0'
        },
        {
          label: this.$t('不良品退货'),
          value: '1'
        }
      ],
      cancelList: [],
      materialList: [],
      supplier: '',
      isDisabled: this.$route.path.includes('gc') ? true : false,
      customerEnterpriseId: '',
      topInfo: {
        companyId: '',
        buyerCode: '',
        orderType: '0',
        buyerOrgId: '',
        buyerOrgName: '',
        buyerEnterpriseId: '',
        buyerName: '',
        quantity: '',
        itemName: '',
        orderItemNo: '',
        itemCode: undefined,
        // buyerTenantId: 0,
        orderCode: '',
        cancelAddress: '',
        cancelOrderCode: '',
        cancelType: '0',
        id: '',
        isOutDirect: this.$route.path.includes('gc') ? '1' : '',
        isOutSale: this.$route.path.includes('gc') ? '1' : '',
        materialSupplierCode: null,
        materialSupplierId: '',
        materialSupplierName: '',
        remark: '',
        siteCode: '',
        siteId: '',
        siteName: '',
        supplierCode: null,
        supplierId: '',
        supplierName: '',
        warehouseCode: '',
        warehouseId: '',
        warehouseName: ''
      },
      companyList: [],
      addForm: {},
      radioData1: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],

      site: '',
      isExpand: true,
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        itemCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderType: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        buyerEnterpriseId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        isOutDirect: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        isOutSale: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        warehouseCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        cancelSiteCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        cancelType: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }]
      },
      expedited: '1', //待领料明细or手工创建
      siteOptions: [],

      userInfo: JSON.parse(sessionStorage.getItem('userInfo')),
      siteTenantList: [],
      edit: ''
    }
  },
  watch: {
    headerInfo: {
      handler(newVal) {
        console.log(newVal)
        this.edit = '1'
        this.topInfo.buyerEnterpriseId = newVal.buyerEnterpriseId
        this.topInfo.buyerOrgCode = newVal.buyerOrgCode
        this.topInfo.buyerOrgName = newVal.buyerOrgName

        this.topInfo.cancelSiteName = newVal.cancelSiteName
        this.topInfo.cancelSiteCode = newVal.cancelSiteCode

        this.topInfo.orderType = newVal.orderType

        this.topInfo.cancelAddress = newVal.cancelAddress
        this.topInfo.cancelOrderCode = newVal.cancelOrderCode
        this.topInfo.cancelType = String(newVal.cancelType)
        this.topInfo.id = newVal.id
        this.topInfo.isOutDirect = String(newVal.isOutDirect)
        this.topInfo.isOutSale = String(newVal.isOutSale)
        this.topInfo.materialSupplierCode = newVal.materialSupplierCode
        this.topInfo.materialSupplierId = newVal.materialSupplierId
        this.topInfo.materialSupplierName = newVal.materialSupplierName
        this.topInfo.remark = newVal.remark
        this.topInfo.siteCode = newVal.siteCode
        this.topInfo.siteId = newVal.siteId
        this.topInfo.siteName = newVal.siteName
        this.topInfo.supplierCode = newVal.supplierCode
        this.topInfo.supplierId = newVal.supplierId
        this.topInfo.supplierName = newVal.supplierName
        this.topInfo.warehouseId = newVal.warehouseId
        this.topInfo.warehouseCode = newVal.warehouseCode

        this.topInfo.warehouseName = newVal.warehouseName
      },
      deep: true
    }
  },
  mounted() {
    this.getSourceCompany()

    // this.purOrderQueryOrder();
    // this.init();

    // this.topInfo.supplierCode = this.userInfo.enterpriseCode;
    // this.topInfo.supplierName = this.userInfo.enterpriseName;
    // this.topInfo.supplierId = this.userInfo.enterpriseId;
    // this.topInfo.supplierCode = this.userInfo.enterpriseCode;
    // this.topInfo.supplierName = this.userInfo.enterpriseName;

    // this.topInfo.buyerCompanyOrgCode = this.userInfo.enterpriseCode;

    // 初始化时触发一次表头更新
    this.$nextTick(() => {
      this.$bus.$emit('isOutSaleChange', this.topInfo.isOutSale)
    })
  },
  methods: {
    init() {
      //   this.topInfo.remark = this.headerInfo.remark;
      this.isDisabled2 = true
      this.isDisabled = true
    },
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.topInfo.supplierId = itemData.id
        this.topInfo.supplierCode = itemData.supplierCode
        this.topInfo.supplierName = itemData.supplierName
      } else {
        this.topInfo.supplierId = null
        this.topInfo.supplierCode = null
        this.topInfo.supplierName = null
      }
      setTimeout(() => {
        this.radioChange()
      }, 10)
    },
    // 主数据 获取加工供应商
    getSupplier(args, companyCode) {
      const { text, updateData, setSelectData } = args
      let obj = {
        commonCode: companyCode || this.topInfo.buyerCode,
        fuzzyParam: text
      }
      this.$API.outsourcingNew
        .companyCodeGetSupplierList(obj)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    //库存地点
    warehouseClick(e) {
      this.topInfo.warehouseName = e.itemData.siteAddressName
      setTimeout(() => {
        this.radioChange()
      }, 10)
    },
    // 获取库存
    getWarehouseCodeOptions(args) {
      const { updateData, setSelectData, siteCode } = args
      const params = {
        id: this.topInfo.enterpriseId || '',
        param: {
          page: { current: 1, size: 9999 },
          condition: 'and',
          defaultRules: [
            {
              field: 'siteCode',
              operator: 'equal',
              value: siteCode || this.topInfo.siteCode
            }
          ]
        }
      }
      this.$API.outsourcingNew
        .getWarehouseList(params)
        .then((res) => {
          if (res) {
            const list = res?.data?.records || []
            this.cancelList = addCodeNameKeyInList({
              firstKey: 'siteAddress',
              secondKey: 'siteAddressName',
              list
            }).filter((item) => item.siteAddress)
            console.log(this.cancelList, 'cancelList')
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.cancelList)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
      if (this.headerInfo.warehouseCode) {
        setTimeout(() => {
          this.topInfo.warehouseCode = this.headerInfo.warehouseCode
          this.topInfo.warehouseName = this.headerInfo.warehouseName
        }, 500)
      }
    },
    // getWarehouseCodeOptions() {
    //   let obj = {
    //     enterpriseId: this.topInfo.buyerEnterpriseId,
    //     params: {
    //       page: {
    //         size: maxPageSize,
    //         current: 1
    //       },
    //       condition: 'and',
    //       defaultRules: [
    //         {
    //           // 工厂
    //           field: 'siteCode',
    //           operator: 'equal',
    //           value: this.topInfo.siteCode
    //         }
    //       ]
    //     }
    //   }
    //   this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
    //     res.data.records.forEach((item) => {
    //       item.label = item.siteAddress + `-` + item.siteAddressName
    //     })
    //     this.cancelList = res.data.records
    //   })
    //   if (this.headerInfo.warehouseCode) {
    //     setTimeout(() => {
    //       this.topInfo.warehouseCode = this.headerInfo.warehouseCode
    //       this.topInfo.warehouseName = this.headerInfo.warehouseName
    //     }, 500)
    //   }
    // },
    resoveClick() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('resolve', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 原材料供应商点击事件
    materialClick(val) {
      console.log(val)
      this.topInfo.materialSupplierCode = val.itemData?.supplierCode
      this.topInfo.materialSupplierId = val.itemData?.supplierId
      this.topInfo.materialSupplierName = val.itemData?.supplierName
      this.topInfo.cancelAddress = val.itemData?.cancelAddress
      // this.cancelAddressClick(val.itemData?.supplierCode)
      this.radioChange()
    },
    getCurrentUserInfo() {
      return this.userInfo?.username
    },
    cancelAddressClick(val) {
      let obj = {
        supplierCode: val
      }
      this.$API.outsourcing.supplierCancelAddress(obj).then((res) => {
        this.topInfo.cancelAddress = res.data.detailAddress
      })
    },
    // 当前日期
    getCurrentTime() {
      let yy = new Date().getFullYear()
      let mm = new Date().getMonth() + 1
      let dd = new Date().getDate()
      let hh = new Date().getHours()
      let mf =
        new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      let ss =
        new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      return yy + '/' + mm + '/' + dd + ' ' + hh + ':' + mf + ':' + ss
    },
    // 工厂点击
    siteOrgClick(e) {
      console.log(e.itemData)
      this.topInfo.buyerOrgId = e.itemData?.organizationId
      this.topInfo.siteCode = e.itemData?.siteCode
      this.topInfo.siteName = e.itemData?.siteName
      this.getWarehouseCodeOptions({ text: '', siteCode: e.itemData.siteCode })
      this.radioChange()
    },

    // 获取 原材料供应商
    getMaterialList(val, companyCode) {
      let obj = {
        commonCode: companyCode || this.topInfo.buyerCode,
        fuzzyParam: val
      }
      this.$API.outsourcingNew.companyCodeGetSupplierList(obj).then((res) => {
        const list = res?.data || []
        this.materialList = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        })
      })
    },
    getSourceMaterialSupplier(e) {
      this.getMaterialList(e.text)
    },

    radioChange() {
      sessionStorage.setItem('codeArr', JSON.stringify(this.topInfo))
      // 触发事件通知表头更新必填状态
      this.$bus.$emit('isOutSaleChange', this.topInfo.isOutSale)
    },
    submit() {
      const _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          _this.$emit('submitClick', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    companyClick(val) {
      console.log(val)
      if (val.e !== null) {
        // if (this.topInfo.buyerEnterpriseId) {
        //   this.$emit('deleteRe')
        // }

        this.topInfo.quantity = ''
        this.topInfo.siteCode = ''
        this.topInfo.siteName = ''
        this.topInfo.buyerOrgCode = ''
      }

      // this.$API.masterData
      //   .findInBuyingByCustomerCode({ customerCode: val.itemData.customerCode })
      //   .then((res) => {
      //     this.topInfo.supplierId = res.data.supplierId
      //     this.supplier = res.data.supplierCode + res.data.supplierName
      //     this.topInfo.supplierCode = res.data.supplierCode
      //     this.topInfo.supplierName = res.data.supplierName
      //   })
      this.topInfo.companyId = val.itemData.id
      this.customerEnterpriseId = val.itemData.enterpriseId
      this.topInfo.buyerOrgCode = val.itemData.orgCode
      this.topInfo.buyerOrgName = val.itemData.orgName
      this.topInfo.buyerCode = val.itemData.orgCode
      this.topInfo.buyerName = val.itemData.orgName
      this.topInfo.enterpriseId = val.itemData.enterpriseId
      const companyCode = val.itemData.orgCode
      this.getSourceSitesInVendor({})
      this.getMaterialList('', companyCode) // 获取原材料
      this.getSupplier({ text: null }, companyCode)
    },
    getSourceCompany() {
      let obj = {
        fuzzyNameOrCode: ''
      }

      this.$API.outsourcingNew.queryCustomCompanyList(obj).then((res) => {
        res.data.forEach((item) => {
          item.name = item.orgCode + '-' + item.orgName
          item.code = item.enterpriseId
        })
        this.companyList = res.data
        // this.topInfo.buyerCode = this.headerInfo.buyerCode;
        // this.topInfo.buyerEnterpriseId = this.headerInfo.buyerEnterpriseId;
        // this.topInfo.buyerName = this.headerInfo.buyerName;
        // console.log("----------", this.headerInfo.buyerCode, this.topInfo);
      })
    },
    getSourceCompany1(e) {
      var searchData = this.companyList
      // this.purOrderQueryOrder(e.text);

      // load overall data when search key empty.
      if (e.text == '') e.updateData(searchData)
      else {
        let query = new Query().select(['name', 'code', 'orgCode', 'orgName', 'enterpriseId', 'id'])
        // change the type of filtering
        query = e.text !== '' ? query.where('name', 'contains', e.text, true) : query
        console.log(query)
        e.updateData(searchData, query)
        console.log(searchData)
      }
    },
    formFun(rule, value, callback) {
      console.log(rule, value, callback)
    },
    orderTypeChange() {
      this.radioChange()
    },
    whitch(e) {
      this.radioChange()

      if (e === '0') {
        this.topInfo.materialSupplierCode = ''
        this.topInfo.materialSupplierName = ''
      }
    },

    // 获取主数据-工厂
    getSourceSitesInVendor(args) {
      const { text, updateData } = args
      const params = {
        organizationId: this.topInfo.companyId,
        fuzzyParam: text || ''
        // dataLimit: 10000,
      }
      this.$API.outsourcingNew
        .companyGetsiteList(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.siteOptions = addCodeNameKeyInList({
              firstKey: 'siteCode',
              secondKey: 'siteName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.siteOptions)
              })
            }
          }
        })
        .catch(() => {})
    },
    goBack() {
      this.$router.go(-1)
    },
    // save() {
    //   console.log(this.topInfo);
    //   this.topInfo = this.getOtherInfo(this.topInfo);
    //   this.addPicking(this.topInfo);
    // },
    addPicking(params) {
      params.siteName = params.siteOrgName
      params.siteCode = params.siteOrgCode
      params.buyerCompanyOrgCode = params.companyOrgCode
      params.buyerCompanyOrgId = params.companyOrgId
      params.buyerCompanyOrgName = params.companyOrgName
      params.buyerTenantId = params.tenantId
      params.orderDetailRequestList = []
      this.$API.outsourcing.addPicking(params).then(() => {})
    },
    expandChange() {
      this.isExpand = !this.isExpand
      // this.$refs.ruleForm.clearValidate();
    },
    onchang(e) {
      console.log('onchang', e, this.expedited)
    },

    // 获取其他数据 code、name
    getOtherInfo(params) {
      // 业务类型、申请人、公司、申请部门、采购组织
      if (this.topInfo.siteId && this.$refs.businessRef) {
        let _data = this.$refs.businessRef.ejsRef.getDataByValue(this.topInfo.siteId)
        // console.log("params", _data);
        for (let key in _data) {
          params[key] = _data[key]
        }
        if (!_data) return
      }
      console.log('params', params)
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin: 20px 20px 20px 0;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itemcon {
      display: flex;
      align-items: center;
    }
    .item {
      margin-right: 20px;
    }
    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      // &.more-width {
      //   // width: 450px;
      // }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
