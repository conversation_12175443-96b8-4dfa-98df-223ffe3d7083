<!-- 替代料申请管理-列表视图 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="false"
      grid-id="f4a26b0c-abec-4a16-b128-a7391ab59d33"
      search-grid-id="989c961a-0f4d-408b-ac84-930928cd2794"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getListSearchFormItems } from '../config/searchForm'
import { listColumnData, toolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: listColumnData,
      toolbar
    }
  },
  computed: {
    searchConditions() {
      const items = getListSearchFormItems()
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'createTime':
        case 'updateTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm }) {
      this.searchForm = { ...this.searchForm, ...searchForm }
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.getTableData()
    },
    async getTableData() {
      const params = {
        ...this.searchForm
      }
      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.outsourcingNew
        .getSubstituteMaterialApplicationListApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res?.code === 200) {
        const records = res.data?.records || []
        this.$refs.commonListRef.setTableData(records)
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        const res = await this.$API.outsourcingNew.exportSubstituteMaterialApplicationListApi(
          params
        )
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
