<template>
  <div>
    <mt-select
      :id="data.column.field"
      v-model="data[data.column.field]"
      :data-source="dataSource"
      :fields="{ text: 'codeAndName', value: 'value' }"
      :placeholder="placeholder"
      @change="selectChange"
      :open-dispatch-change="true"
      :disabled="isDisabled"
      :allow-filtering="true"
      :filtering="postChange"
    ></mt-select>
  </div>
</template>

<script>
export default {
  name: 'PurchaseOrderSelect',
  data() {
    return {
      data: {},
      placeholder: this.$t('请选择'),
      dataSource: [],
      isDisabled: false,
      codeArr: JSON.parse(sessionStorage.getItem('codeArr') || '{}'),
      sapComponentData: [], // 存储SAP组件明细数据，用于采购订单行号联动
      getorderItemNoOptionsTimer: null // 防抖定时器
    }
  },
  mounted() {
    this.codeArr = JSON.parse(sessionStorage.getItem('codeArr') || '{}')

    // 根据字段类型初始化数据源
    if (this.data.column.field === 'orderCode') {
      // 主动检查同行的物料编码
      this.checkAndUpdateItemCodeFromRow()

      if (this.data.itemCode) {
        this.getOrderCodeOptions()
      }
    } else if (this.data.column.field === 'orderItemNo') {
      // 主动获取当前行的完整数据
      this.loadCurrentRowData()

      // 主动检查同行的物料编码
      this.checkAndUpdateItemCodeFromRow()

      // 直接获取采购订单行号选项
      this.getorderItemNoOptions()
    }

    // 监听物料编码变化专门用于采购订单号字段的事件
    this.$bus.$on('itemCodeChangeForOrderCode', this.handleItemCodeChangeForOrderCode)
    // 监听其他字段请求物料编码的事件
    this.$bus.$on('requestItemCodeForRow', this.handleRequestItemCodeForRow)
    // 监听物料编码响应事件
    this.$bus.$on('responseItemCodeForRow', this.handleResponseItemCodeForRow)
    // 监听采购订单号变化事件
    this.$bus.$on('orderCodeChange', this.handleOrderCodeChange)
    // 监听父组件的采购订单号选择事件
    this.$parent.$on('orderCodeSelected', this.handleOrderCodeSelected)
    // 监听全局采购订单号选择事件
    this.$bus.$on('orderCodeSelectedGlobal', this.handleOrderCodeSelectedGlobal)
  },
  methods: {
    // 主动获取当前行的完整数据
    loadCurrentRowData() {
      // 尝试通过父组件获取行数据
      if (this.$parent && this.$parent.$refs && this.$parent.$refs.grid) {
        try {
          const grid = this.$parent.$refs.grid
          const rowIndex = this.data.addId || this.data.id

          // 尝试多种方式获取行数据
          let rowData = null

          // 方式1：通过getRowByIndex
          if (grid.getRowByIndex && typeof rowIndex === 'number') {
            rowData = grid.getRowByIndex(rowIndex)
          }

          // 方式2：通过dataSource查找
          if (!rowData && grid.dataSource) {
            rowData = grid.dataSource.find((row) => row.addId === rowIndex || row.id === rowIndex)
          }

          // 方式3：通过getCurrentViewRecords
          if (!rowData && grid.getCurrentViewRecords) {
            const allRows = grid.getCurrentViewRecords()
            rowData = allRows.find((row) => row.addId === rowIndex || row.id === rowIndex)
          }

          if (rowData) {
            // 更新组件数据
            Object.keys(rowData).forEach((key) => {
              if (rowData[key] !== undefined && rowData[key] !== null) {
                this.$set(this.data, key, rowData[key])
              }
            })
          }
        } catch (error) {
          console.error('获取行数据失败:', error)
        }
      }

      // 如果还是没有数据，尝试通过事件获取
      if (!this.data.orderItemNo) {
        this.$bus.$emit('requestRowData', {
          rowIndex: this.data.addId || this.data.id,
          requestField: this.data.column.field
        })
      }
    },

    // 模糊搜索 - 适配mt-select的filtering事件
    postChange(e) {
      if (this.data.column.field === 'orderCode') {
        // 对于采购订单号，进行本地过滤
        if (e.text) {
          const filteredData = this.dataSource.filter(
            (item) => item.codeAndName && item.codeAndName.includes(e.text)
          )
          e.updateData(filteredData)
        } else {
          e.updateData(this.dataSource)
        }
      } else if (this.data.column.field === 'orderItemNo') {
        // 对于采购订单行号，进行本地过滤
        if (e.text) {
          const filteredData = this.dataSource.filter(
            (item) => item.codeAndName && item.codeAndName.includes(e.text)
          )
          e.updateData(filteredData)
        } else {
          e.updateData(this.dataSource)
        }
      }
    },

    // 获取采购订单号下拉数据
    getOrderCodeOptions(searchText = '') {
      if (this.data.itemCode) {
        // 首先尝试从已有数据或sessionStorage获取SAP组件数据
        let sapData = this.sapComponentData
        if (!sapData || sapData.length === 0) {
          const storageKey = `sapComponentData_${this.data.itemCode}`
          try {
            const storedData = sessionStorage.getItem(storageKey)
            if (storedData) {
              sapData = JSON.parse(storedData)
              this.sapComponentData = sapData
            }
          } catch (e) {
            sapData = []
          }
        }

        if (sapData && sapData.length > 0) {
          this.generateOrderCodeOptions(sapData, searchText)
          return
        }

        const params = {
          buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
          createType: 2,
          itemCode: this.data.itemCode,
          buyerOrgCode: this.codeArr.buyerOrgCode,
          isOutSale: this.codeArr.isOutSale,
          id: this.data?.id,
          siteCode: this.codeArr.siteCode,
          supplierCode: this.codeArr.supplierCode
        }

        this.$API.outsourcing
          .getSapComponentDetails(params)
          .then((res) => {
            this.sapComponentData = res.data || []

            const storageKey = `sapComponentData_${this.data.itemCode}`
            sessionStorage.setItem(storageKey, JSON.stringify(this.sapComponentData))

            this.generateOrderCodeOptions(this.sapComponentData, searchText)
          })
          .catch((error) => {
            console.error('获取SAP组件数据失败:', error)
            this.dataSource = []
          })
      } else {
        this.dataSource = []
      }
    },

    // 生成采购订单号选项
    generateOrderCodeOptions(sapData, searchText = '', callback = null) {
      if (!sapData || sapData.length === 0) {
        this.dataSource = []
        if (callback) callback()
        return
      }

      // 提取唯一的采购订单号
      let uniqueOrderCodes = [...new Set(sapData.map((item) => item.orderCode).filter(Boolean))]

      // 如果有搜索文本，进行过滤
      if (searchText) {
        uniqueOrderCodes = uniqueOrderCodes.filter((orderCode) => orderCode.includes(searchText))
      }

      this.dataSource = uniqueOrderCodes.map((orderCode) => ({
        orderCode,
        value: orderCode,
        codeAndName: orderCode,
        text: orderCode
      }))

      // 解决时序问题：在数据源设置后，重新设置字段值以触发组件更新
      this.$nextTick(() => {
        const currentValue = this.data[this.data.column.field]
        if (currentValue) {
          // 临时清空值
          this.data[this.data.column.field] = ''
          // 在下一个tick重新设置值
          this.$nextTick(() => {
            this.data[this.data.column.field] = currentValue
          })
        }
      })

      if (callback) callback()
    },

    // 获取采购订单行号下拉数据 - 从已有数据中筛选，不调用接口
    getorderItemNoOptions(searchText = '') {
      // 首先尝试从已有数据或sessionStorage获取SAP组件数据
      let sapData = this.sapComponentData
      if (!sapData || sapData.length === 0) {
        const storageKey = `sapComponentData_${this.data.itemCode}`
        try {
          const storedData = sessionStorage.getItem(storageKey)
          if (storedData) {
            sapData = JSON.parse(storedData)
            this.sapComponentData = sapData
          }
        } catch (e) {
          sapData = []
        }
      }

      // 如果没有SAP数据且有物料编码，主动获取SAP组件数据
      if ((!sapData || sapData.length === 0) && this.data.itemCode) {
        const params = {
          buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
          createType: 2,
          itemCode: this.data.itemCode,
          buyerOrgCode: this.codeArr.buyerOrgCode,
          isOutSale: this.codeArr.isOutSale,
          id: this.data?.id,
          siteCode: this.codeArr.siteCode,
          supplierCode: this.codeArr.supplierCode
        }

        this.$API.outsourcing
          .getSapComponentDetails(params)
          .then((res) => {
            this.sapComponentData = res.data || []
            const storageKey = `sapComponentData_${this.data.itemCode}`
            sessionStorage.setItem(storageKey, JSON.stringify(this.sapComponentData))
            // 递归调用以使用新获取的数据
            this.getorderItemNoOptions(searchText)
          })
          .catch((error) => {
            console.error('获取SAP组件数据失败:', error)
            this.dataSource = []
          })
        return
      }

      if (this.data.orderCode && sapData && sapData.length > 0) {
        // 过滤出当前选择的采购订单号对应的行号
        let orderItems = sapData.filter((item) => item.orderCode === this.data.orderCode)

        // 如果有搜索文本，进行过滤
        if (searchText) {
          orderItems = orderItems.filter(
            (item) => item.orderItemNo && item.orderItemNo.includes(searchText)
          )
        }

        // 提取唯一的订单行号
        const uniqueorderItemNos = [
          ...new Set(orderItems.map((item) => item.orderItemNo).filter(Boolean))
        ]

        this.dataSource = uniqueorderItemNos.map((orderItemNo) => ({
          orderItemNo,
          value: orderItemNo,
          codeAndName: orderItemNo,
          text: orderItemNo
        }))

        // 解决时序问题：在数据源设置后，重新设置字段值以触发组件更新
        this.$nextTick(() => {
          // 尝试多种方式获取当前值
          const currentValue = this.data[this.data.column.field] || this.data.orderItemNo
          const valueExistsInDataSource = this.dataSource.some(
            (item) => item.value === currentValue
          )

          if (currentValue && valueExistsInDataSource) {
            // 临时清空值
            this.data[this.data.column.field] = ''
            // 在下一个tick重新设置值
            this.$nextTick(() => {
              this.data[this.data.column.field] = currentValue
            })
          } else if (currentValue && !valueExistsInDataSource) {
            this.data[this.data.column.field] = ''
          }
        })
      } else {
        this.dataSource = []
      }
    },

    selectChange(val) {
      // 采购订单号选择处理
      if (this.data.column.field === 'orderCode') {
        // 增强数据获取逻辑
        let orderCode = null
        if (val.itemData) {
          orderCode = val.itemData.orderCode || val.itemData.value || val.itemData.codeAndName
        } else if (val.value) {
          orderCode = val.value
        } else if (val.orderCode) {
          orderCode = val.orderCode
        }

        if (orderCode) {
          // 使用$set确保响应式更新
          this.$set(this.data, 'orderCode', orderCode)

          // 临时清空采购订单行号
          this.$set(this.data, 'orderItemNo', '')

          // 触发采购订单号变化事件
          this.$bus.$emit('orderCodeChange', {
            orderCode: orderCode,
            rowIndex: this.data.addId || this.data.id,
            itemCode: this.data.itemCode // 添加物料编码信息
          })

          // 通过父组件事件通知同一行的采购订单行号组件
          this.$nextTick(() => {
            this.$parent.$emit('orderCodeSelected', {
              orderCode: orderCode,
              rowIndex: this.data.addId || this.data.id,
              itemCode: this.data.itemCode
            })
          })

          // 额外通过全局事件确保所有相关组件都能收到通知
          this.$nextTick(() => {
            this.$bus.$emit('orderCodeSelectedGlobal', {
              orderCode: orderCode,
              rowIndex: this.data.addId || this.data.id,
              itemCode: this.data.itemCode
            })
          })

          // 强制触发表单更新
          this.$nextTick(() => {
            // 触发父组件的数据变更事件
            this.$parent.$emit('dataChanged', {
              field: this.data.column.field,
              value: orderCode,
              rowData: this.data
            })

            this.$forceUpdate()
          })
        } else {
          return
        }
      }

      // 采购订单行号选择处理
      if (this.data.column.field === 'orderItemNo') {
        // 增强数据获取逻辑
        let orderItemNo = null
        if (val.itemData) {
          orderItemNo = val.itemData.orderItemNo || val.itemData.value || val.itemData.codeAndName
        } else if (val.value) {
          orderItemNo = val.value
        } else if (val.orderItemNo) {
          orderItemNo = val.orderItemNo
        }

        if (orderItemNo) {
          // 使用$set确保响应式更新
          this.$set(this.data, 'orderItemNo', orderItemNo)

          // 强制触发表单更新
          this.$nextTick(() => {
            // 触发父组件的数据变更事件
            this.$parent.$emit('dataChanged', {
              field: this.data.column.field,
              value: orderItemNo,
              rowData: this.data
            })

            this.$forceUpdate()
          })
        } else {
          return
        }
      }
    },

    // 主动检查并更新同行的物料编码
    checkAndUpdateItemCodeFromRow() {
      // 通过父组件获取同行的物料编码
      if (this.$parent && this.$parent.getRowData) {
        try {
          const rowData = this.$parent.getRowData(this.data.addId || this.data.id)
          if (rowData && rowData.itemCode && !this.data.itemCode) {
            this.data.itemCode = rowData.itemCode
          }
        } catch (error) {
          // 无法通过父组件获取行数据，尝试事件方式
        }
      }

      // 如果父组件方法不可用，尝试通过事件获取
      if (!this.data.itemCode) {
        this.$bus.$emit('requestItemCodeForRow', {
          rowIndex: this.data.addId || this.data.id,
          requestField: this.data.column.field
        })
      }
    },

    // 处理其他字段请求物料编码的事件
    handleRequestItemCodeForRow(data) {
      // 只有物料编码字段且是同一行才响应
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'itemCode' &&
        this.data.itemCode
      ) {
        // 直接发送物料编码给请求的字段
        this.$bus.$emit('responseItemCodeForRow', {
          rowIndex: this.data.addId || this.data.id,
          itemCode: this.data.itemCode,
          targetField: data.requestField
        })
      }
    },

    // 处理物料编码响应事件
    handleResponseItemCodeForRow(data) {
      // 只有目标字段且是同一行才处理
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        data.targetField === this.data.column.field &&
        data.itemCode
      ) {
        // 更新物料编码
        this.data.itemCode = data.itemCode

        // 如果是采购订单号字段，重新获取数据
        if (this.data.column.field === 'orderCode') {
          this.getOrderCodeOptions()
        }
      }
    },

    // 处理物料编码变化专门用于采购订单号字段的事件
    handleItemCodeChangeForOrderCode(data) {
      // 如果没有匹配到任何字段，跳过处理
      if (data.rowIndex !== (this.data.addId || this.data.id)) {
        return
      }
      if (this.data.column.field !== 'orderCode' && this.data.column.field !== 'orderItemNo') {
        return
      }

      // 处理采购订单号字段
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderCode'
      ) {
        // 更新物料编码
        this.data.itemCode = data.itemCode

        // 清空当前采购订单号
        this.data.orderCode = ''

        // 清空数据源并重新获取数据
        this.dataSource = []

        // 重新获取采购订单号选项
        this.$nextTick(() => {
          this.getOrderCodeOptions()
        })
      }

      // 处理采购订单行号字段
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderItemNo'
      ) {
        // 更新物料编码
        this.data.itemCode = data.itemCode

        // 只在新增模式下清空采购订单行号，编辑模式下保留现有值
        const isEditMode = this.data.orderItemNo && this.data.orderItemNo.trim() !== ''
        if (!isEditMode) {
          this.data.orderItemNo = ''
        }

        // 清空数据源
        this.dataSource = []

        // 如果有采购订单号，重新获取采购订单行号选项
        if (this.data.orderCode) {
          this.$nextTick(() => {
            this.getorderItemNoOptions()
          })
        }
      }
    },

    // 处理采购订单号变化事件
    handleOrderCodeChange(data) {
      if (data.rowIndex === (this.data.addId || this.data.id)) {
        // 更新当前行的采购订单号
        this.data.orderCode = data.orderCode

        if (this.data.column.field === 'orderItemNo') {
          // 如果当前是采购订单行号字段，保存原值并重新获取数据
          const originalOrderItemNo = this.data.orderItemNo

          this.data.orderItemNo = ''
          this.dataSource = []

          // 重新获取采购订单行号数据，并尝试恢复原值
          this.$nextTick(() => {
            this.getorderItemNoOptions()

            // 在数据源更新后尝试恢复原值
            this.$nextTick(() => {
              if (
                originalOrderItemNo &&
                this.dataSource.some((item) => item.value === originalOrderItemNo)
              ) {
                this.data.orderItemNo = originalOrderItemNo
              }
            })
          })
        }
      }
    },

    // 处理父组件的采购订单号选择事件
    handleOrderCodeSelected(data) {
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderItemNo'
      ) {
        // 更新采购订单号
        this.data.orderCode = data.orderCode
        // 保存并清空采购订单行号
        const originalOrderItemNo = this.data.orderItemNo
        this.data.orderItemNo = ''

        // 重新获取采购订单行号数据，并尝试恢复原值
        this.getorderItemNoOptions()
        this.$nextTick(() => {
          if (
            originalOrderItemNo &&
            this.dataSource.some((item) => item.value === originalOrderItemNo)
          ) {
            this.data.orderItemNo = originalOrderItemNo
          }
        })
      }
    },

    // 处理全局采购订单号选择事件
    handleOrderCodeSelectedGlobal(data) {
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderItemNo' &&
        data.itemCode === this.data.itemCode
      ) {
        // 更新采购订单号
        this.data.orderCode = data.orderCode
        // 保存并清空采购订单行号
        const originalOrderItemNo = this.data.orderItemNo
        this.data.orderItemNo = ''

        // 重新获取采购订单行号数据，并尝试恢复原值
        this.getorderItemNoOptions()
        this.$nextTick(() => {
          if (
            originalOrderItemNo &&
            this.dataSource.some((item) => item.value === originalOrderItemNo)
          ) {
            this.data.orderItemNo = originalOrderItemNo
          }
        })
      }
    }
  },

  beforeDestroy() {
    // 清理事件监听
    this.$bus.$off('itemCodeChangeForOrderCode', this.handleItemCodeChangeForOrderCode)
    this.$bus.$off('requestItemCodeForRow', this.handleRequestItemCodeForRow)
    this.$bus.$off('responseItemCodeForRow', this.handleResponseItemCodeForRow)
    this.$bus.$off('orderCodeChange', this.handleOrderCodeChange)
    this.$parent.$off('orderCodeSelected', this.handleOrderCodeSelected)
    this.$bus.$off('orderCodeSelectedGlobal', this.handleOrderCodeSelectedGlobal)

    // 清理定时器
    if (this.getorderItemNoOptionsTimer) {
      clearTimeout(this.getorderItemNoOptionsTimer)
      this.getorderItemNoOptionsTimer = null
    }

    // 清理组件数据
    this.codeArr = null
    this.dataSource = []
    this.sapComponentData = []
  }
}
</script>
