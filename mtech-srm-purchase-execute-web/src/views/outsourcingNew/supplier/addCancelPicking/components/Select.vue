<template>
  <div>
    <!-- 物料编码使用弹框选择方式 -->
    <div v-if="data.column.field === 'itemCode'" class="in-cell">
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="postChange"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'itemCode' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>

    <!-- 其他字段使用普通下拉选择 -->
    <div v-else>
      <mt-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'value' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
        :filtering="postChange"
      ></mt-select>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { PROXY_MDM_AUTH } from '@/utils/constant'
// import { utils } from "@mtech-common/utils";
import { maxPageSize } from '@/utils/constant'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '3aee5144-1201-4eb5-8a0d-8b5792261a9a',
          grid: {
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      codeArr: JSON.parse(sessionStorage.getItem('codeArr') || '{}')
    }
  },
  mounted() {
    this.codeArr = JSON.parse(sessionStorage.getItem('codeArr') || '{}')

    // 物料编码字段初始化
    if (this.data.column.field === 'itemCode') {
      const currentItemCode = this.data.itemCode
      console.log('物料编码字段初始化，当前值:', currentItemCode)

      if (currentItemCode) {
        // 如果有初始值，先获取包含该物料编码的数据
        this.getCategoryItem(currentItemCode)

        // 同时确保当前值在数据源中（处理编辑模式下的回显）
        this.$nextTick(() => {
          this.ensureCurrentItemInDataSource(currentItemCode)
        })
      } else {
        // 如果没有初始值，获取默认数据
        this.getCategoryItem('')
      }
    }

    // 库存地点字段初始化
    if (this.data.column.field === 'warehouseCode') {
      this.getWarehouseCodeOptions()
    }

    // 监听其他字段请求物料编码的事件
    this.$bus.$on('requestItemCodeForRow', this.handleRequestItemCodeForRow)
    // 监听物料编码响应事件
    this.$bus.$on('responseItemCodeForRow', this.handleResponseItemCodeForRow)
  },
  methods: {
    recordDoubleClick(args) {
      this.selectChange({ itemData: args.rowData })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm(_, records) {
      console.log('=== confirm方法开始执行 ===')
      console.log('传入的records参数:', records)

      // 如果没有传入records或records为空，尝试从表格获取选中记录
      if (!records || records.length <= 0) {
        console.log('未传入records，尝试从表格获取选中记录')

        // 增强数据获取的健壮性，逐步检查每个环节
        const templateRef = this.$refs.templateRef
        console.log('templateRef:', templateRef)

        if (!templateRef) {
          console.error('templateRef不存在')
          this.$toast({
            content: this.$t('获取表格引用失败'),
            type: 'warning'
          })
          return
        }

        const currentUsefulRef = templateRef.getCurrentUsefulRef()
        console.log('currentUsefulRef:', currentUsefulRef)

        if (!currentUsefulRef) {
          console.error('currentUsefulRef不存在')
          this.$toast({
            content: this.$t('获取表格实例失败'),
            type: 'warning'
          })
          return
        }

        const ejsRef = currentUsefulRef.ejsRef
        console.log('ejsRef:', ejsRef)

        if (!ejsRef) {
          console.error('ejsRef不存在')
          this.$toast({
            content: this.$t('获取表格组件失败'),
            type: 'warning'
          })
          return
        }

        // 尝试获取选中记录
        try {
          records = ejsRef.getSelectedRecords()
          console.log('从表格获取的选中记录:', records)
        } catch (error) {
          console.error('获取选中记录时发生错误:', error)
          this.$toast({
            content: this.$t('获取选中记录失败'),
            type: 'warning'
          })
          return
        }
      }

      // 检查是否有选中记录
      if (!records || records.length <= 0) {
        console.warn('没有选中任何记录')
        this.$toast({
          content: this.$t('请先选择一条记录'),
          type: 'warning'
        })
        return
      }

      console.log('最终使用的records:', records)
      console.log('选中的第一条记录:', records[0])

      // 调用selectChange处理选中数据
      this.selectChange({ itemData: records[0] })

      // 确保弹框选择后的数据能正确回显
      this.$nextTick(() => {
        console.log('confirm方法执行完成后强制更新组件')
        this.$forceUpdate()
      })
    },
    showDialog() {
      this.pageConfig[0].grid.asyncConfig = {
        url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        recordsPosition: 'data.records',
        params: {
          customerEnterpriseId: this.codeArr.buyerEnterpriseId,
          organizationCode: this.codeArr.siteCode
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    // 获取库存地点
    getWarehouseCodeOptions() {
      let obj = {
        enterpriseId: this.codeArr.buyerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: this.codeArr.siteCode
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.siteAddressName //
          item.value = item.siteAddress //
        })
        this.dataSource = res.data.records.map((i) => {
          return {
            ...i,
            codeAndName: `${i.siteAddress} - ${i.siteAddressName}`
          }
        })
      })
    },
    // 模糊搜索 - 适配mt-select的filtering事件
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text)
      } else {
        // 对于其他字段，进行本地过滤
        if (e.text) {
          const filteredData = this.dataSource.filter(
            (item) => item.codeAndName && item.codeAndName.includes(e.text)
          )
          e.updateData(filteredData)
        } else {
          e.updateData(this.dataSource)
        }
      }
    },

    getCategoryItem(e, callback = null) {
      //物料下拉
      let obj = {
        page: {
          current: 1,
          size: 20
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
        ],
        customerEnterpriseId: this.codeArr.buyerEnterpriseId,
        organizationCode: this.codeArr.siteCode
      }
      this.$API.masterData
        .getOrgRel(obj)
        .then((res) => {
          console.log('物料编码接口返回原始数据:', res.data.records)

          this.dataSource =
            res.data.records.map((i) => {
              const item = {
                ...i,
                codeAndName: `${i.itemCode} - ${i.itemName}`,
                itemCode: i.itemCode, // 作为value字段
                name: i.itemCode,
                value: i.itemCode,
                text: `${i.itemCode} - ${i.itemName}`
              }
              return item
            }) || []

          console.log('处理后的物料编码数据源:', this.dataSource)
          console.log('数据源第一项结构:', this.dataSource[0])

          // 如果是基于选中项的查询（精确匹配），确保查询结果包含选中项
          if (e && this.dataSource.length > 0) {
            console.log('基于选中项查询完成，当前物料编码值:', this.data[this.data.column.field])

            // 强制更新组件以确保选中项正确回显
            this.$nextTick(() => {
              console.log('查询完成后强制更新组件以确保回显')
              this.$forceUpdate()
            })
          }

          // 调用回调函数
          if (callback && typeof callback === 'function') {
            console.log('调用回调函数')
            callback()
          }
        })
        .catch((error) => {
          console.error('物料编码查询接口调用失败:', error)
          this.dataSource = []

          // 即使出错也要调用回调函数
          if (callback && typeof callback === 'function') {
            console.log('接口调用失败，仍然调用回调函数')
            callback()
          }
        })
    },

    // 根据选中的物料编码调用查询接口更新dataSource
    ensureItemInDataSource(itemData, callback = null) {
      console.log('=== ensureItemInDataSource方法开始执行 ===')
      console.log('根据选中物料编码调用查询接口更新dataSource:', itemData)

      if (!itemData || !itemData.itemCode) {
        console.warn('itemData或itemCode为空，跳过处理')
        if (callback && typeof callback === 'function') {
          callback()
        }
        return
      }

      // 根据选中的物料编码调用查询物料接口
      console.log('调用getCategoryItem接口，以选中的物料编码为关键词:', itemData.itemCode)
      this.getCategoryItem(itemData.itemCode, callback)
    },

    // 确保当前物料编码值在数据源中存在（用于编辑模式回显）
    ensureCurrentItemInDataSource(itemCode) {
      console.log('=== ensureCurrentItemInDataSource方法开始执行 ===')
      console.log('检查当前物料编码是否在数据源中:', itemCode)

      if (!itemCode) {
        console.warn('itemCode为空，跳过处理')
        return
      }

      // 检查当前数据源中是否已存在该物料编码
      const existingItem = this.dataSource.find((item) => item.itemCode === itemCode)

      if (existingItem) {
        console.log('当前物料编码已存在于数据源中:', itemCode)
        return
      }

      console.log('当前物料编码不在数据源中，需要获取详细信息并添加:', itemCode)

      // 调用接口获取该物料的详细信息
      const obj = {
        page: {
          current: 1,
          size: 1
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'equal',
            value: itemCode
          }
        ],
        customerEnterpriseId: this.codeArr.buyerEnterpriseId,
        organizationCode: this.codeArr.siteCode
      }

      this.$API.masterData
        .getOrgRel(obj)
        .then((res) => {
          console.log('获取当前物料详细信息返回:', res.data.records)

          if (res.data.records && res.data.records.length > 0) {
            const itemData = res.data.records[0]

            // 构造符合数据源格式的物料项
            const newItem = {
              ...itemData,
              codeAndName: `${itemData.itemCode} - ${itemData.itemName}`,
              itemCode: itemData.itemCode,
              name: itemData.itemCode,
              value: itemData.itemCode,
              text: `${itemData.itemCode} - ${itemData.itemName}`
            }

            // 将物料添加到数据源的开头
            this.dataSource.unshift(newItem)

            console.log('当前物料编码已添加到数据源:', newItem)

            // 强制更新组件
            this.$nextTick(() => {
              this.$forceUpdate()
            })
          } else {
            console.warn('未找到当前物料编码的详细信息:', itemCode)
          }
        })
        .catch((error) => {
          console.error('获取当前物料详细信息失败:', error)
        })
    },

    selectChange(val) {
      console.log('=== selectChange方法开始执行 ===')
      console.log('传入的val参数:', val)
      console.log('当前字段:', this.data.column.field)

      if (this.data.column.field === 'itemCode') {
        // 处理物料编码选择
        console.log('处理物料编码选择')

        // 增强数据获取逻辑，支持多种数据结构
        let itemCode = null
        let itemData = null

        // 优先从val.itemData获取数据（弹框选择的情况）
        if (val.itemData) {
          itemData = val.itemData
          itemCode = itemData.itemCode || itemData.value || val.value
          console.log('从itemData获取物料编码:', itemCode)
        }
        // 其次从val.value获取（下拉选择的情况）
        else if (val.value) {
          itemCode = val.value
          console.log('从value获取物料编码:', itemCode)
        }
        // 最后尝试直接从val获取（兼容其他情况）
        else if (val.itemCode) {
          itemCode = val.itemCode
          console.log('直接从val获取物料编码:', itemCode)
        }

        console.log('最终确定的物料编码:', itemCode)

        // 检查是否是相同的物料编码（避免重复处理）
        const currentItemCode = this.data[this.data.column.field]
        const hasCurrentValue = currentItemCode && currentItemCode.trim() !== ''
        const hasOrderCode = this.data.orderCode && this.data.orderCode.trim() !== ''
        const isSameItemCode = hasCurrentValue && currentItemCode === itemCode && hasOrderCode
        console.log('当前物料编码:', currentItemCode)
        console.log('新选择的物料编码:', itemCode)
        console.log('hasCurrentValue:', hasCurrentValue)
        console.log('hasOrderCode:', hasOrderCode)
        console.log('currentItemCode === itemCode:', currentItemCode === itemCode)
        console.log('是否为相同物料编码选择:', isSameItemCode)

        if (itemCode) {
          console.log('开始更新物料编码到表单')

          // 定义更新所有相关字段的函数
          const updateAllFields = () => {
            // 设置物料编码
            console.log('设置物料编码:', itemCode)
            this.$set(this.data, this.data.column.field, itemCode)

            // 如果有itemData，同时更新物料名称等相关字段
            if (itemData) {
              console.log('同时更新相关字段，itemData:', itemData)

              // 更新物料名称（如果存在）
              if (
                itemData.itemName &&
                Object.prototype.hasOwnProperty.call(this.data, 'itemName')
              ) {
                this.$set(this.data, 'itemName', itemData.itemName)
                console.log('更新物料名称:', itemData.itemName)
              }

              // 更新其他可能的字段
              if (
                itemData.itemUnitDescription &&
                Object.prototype.hasOwnProperty.call(this.data, 'itemUnitDescription')
              ) {
                this.$set(this.data, 'itemUnitDescription', itemData.itemUnitDescription)
                console.log('更新单位:', itemData.itemUnitDescription)
              }
            }

            // 只有当物料编码发生变化时才清空采购订单号和采购订单行号
            if (!isSameItemCode) {
              console.log('物料编码发生变化，清空采购订单号和采购订单行号')
              this.$set(this.data, 'orderCode', '')
              this.$set(this.data, 'orderItemNo', '')
            } else {
              console.log('物料编码未变化，保持采购订单号和采购订单行号不变')
            }

            console.log('更新后的data[itemCode]:', this.data[this.data.column.field])

            // 强制更新组件以确保回显
            this.$nextTick(() => {
              console.log('所有字段更新完成后强制更新组件')

              // 触发父组件的数据变更事件
              this.$parent.$emit('dataChanged', {
                field: this.data.column.field,
                value: itemCode,
                rowData: this.data
              })

              this.$forceUpdate()
            })
          }

          // 如果有itemData，先确保选中的物料编码在下拉选项中存在，然后再设置值
          if (itemData) {
            console.log('先确保选中的物料编码在下拉选项中存在:', itemData)
            // 调用查询接口更新dataSource，并在完成后更新所有字段
            this.ensureItemInDataSource(itemData, updateAllFields)
          } else {
            // 如果没有itemData，直接更新所有字段
            updateAllFields()
          }
        } else {
          console.warn('未能获取到有效的物料编码')
          this.$toast({
            content: this.$t('获取物料编码失败，请重新选择'),
            type: 'warning'
          })
          return
        }

        // 只有当物料编码发生变化时才调用相关接口和触发事件
        if (itemCode && !isSameItemCode) {
          console.log('物料编码发生变化，调用相关接口和触发事件')

          let obj = {
            buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
            createType: 2,
            itemCode: itemCode,
            buyerOrgCode: this.codeArr.buyerOrgCode,
            isOutSale: this.codeArr.isOutSale,
            id: this.data?.id,
            siteCode: this.codeArr.siteCode,
            supplierCode: this.codeArr.supplierCode
          }

          console.log('调用outNewWaitQuerySapOutDemand接口，参数:', obj)

          this.$API.outsourcing
            .outNewWaitQuerySapOutDemand(obj)
            .then((res) => {
              console.log('outNewWaitQuerySapOutDemand接口返回:', res)
              if (res.data && res.data.length > 0) {
                this.$bus.$emit('itemNameChange', res.data[0].itemName) //传给物料名称
                this.$bus.$emit('planGroupNameChange', res.data[0].planGroupName) // 计划组
                this.$bus.$emit('buyerOrgNameChange', {
                  buyerGroupName: res.data[0].buyerGroupName,
                  buyerGroupCode: res.data[0].buyerGroupCode
                }) // 采购组

                this.$bus.$emit('basicUnitCodeChange', {
                  basicUnitCode: res.data[0].basicUnitCode,
                  basicUnitName: res.data[0].basicUnitName
                }) //传给单位
                this.$bus.$emit('maxDemandQuantityChange', res.data[0].maxReceiveQuantity) //传给可调拨数量
                this.$bus.$emit('supplierStockChange', res.data[0].supplierStock) //传给库存现有
              }
            })
            .catch((error) => {
              console.error('outNewWaitQuerySapOutDemand接口调用失败:', error)
            })

          // 通知采购订单号字段更新数据
          console.log('通知采购订单号字段更新数据')
          this.$nextTick(() => {
            this.$bus.$emit('itemCodeChangeForOrderCode', {
              itemCode: itemCode,
              rowIndex: this.data.addId || this.data.id
            })
          })
        } else if (itemCode && isSameItemCode) {
          console.log('物料编码未变化，跳过接口调用和事件触发')
        }

        // 无论是否有物料编码，都要关闭弹框
        this.handleClose()

        // 在关闭弹框后强制更新组件以确保回显
        this.$nextTick(() => {
          console.log('弹框关闭后强制更新组件以确保回显')
          this.$forceUpdate()
        })
      }

      // 库存地点字段的特殊处理
      if (this.data.column.field === 'warehouseCode') {
        console.log('处理库存地点选择')

        // 增强数据获取逻辑
        let warehouseCode = null
        let warehouseName = null

        if (val.itemData) {
          warehouseCode =
            val.itemData.siteAddress || val.itemData.value || val.itemData.warehouseCode
          warehouseName =
            val.itemData.siteAddressName || val.itemData.name || val.itemData.warehouseName
        } else if (val.value) {
          warehouseCode = val.value
          warehouseName = val.text || val.name
        }

        console.log('最终确定的库存地点编码:', warehouseCode)
        console.log('最终确定的库存地点名称:', warehouseName)

        if (warehouseCode) {
          // 使用$set确保响应式更新
          this.$set(this.data, 'warehouseCode', warehouseCode)
          if (warehouseName) {
            this.$set(this.data, 'warehouseName', warehouseName)
          }

          console.log('库存地点更新成功')

          // 触发父组件事件
          this.$parent.$emit('selectedChanged', {
            //传出额外数据
            fieldCode: 'warehouseCode',
            itemInfo: {
              ...this.data
            }
          })

          // 关闭弹框
          this.handleClose()
        } else {
          console.warn('未能获取到有效的库存地点')
          this.$toast({
            content: this.$t('获取库存地点失败，请重新选择'),
            type: 'warning'
          })
          return
        }
      }

      // 其他字段的通用选择处理
      else if (this.data.column.field !== 'itemCode') {
        console.log('处理其他字段选择:', this.data.column.field)

        // 通用的数据获取逻辑
        let fieldValue = null
        if (val.itemData) {
          fieldValue = val.itemData[this.data.column.field] || val.itemData.value || val.value
        } else if (val.value) {
          fieldValue = val.value
        }

        console.log('最终确定的字段值:', fieldValue)

        if (fieldValue) {
          // 使用$set确保响应式更新
          this.$set(this.data, this.data.column.field, fieldValue)
          console.log(`${this.data.column.field}更新成功:`, fieldValue)

          // 触发父组件的数据变更事件
          this.$nextTick(() => {
            this.$parent.$emit('dataChanged', {
              field: this.data.column.field,
              value: fieldValue,
              rowData: this.data
            })
          })
        } else {
          console.warn(`未能获取到有效的${this.data.column.field}`)
          this.$toast({
            content: this.$t(`获取${this.data.column.field}失败，请重新选择`),
            type: 'warning'
          })
          return
        }
      }
    },

    // 主动检查并更新同行的物料编码
    checkAndUpdateItemCodeFromRow() {
      console.log('检查同行物料编码，当前字段:', this.data.column.field)

      // 通过父组件获取同行的物料编码
      if (this.$parent && this.$parent.getRowData) {
        try {
          const rowData = this.$parent.getRowData(this.data.addId || this.data.id)
          if (rowData && rowData.itemCode && !this.data.itemCode) {
            console.log('从同行获取物料编码:', rowData.itemCode)
            this.data.itemCode = rowData.itemCode
          }
        } catch (error) {
          console.log('无法通过父组件获取行数据，尝试事件方式')
        }
      }

      // 如果父组件方法不可用，尝试通过事件获取
      if (!this.data.itemCode) {
        this.$bus.$emit('requestItemCodeForRow', {
          rowIndex: this.data.addId || this.data.id,
          requestField: this.data.column.field
        })
      }
    },

    // 处理其他字段请求物料编码的事件
    handleRequestItemCodeForRow(data) {
      // 只有物料编码字段且是同一行才响应
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'itemCode' &&
        this.data.itemCode
      ) {
        console.log('物料编码字段响应请求，发送物料编码:', this.data.itemCode)

        // 直接发送物料编码给请求的字段
        this.$bus.$emit('responseItemCodeForRow', {
          rowIndex: this.data.addId || this.data.id,
          itemCode: this.data.itemCode,
          targetField: data.requestField
        })
      }
    },

    // 处理物料编码响应事件
    handleResponseItemCodeForRow(data) {
      // 只有目标字段且是同一行才处理
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        data.targetField === this.data.column.field &&
        data.itemCode
      ) {
        console.log('收到物料编码响应，更新物料编码:', data.itemCode)

        // 更新物料编码
        this.data.itemCode = data.itemCode
      }
    }
  },
  deactivated() {
    // 清理事件监听
    this.$bus.$off('requestItemCodeForRow', this.handleRequestItemCodeForRow)
    this.$bus.$off('responseItemCodeForRow', this.handleResponseItemCodeForRow)

    // 清理组件数据
    this.codeArr = null
    this.dataSource = []

    console.log('Select组件已清理数据')
  },

  beforeDestroy() {
    // 在组件销毁前也进行清理，确保内存释放
    this.codeArr = null
    this.dataSource = []

    console.log('Select组件销毁前清理完成')
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
