import { i18n } from '@/main.js'

// 工具栏按钮配置
export const toolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName'
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode'
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName'
  },
  {
    title: i18n.t('物料编码'),
    field: 'materialCode'
  },
  {
    title: i18n.t('物料名称'),
    field: 'materialName'
  },
  {
    title: i18n.t('年份'),
    field: 'year'
  },
  {
    title: i18n.t('月份'),
    field: 'month'
  },
  {
    title: i18n.t('供应商库存'),
    field: 'supplierStock'
  },
  {
    title: i18n.t('采购组编码'),
    field: 'purchaseGroupCode'
  },
  {
    title: i18n.t('采购组名称'),
    field: 'purchaseGroupName'
  },
  {
    title: i18n.t('采购组织编码'),
    field: 'purchaseOrgCode'
  },
  {
    title: i18n.t('采购组织名称'),
    field: 'purchaseOrgName'
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName'
  }
]
