<!-- 供方-加工商结存库存查询 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="false"
      grid-id="7aa4aca3-bb8e-4662-b147-5b852a4518ce"
      search-grid-id="c86cde92-8b0c-4d5d-b1eb-1fd8d1045fbe"
      @search="handleSearch"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getSearchFormItems } from './config/searchForm'
import { columnData, toolbar } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  name: 'BalanceInventory',
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      columns: columnData,
      toolbar: toolbar,
      requiredConditions: ['siteCode', 'year', 'month']
    }
  },
  computed: {
    searchConditions() {
      const items = getSearchFormItems()
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearch({ searchForm }) {
      this.searchForm = searchForm
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.handleSearch()
    },
    async getTableData() {
      const params = {
        ...this.searchForm
      }
      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.outsourcingNew
        .getBalanceInventoryApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res.code === 200) {
        const records = res.data?.records || []
        this.$refs.commonListRef.setTableData(records)
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        const res = await this.$API.outsourcingNew.exportBalanceInventoryApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
