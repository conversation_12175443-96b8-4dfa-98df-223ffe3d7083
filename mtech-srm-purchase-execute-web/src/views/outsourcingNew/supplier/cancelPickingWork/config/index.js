import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'
const todoListToolBar = [
  {
    id: 'create',
    icon: 'icon_solid_edit',
    title: i18n.t('创建委外退货单')
    // permission: ["O_02_1288"],
  },
  {
    id: 'print',
    icon: 'icon_table_print',
    title: i18n.t('打印退货单')
    // permission: ["O_02_1289"],
  }
]
const todoListToolBarDetail = [
  {
    id: 'create',
    icon: 'icon_solid_edit',
    title: i18n.t('创建委外退货单')
    // permission: ["O_02_1288"],
  },
  {
    id: 'export1',
    icon: 'icon_solid_Createorder',
    title: i18n.t('导出')
  }
  // {
  // id: "print",
  // icon: "icon_table_print",
  // title: i18n.t("打印退货单"),
  // permission: ["O_02_1045"],
  // },
]
const todoListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '155',
    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号'),
    cellTools: []
  },
  {
    width: '95',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,
      dataSource: [
        { label: i18n.t('新建'), value: 0 },
        { label: i18n.t('待确认'), value: 1 },
        { label: i18n.t('供应商退回'), value: 2 },
        { label: i18n.t('供应商确认'), value: 3 },
        { label: i18n.t('采购退回'), value: 4 },
        { label: i18n.t('采购确认'), value: 5 },
        { label: i18n.t('已完结'), value: 6 },
        { label: i18n.t('已取消'), value: 7 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    cellTools: [
      {
        id: 'delete',
        title: i18n.t('删除'),
        visibleCondition: (data) => data['status'] == 0
        // permission: ["O_02_1288"],
      },
      {
        id: 'add',
        title: i18n.t('提交'),
        // permission: ["O_02_1288"],
        visibleCondition: (data) =>
          data['status'] == 0 || data['status'] == 2 || data['status'] == 4
      },
      {
        id: 'edit',
        title: i18n.t('编辑'),
        // permission: ["O_02_1288"],
        visibleCondition: (data) =>
          data['status'] == 0 || data['status'] == 2 || data['status'] == 4
      },
      {
        id: 'cancel',
        title: i18n.t('取消'),
        visibleCondition: (data) =>
          data['status'] == 4 || data['status'] == 2 || data['status'] == 1
        // permission: ["O_02_1290"],
        // permission: ["O_02_0701"],
      }
    ]
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '95',
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '110',
    field: 'materialApproveUserName',
    headerText: i18n.t('供方确认人')
  },
  {
    width: '125',
    field: 'materialApproveDate',
    headerText: i18n.t('供方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    width: '125',
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerCancelUserName',
    headerText: i18n.t('取消人')
  },
  {
    width: '125',
    field: 'buyerCancelDate',
    headerText: i18n.t('取消时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '95',
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步成功'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' },
        { value: '3', text: i18n.t('同步中'), cssClass: '' }
      ]
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步消息')
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    allowEditing: false,
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    allowEditing: false,
    searchOptions: MasterDataSelect.companySupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.buyerOrgCode, data?.buyerOrgName)
    }
  },
  {
    width: '0',
    field: 'buyerOrgName',
    headerText: i18n.t('公司名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('加工供应商名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供应商'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.materialSupplierCode, data?.materialSupplierName)
    }
  },
  {
    width: '0',
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供应商名称'),
    ignore: true
  },
  {
    width: '170',
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('制单日期')
  }
]

export const completedListColumnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    width: '155',
    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号')
  },
  {
    width: '105',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-active' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,
      dataSource: [
        { label: i18n.t('新建'), value: 0 },
        { label: i18n.t('待确认'), value: 1 },
        { label: i18n.t('供应商退回'), value: 2 },
        { label: i18n.t('供应商确认'), value: 3 },
        { label: i18n.t('采购退回'), value: 4 },
        { label: i18n.t('采购确认'), value: 5 },
        { label: i18n.t('已完结'), value: 6 },
        { label: i18n.t('已取消'), value: 7 }
      ],
      fields: { text: 'label', value: 'value' }
    }
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '95',
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },

  {
    width: '65',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '260',
    field: 'itemCode', //supplierCode
    headerText: i18n.t('物料'),
    searchOptions: MasterDataSelect.itemSupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.itemCode, data?.itemName)
    }
  },
  {
    width: '0',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '120',
    field: 'orderItemNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '95',
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '85',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    width: '260',
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    allowEditing: false,
    searchOptions: MasterDataSelect.factorySupplierAddress,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.siteCode, data?.siteName)
    }
  },
  {
    width: '0',
    field: 'siteName',
    headerText: i18n.t('工厂名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    allowEditing: false,
    searchOptions: MasterDataSelect.companySupplier,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.buyerOrgCode, data?.buyerOrgName)
    }
  },
  {
    width: '0',
    field: 'buyerOrgName',
    headerText: i18n.t('公司名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '0',
    field: 'supplierName',
    headerText: i18n.t('加工供应商名称'),
    ignore: true
  },
  {
    width: '260',
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供应商'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.materialSupplierCode, data?.materialSupplierName)
    }
  },
  {
    width: '0',
    field: 'materialSupplierName',
    headerText: i18n.t('原材料供应商名称'),
    ignore: true
  },
  {
    width: '170',
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('制单日期')
  }
]

//status  定点推荐主表状态 0待处理 3已处理 4已退回
export const pageConfig = (orderType) => [
  {
    title: i18n.t('头视图'),
    // dataPermission: "a",
    // permissionCode: "T_02_0138",
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    useCombinationSelection: false,
    toolbar: todoListToolBar,
    // gridId: Vue.prototype.$tableUUID.outsourcing.cancelPickingWork.list,
    grid: {
      frozenColumns: 1,
      columnData: todoListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/kt/outCancelOrder/supplier/queryView',
        recordsPosition: 'data.records',
        defaultRules: [
          {
            field: 'orderType',
            operator: 'equal',
            value: orderType.includes('gc') ? 1 : 0
          }
        ]
      }
    }
  },
  {
    title: i18n.t('明细视图'),
    // dataPermission: "b",
    // permissionCode: "T_02_0139",
    toolbar: todoListToolBarDetail,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    useCombinationSelection: false,
    gridId: '03f0aa23-a151-4ca7-ac9b-f936dc9e3734',

    grid: {
      frozenColumns: 1,

      columnData: completedListColumnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/kt/outCancelOrder/supplier/queryDetailView',
        defaultRules: [
          {
            field: 'orderType',
            operator: 'equal',
            value: orderType.includes('gc') ? 1 : 0
          }
        ]
        // defaultRules: [
        //   {
        //     condition: "and",
        //     field: "rfx_item.rfxHeaderId",
        //     operator: "equal",
        //     value,
        //   },
        // ],
      }
    }
  }
]
