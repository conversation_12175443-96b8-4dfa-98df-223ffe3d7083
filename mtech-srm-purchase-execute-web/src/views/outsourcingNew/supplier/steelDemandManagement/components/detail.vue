<template>
  <div style="height: 100%">
    <mt-template-page
      ref="templateRef1"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle1"
      @handleCustomReset="handleSearchReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="demandCode" :label="$t('需求单编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.demandCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCode"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              />
            </mt-form-item>
            <mt-form-item prop="outsourcedType" :label="$t('委外方式')" label-style="top">
              <mt-select
                v-model="searchFormModel.outsourcedType"
                :data-source="outsourcedTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择委外方式')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                type="multipleChoice"
                :placeholder="$t('请选择状态')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="sendAddress" :label="$t('送货地址')" label-style="top">
              <mt-input
                v-model="searchFormModel.sendAddress"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料')" label-style="top">
              <mt-input
                v-model="searchFormModel.itemCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="lineNo" :label="$t('行号')" label-style="top">
              <mt-input
                v-model="searchFormModel.lineNo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
              <mt-input
                v-model="searchFormModel.updateUserName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="updateTime" :label="$t('更新时间')">
              <mt-date-range-picker
                v-model="searchFormModel.updateTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'updateTime')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { columnData1, statusOptions } from '../config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName, codeNameColumn } from '@/utils/utils'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      siteOptions: [], // 工厂 下列选项
      siteCodeValueTemplate: codeNameColumn({
        firstKey: 'siteCode',
        secondKey: 'siteName'
      }), // 工厂
      outsourcedTypeOptions: [
        { text: this.$t('标准委外'), value: 0 },
        { text: this.$t('销售委外'), value: 1 },
        { text: this.$t('标准采购'), value: 2 },
        { text: this.$t('工序委外'), value: 3 }
      ], // 委外方式选项
      // 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
      statusOptions,
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'export',
                  icon: 'icon_solid_Import',
                  title: this.$t('导出')
                }
              ],
              ['Refresh', 'Setting']
            ]
          },
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          gridId: 'b1a4bfb8-e1bc-4418-b0ae-2f2f035501f1',
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            columnData: columnData1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/steelDemandDetail/querySupplierPage`
            }
          }
        }
      ]
    }
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'Start'] = null
        this.searchFormModel[flag + 'End'] = null
      }
    },
    getUnix(val) {
      return new Date(val).getTime()
    },
    handleClickCellTitle1(e) {
      if (e.field === 'demandCode') {
        console.log(e)
      }
    },
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id == 'export') {
        const params = {
          page: { current: 1, size: 10000 },
          ...this.searchFormModel
        } // 筛选条件
        this.apiStartLoading()
        this.$API.thirdPartyVMICollaboration
          .exportSupplierSteelDemandDetailApi(params)
          .then((res) => {
            this.apiEndLoading()
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
          .catch(() => {
            this.apiEndLoading()
          })
          .finally(() => {
            this.apiEndLoading()
          })
        return
      }
    },
    handleSearchReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = []
          }
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
