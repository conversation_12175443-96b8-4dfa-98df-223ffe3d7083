import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

export const allocationStatusOptions = [
  { text: i18n.t('未分配'), value: 0 },
  { text: i18n.t('成功'), value: 1 },
  { text: i18n.t('失败'), value: 2 }
]

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
          <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
              <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
            </div>
          </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

export const statusOptions = [
  { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
  { value: 1, text: i18n.t('已提交'), cssClass: 'col-active' },
  { value: 2, text: i18n.t('已调料'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已取消'), cssClass: 'col-inactive' },
  { value: 4, text: i18n.t('已退回'), cssClass: 'col-inactive' }
]
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false,
    allowResizing: false
  },
  {
    width: '200',
    field: 'demandCode',
    cellTools: [],
    headerText: i18n.t('需求单编号')
  },
  {
    width: '90',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusOptions
    }
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '200',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '120',
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('标准委外'),
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        3: i18n.t('工序委外')
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('钣金厂供应商编码')
  },
  {
    width: '110',
    field: 'supplierName',
    headerText: i18n.t('钣金厂名称')
  },
  {
    width: '135',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '86',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '105',
    field: 'updateUserName',
    headerText: i18n.t('最后更新人')
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('更新时间')
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  }
]
export const columnData1 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false,
    allowEditing: false,
    allowResizing: false
  },
  {
    width: '200',
    field: 'demandCode',
    cellTools: [],
    headerText: i18n.t('需求单编号')
  },
  {
    width: '90',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusOptions
    }
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '200',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '120',
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('标准委外'),
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        3: i18n.t('工序委外')
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('钣金厂供应商编码')
  },
  {
    width: '110',
    field: 'supplierName',
    headerText: i18n.t('钣金厂名称')
  },
  {
    width: '135',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '100',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '200',
    field: 'itemName',
    headerText: i18n.t('物料描述'),
    ignore: true
  },
  {
    width: '120',
    field: 'deliveryQuantity',
    headerText: i18n.t('需求数量(kg)')
  },
  {
    width: '86',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    width: '120',
    field: 'demandQty',
    headerText: i18n.t('批准数量(kg)')
  },
  {
    width: '120',
    field: 'allocationStatus',
    headerText: i18n.t('系统分配状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未匹配'),
        1: i18n.t('成功'),
        2: i18n.t('失败')
      }
    }
  },
  {
    width: '200',
    field: 'allocationDesc',
    headerText: i18n.t('系统分配校验信息')
  },
  {
    width: '86',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '105',
    field: 'updateUserName',
    headerText: i18n.t('最后更新人')
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('更新时间')
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  }
]

export const scToolBar = [
  { code: 'save', name: i18n.t('保存'), status: 'info' },
  {
    code: 'add',
    name: i18n.t('新增'),
    status: 'info'
  },
  {
    code: 'delete',
    name: i18n.t('删除'),
    status: 'info'
  },
  {
    code: 'deleteAll',
    name: i18n.t('删除所有分配失败行'),
    status: 'info'
  },
  {
    code: 'import',
    name: i18n.t('导入'),
    status: 'info'
  },
  {
    code: 'export',
    name: i18n.t('导出'),
    status: 'info'
  }
]

export const scColumns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'lineNo',
    title: i18n.t('行号'),
    editRender: {
      name: 'input',
      attrs: { type: 'number', min: '1', placeholder: i18n.t('请输入行号') }
    }
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 210,
    editRender: {},
    slots: { edit: 'itemCodeEdit' }
  },
  {
    field: 'itemName',
    title: i18n.t('物料描述'),
    minWidth: 210
  },
  {
    field: 'demandQty',
    title: i18n.t('需求数量(kg)'),
    minWidth: 135,
    editRender: {
      name: 'input',
      attrs: { type: 'number', min: '0', placeholder: i18n.t('请输入需求数量') }
    }
  },
  {
    field: 'itemRemark',
    title: i18n.t('行备注'),
    minWidth: 135,
    editRender: { name: 'input' }
  },
  {
    field: 'quantity',
    title: i18n.t('批准调料数量(kg)'),
    minWidth: 140
  },
  {
    field: 'allocationStatus',
    title: i18n.t('系统分配状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = allocationStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'allocationDesc',
    title: i18n.t('系统分配校验信息'),
    minWidth: 140
  }
]
