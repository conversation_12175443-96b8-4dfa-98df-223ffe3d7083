<template>
  <div class="detail-top-info" style="padding-right: 20px">
    <!-- 顶部信息 -->
    <div class="header-box">
      <div class="middle-blank" />
      <span class="header-box-btn" v-waves type="info" @click="$router.go(-1)">
        {{ $t('返回') }}
      </span>
      <span v-if="canEdit" class="header-box-btn" v-waves type="info" @click="handleSave">
        {{ $t('保存') }}
      </span>
      <span v-if="canEdit" class="header-box-btn" v-waves type="info" @click="handleSubmit">
        {{ $t('提交') }}
      </span>
      <span
        v-if="canEdit && headerId"
        class="header-box-btn"
        v-waves
        type="primary"
        @click="handleCancel"
      >
        {{ $t('取消') }}
      </span>
    </div>
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="formObject" :rules="rules">
        <mt-form-item prop="createUserName" :label="$t('创建人')">
          <mt-input v-model="formObject.createUserName" disabled />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" class="">
          <mt-input v-model="formObject.createTime" disabled />
        </mt-form-item>
        <mt-form-item prop="updateUserName" :label="$t('更新人')">
          <mt-input v-model="formObject.updateUserName" disabled />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')" class="">
          <mt-input v-model="formObject.updateTime" disabled />
        </mt-form-item>
        <mt-form-item prop="demandCode" :label="$t('需求单编号')">
          <mt-input v-model="formObject.demandCode" disabled />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')">
          <mt-select
            v-model="formObject.status"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            disabled
          />
        </mt-form-item>
        <mt-form-item prop="supplier" :label="$t('钣金厂')">
          <mt-input v-model="formObject.supplier" disabled />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <RemoteAutocomplete
            v-model="formObject.siteCode"
            :url="$API.masterData.getSiteAuthFuzzyUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :disabled="pageType !== 'add'"
            params-key="fuzzyParam"
            records-position="data"
            @change="siteChange"
          />
        </mt-form-item>
        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-select
            v-model="formObject.outsourcedType"
            :data-source="outsourcedTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
            :disabled="pageType !== 'add'"
          />
        </mt-form-item>
        <mt-form-item prop="addressCode" :label="$t('送货地址')">
          <RemoteAutocomplete
            v-model="formObject.addressCode"
            url="srm-purchase-execute/tenant/siteTenantExtend/queryBySupplierForSteel"
            :placeholder="$t('请选择')"
            :fields="{ text: 'consigneeAddress', value: 'consigneeAddressCode' }"
            :disabled="pageType !== 'add'"
            :params="{
              supplierCode: formObject.supplierCode
            }"
            params-key="consigneeAddress"
            records-position="data"
            @change="addressChange"
          />
        </mt-form-item>
        <mt-form-item prop="rejectReason" :label="$t('退回原因')" class="full-width">
          <mt-input v-model="formObject.rejectReason" disabled />
        </mt-form-item>
      </mt-form>
    </div>
    <div class="table-area">
      <ScTable
        ref="xTable"
        class="vxe-table-area"
        :row-config="{ height: 50 }"
        :columns="scColumns"
        :table-data="tableData"
        :edit-rules="editRules"
        :edit-config="{
          enabled: canEdit,
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #itemCodeEdit="{ row }">
          <vxe-pulldown ref="xDownItem" transfer>
            <template #default>
              <vxe-input
                :value="row.itemCode"
                :placeholder="$t('请选择物料')"
                readonly
                @click="(e) => focusItemCode(e, row)"
              ></vxe-input>
            </template>
            <template #dropdown>
              <vxe-input
                prefix-icon="vxe-icon-search"
                :placeholder="$t('搜索')"
                @keyup="(e) => keyupItemCode(e, row)"
                style="width: 100%"
              ></vxe-input>
              <vxe-list height="200" class="predict-vxe-dropdown" :data="itemOptions" auto-resize>
                <template #default="{ items }">
                  <div
                    v-show="itemOptions.length"
                    class="predict-vxe-list-item"
                    v-for="item in items"
                    :key="item.value"
                    @click="selectItemCode(item, row)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                  <div v-show="!itemOptions.length" class="predict-vxe-list-item">
                    <span>{{ $t('暂无数据') }}</span>
                  </div>
                </template>
              </vxe-list>
            </template>
          </vxe-pulldown>
        </template>
      </ScTable>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import ScTable from '@/components/ScTable/src/index'
import { getHeadersFileName, download } from '@/utils/utils'
import { scColumns, statusOptions, scToolBar } from '../config/index'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      statusOptions,
      scToolBar,
      scColumns,
      tableData: [],
      formObject: {},
      outsourcedTypeOptions: [
        { text: this.$t('标准委外'), value: 0 },
        { text: this.$t('销售委外'), value: 1 },
        { text: this.$t('标准采购'), value: 2 },
        { text: this.$t('工序委外'), value: 3 }
      ],
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        addressCode: [{ required: true, message: this.$t('请选择送货地址'), trigger: 'blur' }],
        outsourcedType: [{ required: true, message: this.$t('请选择委外方式'), trigger: 'blur' }]
      },
      headerId: '',
      pageType: 'detail',
      editRules: {
        lineNo: [{ required: true, message: this.$t('行号必须填写') }],
        itemCode: [{ required: true, message: this.$t('邮件必须填写') }],
        demandQty: [{ required: true, message: this.$t('需求数量必须填写') }]
      },
      editConfig: {
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
      },
      itemOptions: [],
      getItemDataSource: () => {}
    }
  },
  computed: {
    canEdit() {
      return (
        this.formObject?.status === 0 ||
        this.formObject?.status === 4 ||
        this.$route.query?.type === 'add'
      )
    },
    toolbar() {
      let data = [
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info'
        }
      ]
      if (
        this.formObject?.status === 0 ||
        this.formObject?.status === 4 ||
        this.$route.query?.type === 'add'
      ) {
        data = this.scToolBar
      }
      return data
    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.headerId = this.$route.query.id
    }
    if (this.$route.query.type) {
      this.pageType = this.$route.query.type
      if (this.pageType === 'add') {
        const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        const { enterpriseCode, enterpriseName } = userInfo
        this.formObject = {
          supplierName: enterpriseName,
          supplierCode: enterpriseCode,
          supplier: `${enterpriseCode}-${enterpriseName}`
        }
      } else {
        this.getDatailData()
        this.getTableData()
      }
    }
    this.getItem({ value: '' })
    this.getItemDataSource = utils.debounce(this.getItem, 1000)
  },
  methods: {
    getDatailData() {
      this.$API.thirdPartyVMICollaboration
        .queryHeaderSupplierSteelDemandApi({ id: this.headerId })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.formObject = data
            this.formObject.supplier = `${data.supplierCode}-${data.supplierName}`
            this.headerId = data.id
          }
        })
    },
    getTableData() {
      this.$API.thirdPartyVMICollaboration
        .pageDetailSupplierSteelDemandApi({
          demandId: this.headerId
        })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.tableData = data
          }
        })
    },
    addressChange(e) {
      const { itemData } = e
      if (itemData) {
        this.formObject.addressId = itemData.id
        this.formObject.addressCode = itemData.consigneeAddressCode
        this.formObject.sendAddress = itemData.consigneeAddress
        this.formObject.addressContactPerson = itemData.consigneeName
        this.formObject.addressContactPhone = itemData.consigneePhone
      } else {
        this.formObject.addressId = null
        this.formObject.addressCode = null
        this.formObject.sendAddress = null
        this.formObject.addressContactPerson = null
        this.formObject.addressContactPhone = null
      }
    },
    // 主数据 物料
    getItem(args) {
      const { value } = args
      const params = {
        page: { current: 1, size: 100 },
        condition: 'and',
        rules: [
          {
            label: this.$t('物料编号'),
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value
          }
        ]
      }
      this.$API.outsourcingNew.getItemSup(params).then((res) => {
        if (res) {
          const list = res?.data?.records || []
          const newData = list.map((i) => {
            return {
              ...i,
              label: `${i.itemCode}-${i.itemName}`,
              value: i.itemCode
            }
          })
          this.itemOptions = [...newData]
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code === 'add') {
        $grid.insert()
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          $grid.setEditRow(currentViewRecords[0])
        })
      } else if (code === 'delete') {
        this.handleDelete(selectedRecords)
      } else if (code === 'deleteAll') {
        this.handleDeleteAll()
      } else if (code === 'import') {
        this.handleImport()
      } else if (code === 'export') {
        this.handleExport()
      } else if (code === 'save') {
        this.detailSave()
      }
    },
    detailSave() {
      if (this.headerId) {
        let params = {
          demandId: this.headerId,
          detailList: []
        }
        const currentViewRecords = this.$refs.xTable.$refs.xGrid.getTableData().visibleData
        params.detailList = currentViewRecords
        params.detailList.forEach((item) => {
          if (item.id?.includes('row_')) {
            item.id = null
          }
        })
        this.$loading()
        this.$API.thirdPartyVMICollaboration
          .saveDetailSupplierSteelDemandApi(params)
          .then((res) => {
            this.$hloading()
            if (res.code === 200) {
              this.$toast({
                content: this.$t(`保存成功`),
                type: 'success'
              })
              this.getTableData()
            }
          })
          .catch(() => {
            this.$hloading()
          })
      } else {
        this.$toast({
          content: this.$t('请先保存抬头数据！'),
          type: 'warning'
        })
      }
    },
    handleImport() {
      if (this.headerId) {
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('导入'),
            importApi: this.$API.thirdPartyVMICollaboration.importDetailSupplierSteelDemandApi,
            downloadTemplateApi:
              this.$API.thirdPartyVMICollaboration.tempDetailSupplierSteelDemandApi,
            paramsKey: 'excel',
            queryParams: {
              id: this.headerId
            }
          },
          success: () => {
            this.getTableData()
          }
        })
      } else {
        this.$toast({
          content: this.$t('请先保存抬头数据！'),
          type: 'warning'
        })
      }
    },
    handleExport() {
      if (this.headerId) {
        const params = { id: this.headerId }
        this.$store.commit('startLoading')
        this.$API.thirdPartyVMICollaboration
          .exportDetailSupplierSteelDemandApi(params)
          .then((res) => {
            this.$store.commit('endLoading')

            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
      } else {
        this.$toast({
          content: this.$t('请先保存抬头数据！'),
          type: 'warning'
        })
      }
    },
    handleDelete(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const deleteList = []
          // 遍历已勾选的数据 有id的就是线上数据需要调删除接口
          const arr = this.tableData.filter((i) => {
            return !selectedRecords.some((j) => {
              if (j && j.id && !j.id.includes('row_')) {
                deleteList.push(j.id)
              }
              if (j && j.id) {
                return j.id === i.id
              }
            })
          })
          if (deleteList.length) {
            const ids = Array.from(new Set(deleteList)) // 去个重
            this.$API.thirdPartyVMICollaboration
              .deleteDetailSupplierSteelDemandApi({ ids })
              .then((res) => {
                const { code } = res
                if (code === 200) {
                  this.afterDeleteEvent(arr)
                }
              })
            return
          }
          // 删除后刷新列表
          this.afterDeleteEvent(arr)
        }
      })
    },
    handleDeleteAll() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除所有系统分配状态为失败的数据？')
        },
        success: () => {
          this.$API.thirdPartyVMICollaboration
            .deleteAllocationFailDetailApi({ id: this.headerId })
            .then((res) => {
              const { code } = res
              if (code === 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                this.getTableData()
              }
            })
        }
      })
    },
    afterDeleteEvent() {
      this.$toast({
        content: this.$t('删除成功'),
        type: 'success'
      })
      this.$refs.xTable.$refs.xGrid.removeCheckboxRow()
    },
    focusItemCode() {
      this.$refs.xDownItem.showPanel()
    },
    keyupItemCode(e, row) {
      this.getItemDataSource(e, row)
    },
    selectItemCode(e, row) {
      row.itemCode = e.itemCode
      this.$set(row, 'itemName', e.itemName)
      row.itemId = e.id
    },
    deliverAddrChange(e) {
      if (e.itemData) {
        this.formObject.deliverAddrId = e.itemData.id
        this.formObject.deliverAddrCode = e.itemData.consigneeAddressCode
        this.formObject.deliverAddr = e.itemData.consigneeAddress
        this.formObject.warehouseCode = e.itemData.siteAddress
        this.formObject.warehouseName = e.itemData.siteAddressName
      } else {
        this.formObject.deliverAddrId = null
        this.formObject.deliverAddrCode = null
        this.formObject.deliverAddr = null
        this.formObject.warehouseCode = null
        this.formObject.warehouseName = null
      }
    },
    siteChange(e) {
      const { itemData } = e
      this.formObject.siteName = itemData.siteName
    },
    handleSave() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = { ...this.formObject }
          const currentViewRecords = this.$refs.xTable.$refs.xGrid.getTableData().visibleData
          params.detailList = currentViewRecords
          params.detailList?.forEach((item) => {
            if (item.id?.includes('row_')) {
              item.id = null
            }
          })
          this.$loading()
          this.$API.thirdPartyVMICollaboration
            .saveSupplierSteelDemandApi(params)
            .then((res) => {
              this.$hloading()
              if (res.code === 200) {
                this.headerId = res.data
                this.$toast({
                  content: this.$t(`保存成功`),
                  type: 'success'
                })
                this.getDatailData()
                this.getTableData()
              }
            })
            .catch(() => {
              this.$hloading()
            })
        }
      })
    },
    handleSubmit() {
      if (this.headerId) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认提交？')
          },
          success: () => {
            this.$store.commit('startLoading')
            let params = { ...this.formObject }
            const currentViewRecords = this.$refs.xTable.$refs.xGrid.getTableData().visibleData
            params.detailList = currentViewRecords
            params.detailList?.forEach((item) => {
              if (item.id?.includes('row_')) {
                item.id = null
              }
            })
            this.$API.thirdPartyVMICollaboration
              .submitSupplierSteelDemandApi(params)
              .then((res) => {
                if (res.code === 200) {
                  this.$toast({
                    content: this.$t(`提交成功`),
                    type: 'success'
                  })
                  this.$router.go(-1)
                }
              })
              .finally(() => {
                this.$store.commit('endLoading')
                this.getTableData()
              })
          }
        })
      } else {
        this.$toast({
          content: this.$t(`提交前请先保存当前数据！`),
          type: 'warning'
        })
      }
    },
    handleCancel() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认取消？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.thirdPartyVMICollaboration
            .cancelSteelDemandApi({
              ids: [this.headerId]
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t(`操作成功`),
                  type: 'success'
                })
                this.$router.go(-1)
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-area {
  /deep/.tab-item2 div {
    // background: red !important;
    &:before {
      content: '*';
      color: #f44336;
      font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system',
        'BlinkMacSystemFont';
      font-size: 12px;
      font-weight: normal;
      margin-right: -4px;
    }
  }
}
</style>

<style>
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
