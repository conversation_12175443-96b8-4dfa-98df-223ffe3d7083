/**
 * 供方-替代料申请管理搜索表单配置
 */
import { i18n } from '@/main.js'
import { statusOptions, syncStatusOptions } from './index'

// 列表视图搜索表单配置
export const getListSearchFormItems = () => [
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('替代料申请单号'),
    field: 'substituteApplicationNo',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('状态'),
    field: 'status',
    type: 'select',
    options: statusOptions
  },
  {
    label: i18n.t('采购订单号'),
    field: 'purchaseOrderNo',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('采购订单行号'),
    field: 'purchaseOrderLineNo',
    type: 'input'
  },
  {
    label: i18n.t('同步状态'),
    field: 'syncStatus',
    type: 'select',
    options: syncStatusOptions
  },
  {
    label: i18n.t('创建时间'),
    field: 'createTime',
    type: 'dateRange'
  },
  {
    label: i18n.t('更新时间'),
    field: 'updateTime',
    type: 'dateRange'
  }
]

// 明细视图搜索表单配置
export const getDetailSearchFormItems = () => [
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('替代料申请单号'),
    field: 'substituteApplicationNo',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('状态'),
    field: 'status',
    type: 'select',
    options: statusOptions
  },
  {
    label: i18n.t('采购订单号'),
    field: 'purchaseOrderNo',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('采购订单行号'),
    field: 'purchaseOrderLineNo',
    type: 'input'
  },
  {
    label: i18n.t('物料编码'),
    field: 'materialCode',
    type: 'input'
  },
  {
    label: i18n.t('物料名称'),
    field: 'materialName',
    type: 'input'
  },
  {
    label: i18n.t('采购组编码'),
    field: 'purchaseGroupCode',
    type: 'input'
  },
  {
    label: i18n.t('品类编码'),
    field: 'categoryCode',
    type: 'input'
  },
  {
    label: i18n.t('同步状态'),
    field: 'syncStatus',
    type: 'select',
    options: syncStatusOptions
  },
  {
    label: i18n.t('创建时间'),
    field: 'createTime',
    type: 'dateRange'
  },
  {
    label: i18n.t('更新时间'),
    field: 'updateTime',
    type: 'dateRange'
  }
]
