<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="demandCode" :label="$t('需求单编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.demandCode"
                :show-clear-button="true"
                :placeholder="$t('请输入需求单编号')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusSearchOptions"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')" class="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCode"
                :url="$API.masterData.getSiteListUrl"
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              />
            </mt-form-item>
            <mt-form-item prop="outsourcedType" :label="$t('委外方式')" label-style="top">
              <mt-select
                v-model="searchFormModel.outsourcedType"
                :data-source="outsourcedTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                :placeholder="$t('请选择委外方式')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('钣金厂')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择钣金厂')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
            <mt-form-item prop="itemCode" :label="$t('物料编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.itemCode"
                :url="$API.masterData.getItemUrl"
                :placeholder="$t('请选择物料')"
                :fields="{ text: 'itemName', value: 'itemCode' }"
                :search-fields="['itemName', 'itemCode']"
              />
            </mt-form-item>
            <mt-form-item prop="lineNo" :label="$t('行号')" label-style="top">
              <mt-input
                v-model="searchFormModel.lineNo"
                :show-clear-button="true"
                :placeholder="$t('请输入行号')"
              />
            </mt-form-item>
            <mt-form-item prop="sendAddress" :label="$t('送货地址')" label-style="top">
              <mt-input
                v-model="searchFormModel.sendAddress"
                :show-clear-button="true"
                :placeholder="$t('请输入送货地址')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                @change="(e) => handleDateTimeChange(e, 'createTime')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('更新人')" label-style="top">
              <mt-input
                v-model="searchFormModel.updateUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入更新人')"
              />
            </mt-form-item>
            <mt-form-item prop="updateTime" :label="$t('更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.updateTime"
                @change="(e) => handleDateTimeChange(e, 'updateTime')"
                :placeholder="$t('请选择更新时间')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { detailColumnData, statusSearchOptions } from '../config/steelDemandColumn.js'
import * as UTILS from '@/utils/utils'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      statusSearchOptions,
      outsourcedTypeOptions: [
        { text: this.$t('标准委外'), value: 0 },
        { text: this.$t('销售委外'), value: 1 },
        { text: this.$t('标准采购'), value: 2 },
        { text: this.$t('工序委外'), value: 3 }
      ],
      searchFormModel: {
        status: []
      },
      pageConfig: [
        {
          gridId: '366d83a6-8a5d-48bc-ae0c-ebf8d7a891b9',
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          toolbar: {
            useBaseConfig: false,
            tools: [
              [{ id: 'Export1', icon: 'icon_solid_export', title: this.$t('导出') }],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          useToolTemplate: false,
          grid: {
            columnData: detailColumnData,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/steelDemandDetail/queryBuyerPage'
            }
          }
        }
      ]
    }
  },
  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'demandCode') {
        this.$router.push({
          path: `steel-request-detail`,
          query: {
            timeStamp: new Date().getTime(),
            id: e.data.demandId
          }
        })
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleClickToolBar(e) {
      const { toolbar } = e

      if (toolbar.id === 'Export1') {
        this.handleExport()
      }
    },
    handleExport() {
      const params = {
        page: { current: 1, size: 9999 },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.thirdPartyVMICollaboration.exportSteelDemandDetailApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
