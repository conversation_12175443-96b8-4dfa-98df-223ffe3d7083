import { i18n } from '@/main.js'

export const statusSearchOptions = [
  { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
  { value: 1, text: i18n.t('已提交'), cssClass: 'col-active' },
  { value: 2, text: i18n.t('已调料'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已取消'), cssClass: 'col-inactive' },
  { value: 4, text: i18n.t('已退回'), cssClass: 'col-inactive' }
]
export const listColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'demandCode',
    headerText: i18n.t('需求单编号'),
    cellTools: []
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusSearchOptions
    }
  },
  {
    width: '95',
    field: 'siteCode',
    headerText: i18n.t('工厂编号')
  },
  {
    width: '225',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '120',
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('标准委外'),
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        3: i18n.t('工序委外')
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('钣金厂供应商编码')
  },
  {
    width: '195',
    field: 'supplierName',
    headerText: i18n.t('钣金厂供应商名称')
  },
  {
    width: '150',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'rejectReason',
    headerText: i18n.t('退回原因')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  }
]
export const detailColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'demandCode',
    headerText: i18n.t('需求单编号'),
    cellTools: []
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusSearchOptions
    }
  },
  {
    width: '95',
    field: 'siteCode',
    headerText: i18n.t('工厂编号')
  },
  {
    width: '225',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '120',
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('标准委外'),
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        3: i18n.t('工序委外')
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('钣金厂供应商编码')
  },
  {
    width: '195',
    field: 'supplierName',
    headerText: i18n.t('钣金厂供应商名称')
  },
  {
    width: '150',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '195',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    width: '120',
    field: 'demandQty',
    headerText: i18n.t('需求数量(kg)')
  },
  {
    width: '80',
    field: 'itemRemark',
    headerText: i18n.t('行备注')
  },
  {
    width: '120',
    field: 'quantity',
    headerText: i18n.t('批准数量(kg)')
  },
  {
    width: '120',
    field: 'allocationStatus',
    headerText: i18n.t('系统分配状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未匹配'),
        1: i18n.t('成功'),
        2: i18n.t('失败')
      }
    }
  },
  {
    width: '200',
    field: 'allocationDesc',
    headerText: i18n.t('系统分配校验信息')
  },
  {
    width: '150',
    field: 'rejectReason',
    headerText: i18n.t('退回原因')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  }
]
export const relationColumnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'demandCode',
    headerText: i18n.t('需求单编号'),
    cellTools: []
  },
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusSearchOptions
    }
  },
  {
    width: '95',
    field: 'siteCode',
    headerText: i18n.t('工厂编号')
  },
  {
    width: '225',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '120',
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('标准委外'),
        1: i18n.t('销售委外'),
        2: i18n.t('标准采购'),
        3: i18n.t('工序委外')
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('钣金厂供应商编码')
  },
  {
    width: '195',
    field: 'supplierName',
    headerText: i18n.t('钣金厂供应商名称')
  },
  {
    width: '150',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  // {
  //   field: 'lineNo',
  //   headerText: i18n.t('行号')
  // },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '195',
    field: 'itemName',
    headerText: i18n.t('物料描述')
  },
  {
    width: '120',
    field: 'demandQty',
    headerText: i18n.t('需求数量(kg)')
  },
  {
    width: '80',
    field: 'itemRemark',
    headerText: i18n.t('行备注')
  },
  {
    width: '120',
    field: 'quantity',
    headerText: i18n.t('批准数量(kg)')
  },
  {
    width: '120',
    field: 'allocationStatus',
    headerText: i18n.t('系统分配状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未匹配'),
        1: i18n.t('成功'),
        2: i18n.t('失败')
      }
    }
  },
  {
    width: '200',
    field: 'allocationDesc',
    headerText: i18n.t('系统分配校验信息')
  },
  {
    field: 'count',
    headerText: i18n.t('卷重(kg)')
  },
  {
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'steelSupplierCode',
    headerText: i18n.t('钢材供应商编码')
  },
  {
    width: '195',
    field: 'steelSupplierName',
    headerText: i18n.t('钢材供应商名称')
  },
  {
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    field: 'takeNo',
    headerText: i18n.t('车号')
  },
  {
    width: '140',
    field: 'orderCode',
    headerText: i18n.t('关联采购订单号')
  },
  {
    width: '150',
    field: 'orderLineNo',
    headerText: i18n.t('关联采购订单行号')
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('调料单号')
    // cellTools: []
  },
  {
    width: '200',
    field: 'rowNum',
    headerText: i18n.t('调料单行号')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('需求单创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('需求单创建时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('需求单更新人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('需求单更新时间'),
    type: 'date',
    format: 'yyyy-MM-dd HH:mm:ss'
  }
]

export const statusOptions = [
  { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
  { value: 1, text: i18n.t('已提交'), cssClass: 'col-active' },
  { value: 2, text: i18n.t('已调料'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已取消'), cssClass: 'col-inactive' }
]

export const allocationStatusOptions = [
  { text: i18n.t('未分配'), value: 0 },
  { text: i18n.t('成功'), value: 1 },
  { text: i18n.t('失败'), value: 2 }
]

export const scColumns = [
  {
    type: 'checkbox',
    width: 50
    // fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码'),
    minWidth: 210
  },
  {
    field: 'itemName',
    title: i18n.t('物料描述'),
    minWidth: 210
  },
  {
    field: 'demandQty',
    title: i18n.t('需求数量(kg)'),
    minWidth: 135
  },
  {
    field: 'itemRemark',
    title: i18n.t('行备注'),
    minWidth: 135
  },
  {
    field: 'quantity',
    title: i18n.t('调料数量(kg)'),
    minWidth: 140
  },
  {
    field: 'allocationStatus',
    title: i18n.t('系统分配状态'),
    minWidth: 140,
    formatter: ({ cellValue }) => {
      let item = allocationStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'allocationDesc',
    title: i18n.t('系统分配校验信息'),
    minWidth: 140
  },
  {
    field: 'amount',
    title: i18n.t('调料金额'),
    minWidth: 140
  },
  {
    field: 'remainStock',
    title: i18n.t('净剩余库存(kg)'),
    minWidth: 140
  }
]
