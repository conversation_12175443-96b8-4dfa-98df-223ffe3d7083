<template>
  <!-- VMI领料管理，创建领料单详情页 -->
  <div class="full-height pt20 vertical-flex-box">
    <!-- 头部信息 -->
    <!-- :header-info1.sync="id ? formObject : createDate[0]" -->
    <top-info
      ref="topInfo"
      class="flex-keep collapse-search-area"
      :header-info1.sync="headerInfo"
      @saveBtn="saveBtn"
      v-if="headerInfo !== null"
      @updateItemList="updateItemList"
      @submitBtn="submitBtn"
      @goBack="goBack"
      @doExpand="doExpand"
    ></top-info>
    <div class="template-page-container">
      <mt-template-page
        ref="templateRef"
        :current-tab="0"
        :hidden-tabs="false"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @dataBound="handleDataBound"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      ></mt-template-page>
    </div>
  </div>
</template>

<script>
import {
  pickingDetailsPageConfigCreate,
  pickingDetailsPageConfigDetail
} from './config/pickingDetails.js'
import { cloneDeep } from 'lodash'

export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  data() {
    return {
      pageConfig: [],
      headerInfo: null,
      isEdit: false, // 是 新建 || 详情
      createDate: JSON.parse(sessionStorage.getItem('createDate')),
      id: this.$route.query.details,
      newCreateDate: [],
      formObject: {
        status: 1, // status状态：0新建（待提交） 1已提交/待确认 2已接收/待质检 8已完成 9已取消
        vmiOrderCode: 'w20211227001', // VMI领料单号
        createUserName: 'jcs', // 制单人
        createTime: '2022-03-02', // 制单时间
        addressCode: '',
        siteCode: '', // 工厂编码
        siteName: '',
        supplierCode: '', // 原材料供应商编码
        vmiWarehouseCode: '', // VMI仓编码
        vmiWarehouseName: '',
        processorCode: null, // 领料供应商编码（加工商）
        processorName: '', // 领料供应商名称（加工商）
        vmiWarehouseAddress: '', // 送货地址
        vmiWarehouseAddressName: '',
        remark: '', // 备注
        itemList: [], // 创建VMI领料单请求明细参数
        statusCreate: false //判断是不是编辑页面
      },
      isInfo: true
    }
  },
  mounted() {
    // 新建 || 详情
    if (this.$route.query.details != 1) {
      // 渲染顶部和操作按钮toolbar
      // this.createDate.forEach((item) => {
      //   // item.rawDataLine = 1;
      //   this.pageConfig[0].grid.dataSource.push(item);
      // });
      this.renderUI(0)
      this.headerInfo = this.createDate[0]
      // this.isInfo = true;
    } else {
      this.formObject.statusCreate = false
      this.getFormDetail(this.$route.query.id)
    }
  },
  methods: {
    // 动态计算列表高度
    renderPageConfigHeight() {
      const _container = document.getElementsByClassName('full-height')[0]?.clientHeight
      const _topInfo = document.getElementsByClassName('flex-keep')[0]?.clientHeight ?? 277
      const _tab = document.getElementsByClassName('tab-wrap2')[0].clientHeight ?? 28
      const _btn = document.getElementsByClassName('toolbar-container')[0].clientHeight ?? 50
      const _gridListHeaderHeight =
        document.getElementsByClassName('e-gridheader')[0]?.clientHeight ?? 44
      const _pageHeight = document.getElementsByClassName('mt-pagertemplate')[0]?.clientHeight ?? 40
      const gridheight =
        _container - _topInfo - _tab - _btn - _gridListHeaderHeight - _pageHeight - 35
      // this.pageConfig[0].grid.height = gridheight
      this.$set(this.pageConfig[0].grid, 'height', gridheight)
    },
    //详情==接口
    handleClickCellTool() {},
    getFormDetail(id) {
      this.$API.purchaseCoordination
        .VMINewPickupOrderDetailQuery({
          id: id
        })
        .then((res) => {
          this.formObject = cloneDeep(res.data)

          this.formObject.itemList = cloneDeep(
            JSON.parse(JSON.stringify(this.formObject.vmiOrderItemResponses))
          )

          delete this.formObject.vmiOrderItemResponses
          this.headerInfo = this.formObject

          // status状态：0新建（待提交） 1已提交/待确认 2已接收/待质检 8已完成 9已取消
          if (this.formObject.status == 0) {
            // 采方-状态是"新建（待提交）"
            // 渲染顶部和操作按钮toolbar
            this.renderUI(this.formObject.status)
            console.log(this.formObject, '123')
          } else if (this.formObject.status == 2) {
            this.formObject.statusCreate = true

            this.pageConfig = pickingDetailsPageConfigDetail(
              this,
              this.formObject.status,
              this.formObject.itemList
            )
          } else {
            // 渲染顶部和操作按钮toolbar
            this.renderUI(this.formObject.status)
          }
        })
    },
    // 渲染顶部和操作按钮toolbar
    renderUI(status) {
      if (status == 0 && !this.id) {
        // this.formObject.statusCreate = false;
        // let newCreateDate = [];
        // let params = {
        //   siteCode: this.createDate[0].siteCode, // 工厂编码
        //   supplierCode: this.createDate[0].supplierCode, // 原材料供应商编码
        //   vmiWarehouseCode: this.createDate[0].vmiWarehouseCode, // VMI仓编码
        //   processorCode: this.createDate[0].processorCode, // 领料供应商编码（加工商）
        //   vmiWarehouseAddress: this.createDate[0].vmiWarehouseAddress, // 送货地址
        //   vmiWarehouseAddressName: this.createDate[0].vmiWarehouseAddressName,
        //   addressCode: this.createDate[0].addressCode,
        //   addressContactNumber: this.createDate[0].consigneePhone,
        //   addressContactPerson: this.createDate[0].consigneeName,
        //   addressId: this.createDate[0].addressId,
        //   remark: this.createDate[0].remark, // 备注
        //   itemList: this.createDate, // 创建VMI领料单请求明细参数
        // };
        // this.$API.purchaseCoordination
        //   .VmiSteelGetPickupAmount(params)
        //   .then((res) => {
        //     newCreateDate = res.data.vmiOrderItemResponses;
        //     newCreateDate.forEach((item) => {
        //       item.checkCount = item.count;
        //       item.countLimit = item.count;
        //       item.purchaseGroupName = item.buyerOrgName;
        //       item.purchaseGroupCode = item.buyerOrgCode;
        //     });
        //     this.pageConfig = pickingDetailsPageConfigCreate(
        //       this,
        //       status,
        //       newCreateDate
        //     );
        //   });
        this.pageConfig = pickingDetailsPageConfigCreate(this, status, this.createDate)
      } else if (status == 0 && this.id) {
        this.pageConfig = pickingDetailsPageConfigCreate(this, status, this.formObject.itemList)
      } else {
        this.formObject.statusCreate = true
        this.pageConfig = pickingDetailsPageConfigDetail(this, status, this.formObject.itemList)
      }
    },
    // 跳转详情
    handleClickCellTitle() {},
    // 表格头部操作
    handleClickToolBar(e) {
      // let fieldData = {
      //   siteCode: this.formObject.siteCode,
      //   supplierCode: this.formObject.supplierCode,
      //   vmiWarehouseCode: this.formObject.vmiWarehouseCode,
      // };
      let _selectRecords = e.grid.getSelectedRecords()
      // let _getCheckboxRecords = e.grid.getCheckboxRecords();
      // console.log(_getCheckboxRecords);

      console.log(e)
      console.log(_selectRecords)

      if (e.toolbar.id == 'Delete') {
        if (_selectRecords.length == 0) {
          return this.$toast({
            content: this.$t('请先选择一行'),
            type: 'warning'
          })
        }
        if (this.id) {
          let obj = {
            ids: []
          }
          obj.ids = _selectRecords.map((item) => {
            return item.id
          })
          this.$API.purchaseCoordination.postReceiveRemoveDetail(obj).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('删除成功'),
                type: 'success'
              })
              this.$refs.topInfo.getAmmount()
              // this.goBack();
            } else {
              this.$refs.topInfo.getAmmount()
              this.$toast({ content: this.$t('提交失败'), type: 'error' })
            }
          })
        }
        _selectRecords.map((item) => {
          this.pageConfig[0].grid.dataSource.splice(
            this.pageConfig[0].grid.dataSource.findIndex((value) => {
              return value.id === item.id
            }),
            1
          )
        })
        console.log(this.pageConfig[0].grid.dataSource)
        this.$refs.topInfo.getAmmount(true, this.pageConfig[0].grid.dataSource)
      } else if (e.toolbar.id == 'print') {
        // TODO: 领料-供方-确认后-打印
      } else {
        // 调用刷新方法
      }
    },
    handleDataBound() {
      // debugger;
      setTimeout(() => {
        this.$refs.templateRef.resetGridHeight()
      }, 1)
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 更新itemList
    updateItemList(val) {
      console.log(val, this.pageConfig[0].grid.dataSource)
      this.pageConfig[0].grid.dataSource = val
      setTimeout(() => {
        this.renderPageConfigHeight()
      }, 1)
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 展开收起的hearder
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
      setTimeout(() => {
        this.renderPageConfigHeight()
      }, 100)
    },
    // 保存
    saveBtn(val) {
      let _this = this
      // 提交时校验必填项
      _this.VMIPickupOrderCreateInterface(true, val)
    },
    // 提交
    submitBtn(val) {
      let _this = this
      // 提交时校验必填项
      // this.$refs.topInfo.$refs.ruleForm.validate((valid) => {
      //   if (valid) {
      //     if (_this.formObject.itemList.length == 0) {
      //       return _this.$toast({
      //         content: _this.$t("请先选择VMI库存行"),
      //         type: "warning",
      //       });
      //     }
      //     if (_this.formObject.itemList.some((item) => !item.checkCount)) {
      //       return _this.$toast({
      //         content: _this.$t("请输入领料数量"),
      //         type: "warning",
      //       });
      //     }
      _this.VMIPickupOrderCreateInterface(false, val)
      // }
      // });
    },
    // 保存||提交==接口
    VMIPickupOrderCreateInterface(isSave, val) {
      // TODO: 领料-采方-创建-缺参数“单位编码”、“计划组code”
      let _getCheckboxRecords = this.$refs.templateRef
        ?.getCurrentUsefulRef()
        .gridRef?.ejsRef.getCurrentViewRecords()
      _getCheckboxRecords.forEach((item) => {
        item.checkCount = item.count
        item.countLimit = item.count
      })

      let itemList = _getCheckboxRecords
      if (isSave === false) {
        itemList.forEach((item) => {
          item.purchaseGroupName = item.buyerOrgName
          item.purchaseGroupCode = item.buyerOrgCode
        })
      }
      let params = {
        siteCode: val.siteCode, // 工厂编码
        supplierCode: val.supplierCode, // 原材料供应商编码
        vmiWarehouseCode: val.vmiWarehouseCode, // VMI仓编码
        outsourcedType: val.outsourcedType,
        processorCode: val.processorCode, // 领料供应商编码（加工商）
        vmiWarehouseAddress: val.vmiWarehouseAddress, // 送货地址
        vmiWarehouseAddressName: val.vmiWarehouseAddressName,
        addressCode: val.addressCode,
        addressContactNumber: val.consigneePhone,
        addressContactPerson: val.consigneeName,
        addressId: val.addressId,
        remark: val.remark, // 备注
        itemList: itemList // 创建VMI领料单请求明细参数
      }
      if (this.$route.query.id) {
        params['id'] = this.$route.query.id
      } else {
        params.itemList.forEach((item) => {
          item.id = ''
        })
      }
      params['operationType'] = isSave ? 1 : 2 // 操作类型，1-保存/2-提交

      this.$API.purchaseCoordination.VmiSteelPickupOrderCreate(params).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: isSave ? this.$t('保存成功') : this.$t('提交成功'),
            type: 'success'
          })

          this.goBack()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}

/deep/ .template-page-container {
  overflow: scroll;
}
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}
.set-country {
  height: 100%;
}
</style>
