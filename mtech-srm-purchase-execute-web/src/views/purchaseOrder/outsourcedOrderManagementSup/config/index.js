import { i18n } from '@/main.js'

export const moveTypeOptions = [
  { text: i18n.t('消耗'), value: 0 },
  { text: i18n.t('取消消耗'), value: 1 }
]

// 工具栏按钮配置
export const toolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('委外订单号'),
    field: 'orderCode'
  },
  {
    title: i18n.t('委外订单行号'),
    field: 'orderItemNo'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName'
  },
  {
    title: i18n.t('加工商编码'),
    field: 'supplierCode'
  },
  {
    title: i18n.t('加工商名称'),
    field: 'supplierName'
  },
  {
    title: i18n.t('加工件物料编码'),
    field: 'materialCode'
  },
  {
    title: i18n.t('加工件物料名称'),
    field: 'materialName'
  },
  {
    title: i18n.t('原材料物料编码'),
    field: 'materialCode2'
  },
  {
    title: i18n.t('原材料物料名称'),
    field: 'materialName2'
  },
  {
    title: i18n.t('采购组编码'),
    field: 'purchaseGroupCode'
  },
  {
    title: i18n.t('采购组名称'),
    field: 'purchaseGroupName'
  },
  {
    title: i18n.t('需求量'),
    field: 'requirementQuantity'
  },
  {
    title: i18n.t('未清需求数量'),
    field: 'unClearedQuantity'
  },
  {
    title: i18n.t('待领料数量'),
    field: 'quantity'
  },
  {
    title: i18n.t('已建领料但未接收数量'),
    field: 'quantity2'
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName'
  },
  {
    title: i18n.t('原材料需求日期'),
    field: 'requirementDate'
  },
  {
    title: i18n.t('库存地点'),
    field: 'warehouseCode'
  },
  {
    title: i18n.t('仓管员编码'),
    field: 'warehouseKeeperCode'
  },
  {
    title: i18n.t('供应商库存（分包库存）'),
    field: 'supplierStock'
  },
  {
    title: i18n.t('非限制库存'),
    field: 'unrestrictedStock'
  },
  {
    title: i18n.t('待检不合格库存'),
    field: 'pendingInspectionStock'
  },
  {
    title: i18n.t('质量检验中的库存'),
    field: 'qualityInspectionStock'
  },
  {
    title: i18n.t('已审核未过账数量'),
    field: 'auditedNotPostedQuantity'
  },
  {
    title: i18n.t('未审核未过账数量'),
    field: 'unauditedNotPostedQuantity'
  },
  {
    title: i18n.t('根据取整模式得到的取整值'),
    field: 'roundedValue'
  }
]

export const consColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('物料凭证号'),
    field: 'code'
  },
  {
    title: i18n.t('物料凭证行号'),
    field: 'lineCode'
  },
  {
    title: i18n.t('凭证年度'),
    field: 'year'
  },
  {
    title: i18n.t('委外订单号'),
    field: 'orderCode'
  },
  {
    title: i18n.t('委外订单行号'),
    field: 'orderItemNo'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName'
  },
  {
    title: i18n.t('加工商编码'),
    field: 'supplierCode'
  },
  {
    title: i18n.t('加工商名称'),
    field: 'supplierName'
  },
  {
    title: i18n.t('加工件物料编码'),
    field: 'materialCode'
  },
  {
    title: i18n.t('加工件物料名称'),
    field: 'materialName'
  },
  {
    title: i18n.t('加工件单位'),
    field: 'unit'
  },
  {
    title: i18n.t('原材料物料编码'),
    field: 'materialCode2'
  },
  {
    title: i18n.t('原材料物料名称'),
    field: 'materialName2'
  },
  {
    title: i18n.t('原材料单位'),
    field: 'unit2'
  },
  {
    title: i18n.t('采购组编码'),
    field: 'purchaseGroupCode'
  },
  {
    title: i18n.t('采购组名称'),
    field: 'purchaseGroupName'
  },
  {
    title: i18n.t('移动类型'),
    field: 'moveType',
    formatter: ({ cellValue }) => {
      let item = moveTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    title: i18n.t('数量'),
    field: 'quantity'
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName'
  },
  {
    title: i18n.t('库存地点'),
    field: 'warehouseCode'
  },
  {
    title: i18n.t('过账日期'),
    field: 'postingDate',
    minWidth: 160
  },
  {
    title: i18n.t('凭证创建时间'),
    field: 'createTime',
    minWidth: 160
  }
]
