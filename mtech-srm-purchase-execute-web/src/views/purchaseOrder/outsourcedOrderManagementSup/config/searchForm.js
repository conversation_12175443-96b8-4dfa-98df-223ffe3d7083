/**
 * 委外订单管理搜索表单配置
 */
import { i18n } from '@/main.js'
import { moveTypeOptions } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('委外订单号'),
    field: 'orderCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('委外订单行号'),
    field: 'orderItemNo',
    type: 'input'
  },
  {
    label: i18n.t('原材料物理编码'),
    field: 'materialCode',
    type: 'input'
  }
]

export const getConsSearchFormItems = () => [
  {
    label: i18n.t('物料凭证号'),
    field: 'code',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('物料凭证行号'),
    field: 'lineCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('委外订单号'),
    field: 'orderCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('委外订单行号'),
    field: 'orderItemNo',
    type: 'input'
  },
  {
    label: i18n.t('原材料物料编码'),
    field: 'materialCode',
    type: 'input'
  },
  {
    label: i18n.t('原材料物料名称'),
    field: 'materialName',
    type: 'input'
  },
  {
    label: i18n.t('加工件物料编码'),
    field: 'materialCode2',
    type: 'input'
  },
  {
    label: i18n.t('加工件物料名称'),
    field: 'materialName2',
    type: 'input'
  },
  {
    label: i18n.t('移动类型'),
    field: 'moveType',
    type: 'select',
    options: moveTypeOptions
  },
  {
    label: i18n.t('品类编码'),
    field: 'categoryCode',
    type: 'input'
  },
  {
    label: i18n.t('品类名称'),
    field: 'categoryName',
    type: 'input'
  },
  {
    label: i18n.t('库存地点'),
    field: 'warehouseCode',
    type: 'input'
  },
  {
    label: i18n.t('过账日期'),
    field: 'updateTime',
    type: 'dateRange'
  },
  {
    label: i18n.t('凭证创建时间'),
    field: 'createTime',
    type: 'dateRange'
  }
]
