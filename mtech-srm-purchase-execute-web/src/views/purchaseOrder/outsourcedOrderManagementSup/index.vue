<!-- 供方-委外订单管理 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <IngredientQuery ref="ingredientQueryRef" v-show="tabIndex == 0" />
      <ConsumptionQuery ref="consumptionQueryRef" v-show="tabIndex == 1" />
    </div>
  </div>
</template>

<script>
import IngredientQuery from './pages/IngredientQuery.vue'
import ConsumptionQuery from './pages/ConsumptionQuery.vue'
export default {
  components: { IngredientQuery, ConsumptionQuery },
  data() {
    return {
      tabList: [{ title: this.$t('委外订单原料查询') }, { title: this.$t('委外原料消耗查询') }],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
