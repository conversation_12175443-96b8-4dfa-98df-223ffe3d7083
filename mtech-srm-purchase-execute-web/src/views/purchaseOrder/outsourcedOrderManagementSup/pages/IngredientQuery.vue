<!-- 委外订单原料查询 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="false"
      grid-id="9b7f52d1-3980-4730-a06d-fe2ca430a068"
      search-grid-id="553de690-b643-473f-af46-76a6d139d648"
      @search="handleSearch"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getSearchFormItems } from '../config/searchForm'
import { columnData, toolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: columnData,
      toolbar
    }
  },
  computed: {
    searchConditions() {
      const items = getSearchFormItems()
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearch({ searchForm }) {
      this.searchForm = searchForm
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.handleSearch()
    },
    async getTableData() {
      const params = {
        ...this.searchForm
      }
      this.$refs.commonListRef.setLoading(true)
      const res = await this.$API.outsourcedOrderManagement
        .pageQueryIngredientApi(params)
        .catch(() => this.$refs.commonListRef.setLoading(false))
      this.$refs.commonListRef.setLoading(false)
      if (res.code === 200) {
        const records = res.data?.records || []
        this.$refs.commonListRef.setTableData(records)
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        const res = await this.$API.outsourcedOrderManagement.exportIngredientApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
