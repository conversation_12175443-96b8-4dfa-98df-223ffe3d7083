import UTILS from '../../../../utils/utils'
import Vue from 'vue'
import cellFileView from '@/components/normalEdit/cellFileView' // 单元格附件查看
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
export const editColumnBefore = [
  //订单明细
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  // 合同编号
  {
    field: 'contractNo',
    headerText: i18n.t('合同编号'),
    width: '150'
  },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用空格隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(' ')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'showStatus',
    headerText: i18n.t('订单状态'),
    width: '95',
    valueConverter: {
      type: 'map',
      map: [
        { value: i18n.t('草稿'), text: i18n.t('草稿'), cssClass: '' },
        {
          value: i18n.t('待审批'),
          text: i18n.t('待审批'),
          cssClass: ''
        },
        {
          value: i18n.t('审批不通过'),
          text: i18n.t('审批不通过'),
          cssClass: ''
        },
        {
          value: i18n.t('待发布'),
          text: i18n.t('待发布'),
          cssClass: ''
        },
        {
          value: i18n.t('发布待确认'),
          text: i18n.t('发布待确认'),
          cssClass: ''
        },
        { value: i18n.t('完成'), text: i18n.t('完成'), cssClass: '' },
        {
          value: i18n.t('关闭'),
          text: i18n.t('关闭'),
          cssClass: ''
        },
        {
          value: i18n.t('反馈正常'),
          text: i18n.t('反馈正常'),
          cssClass: ''
        },
        {
          value: i18n.t('反馈异常'),
          text: i18n.t('反馈异常'),
          cssClass: ''
        }
      ]
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    width: '130'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: MasterDataSelect.businessCompany,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'prototypeMachineCode',
    headerText: i18n.t('原型机编码'),
    width: '100'
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '300',
    searchOptions: MasterDataSelect.supplier,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  }
]
export const editColumnEnd = [
  {
    ignore: true,
    field: 'version1',
    headerText: i18n.t('变更情况'),
    width: '92',
    searchOptions: {
      customRule: (e) => {
        return {
          label: i18n.t('版本'),
          field: 'version',
          type: 'number',
          operator: 'contains',
          value: e
        }
      }
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e > 1) {
          return i18n.t('变更')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'source',
    headerText: i18n.t('订单来源'),
    width: '103',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('采购申请转化'),
        1: i18n.t('手动创建'),
        2: i18n.t('商城申请转化'),
        3: i18n.t('外部SAP'),
        4: i18n.t('合同转换')
      }
    }
  },
  {
    field: 'orderTime',
    headerText: i18n.t('订单日期'),
    width: '99',
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '95'
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('订单类型'),
    searchOptions: {
      renameField: 'orderTypeCode',

      ...MasterDataSelect.dictOrderType
    }
  },
  {
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组织'),
    searchOptions: MasterDataSelect.businessGroupUnit,
    width: '190',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '80',
    searchOptions: {
      ...MasterDataSelect.staff,
      fields: { text: 'title', value: 'externalCode' },
      renameField: 'buyerUserCode'
    }
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    width: '192',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.currencyCode}}-{{data.currencyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '107'
  },
  {
    field: 'supSalesOrderNo',
    headerText: i18n.t('供方销售订单号'),
    width: '160'
  },
  {
    field: 'syncSupSysStatus',
    headerText: i18n.t('同步供方系统状态'),
    width: '160',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('成功'),
        2: i18n.t('失败')
      }
    }
  },
  {
    field: 'syncSupSysFailDesc',
    headerText: i18n.t('同步供方系统失败原因'),
    width: '160'
  },
  {
    field: 'orderRemark',
    headerText: i18n.t('采方备注'),
    width: '155'
  },
  {
    field: 'orderSupRemark',
    headerText: i18n.t('供方备注'),
    width: '155'
  },
  {
    field: 'version',
    headerText: i18n.t('版本'),
    width: '73',
    // cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return 'V' + e
      }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '85'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'feedbackTime',
    headerText: i18n.t('反馈时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]
export const columnData1 = [
  //采购明细列表
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    width: '150',
    field: 'purchaseStrategy',
    headerText: i18n.t('采购策略'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('标准'),
        2: i18n.t('寄售'),
        3: i18n.t('第三方')
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    width: '125',
    searchOptions: {
      // ...MasterDataSelect.material,
      // renameField: 'itemCode'
      placeholder: i18n.t('多个物料编号需要用空格隔开'),
      // operator: 'in',
      // serializeValue: (e) => {
      //   let obj = e.split(',')
      //   //自定义搜索值，规则
      //   return obj
      // },
      maxQueryValueLength: 10000
    }
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '360',
    ignore: true
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编号'),
    width: '95',
    searchOptions: {
      ...MasterDataSelect.sku,
      renameField: 'skuCode'
    }
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称'),
    width: '150',
    ignore: true
  },
  { field: 'enclosure', ignore: true, headerText: i18n.t('附件') },
  {
    field: 'consignee',
    headerText: i18n.t('收货人'),
    width: '82',
    valueAccessor: function (field, data) {
      if (data?.consignee) {
        if (data.consignee.split('-')[3]) {
          return data?.consignee.split('-')[3]
        } else {
          return data?.consignee
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'techContactPerson',
    headerText: i18n.t('技术对接人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.techContactPerson) {
        if (data.techContactPerson.split('-')[3]) {
          return data?.techContactPerson.split('-')[3]
        } else {
          return data?.techContactPerson
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'provisionalEstimateStatus',
    headerText: i18n.t('是否暂估价'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'returnIdentification',
    headerText: i18n.t('退货标识'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'subjectType',
    headerText: i18n.t('科目类型'),
    width: '97',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('生产工单'),
        1: i18n.t('销售订单'),
        2: i18n.t('生产工单'),
        3: i18n.t('项目'),
        4: i18n.t('资产'),
        5: i18n.t('其他')
      }
    }
  },
  {
    field: 'timePromise',
    headerText: i18n.t('承诺日期'),
    width: '95',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'orderCosts',
    headerText: i18n.t('成本中心'),
    width: '206',
    ignore: true,
    searchOptions: {
      ...MasterDataSelect.costCenter,
      renameField: 'orderCosts'
    }
  },
  {
    field: 'warehouseCode',
    headerText: i18n.t('库存地点'),
    width: '250px',
    searchOptions: {
      ...MasterDataSelect.stockAddress,
      renameField: 'warehouseCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.warehouseCode}}-{{data.warehouse}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'profitCenterName',
    width: '294',
    headerText: i18n.t('利润中心'),
    searchOptions: {
      ...MasterDataSelect.profitCenter,
      renameField: 'profitCenterCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.profitCenterCode}}-{{data.profitCenterName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    width: '192',
    field: 'siteName',
    headerText: i18n.t('地点/工厂'), // 也得是个按钮，点击出弹窗
    searchOptions: {
      ...MasterDataSelect.factoryAddress,
      renameField: 'siteCode',
      operator: 'in'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          },
          mounted() {}
        })
      }
    }
  },
  {
    field: 'purUnitName',
    headerText: i18n.t('采购单位'),
    width: '250',
    searchOptions: {
      ...MasterDataSelect.unit,
      renameField: 'purUnitCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.purUnitCode}}-{{data.purUnitName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'unitName',
    headerText: i18n.t('基本单位'),
    searchOptions: {
      ...MasterDataSelect.unit,
      renameField: 'unitCode'
    },
    width: '93',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.unitCode}}-{{data.unitName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerOrgName',
    headerText: i18n.t('采购组'),
    width: 250,
    searchOptions: {
      ...MasterDataSelect.businessGroupIn,
      renameField: 'buyerOrgCode'
    },
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'contractRel',
    width: '150px',
    headerText: i18n.t('关联合同协议比那好')
  },
  {
    field: 'contractType',
    width: '150px',
    headerText: i18n.t('合同协议类型'),
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.contractType == '0') {
                str = i18n.t('单次采购')
              }
              if (this.data.contractType == '1') {
                str = i18n.t('框架协议')
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },

  {
    field: 'confirmStatus',
    headerText: i18n.t('供应商确认状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('待确认'),
        1: i18n.t('反馈异常'),
        2: i18n.t('反馈正常')
      }
    }
  },
  {
    field: 'requiredDeliveryDate',
    headerText: i18n.t('要求交期'),
    width: '98',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'urgentStatus',
    headerText: i18n.t('加急状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in',
      dataSource: [
        { label: i18n.t('普通'), value: 0 },
        { label: i18n.t('加急'), value: 1 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div>
              <span :style="urgentStatus === $t('加急') ? {color: 'red'} : {}">{{ urgentStatus }}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            urgentStatus() {
              return this.data.urgentStatus === 0 ? i18n.t('普通') : i18n.t('加急')
            }
          }
        })
      }
    }
  },
  {
    field: 'urgentTime',
    headerText: i18n.t('加急日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'warehouseStatus',
    headerText: i18n.t('入库状态'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未入库'),
        1: i18n.t('部分入库'),
        2: i18n.t('全部入库')
      }
    }
  },
  {
    field: 'requestOrderMethod',
    headerText: i18n.t('独立/集中'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('独立'), 2: i18n.t('集中') }
    }
  },
  {
    field: 'purchasingCycleStart',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (
                this.data.purchasingCycleStart &&
                this.data.purchasingCycleStart.length === '13'
              ) {
                str = UTILS.dateFormat(Number(this.data.purchasingCycleStart))
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'purchasingCycleEnd',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = ''
              if (this.data.purchasingCycleEnd && this.data.purchasingCycleEnd.length === '13') {
                str = UTILS.dateFormat(Number(this.data.purchasingCycleEnd))
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'agreementCode',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.contract
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'budgetQuantity',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.forecastQty
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'orderUnitId',
    width: '150',
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = this.data.purUnitId
              return str
            }
          }
        })
      }
    }
  },
  {
    field: 'receiveQty',
    headerText: i18n.t('已收货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'preDeliveryQty',
    headerText: i18n.t('待发货数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'warehouseQty',
    headerText: i18n.t('已入库数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'transitQty',
    headerText: i18n.t('在途数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'quantity',
    headerText: i18n.t('订单数量'),
    searchOptions: { elementType: 'number' }
  },
  {
    field: 'outstandingNum',
    headerText: i18n.t('订单未清数量'),
    searchOptions: { elementType: 'number' }
  }

  // {
  //   field: "orderCode",
  //   headerText: i18n.t("订单号码"),
  //   width: "150",
  // },
  // {
  //   field: "id",
  //   headerText: i18n.t("订单id"),
  //   width: "150",
  // },
]
export const columnData2 = [
  //验收计划
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    field: 'acceptanceStatus',
    headerText: i18n.t('验收状态'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未验收'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('验收中'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('验收确认'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('已验收'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'ys',
        icon: 'icon_list_acceptance',
        title: i18n.t('验收'),
        permission: ['O_02_0145'],
        visibleCondition: (data) => {
          const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
          return (
            data.acceptanceStatus == 0 &&
            (data.acceptorId === userInfo.employeeId || data.acceptorId === userInfo.uid)
          )
        }
      },
      {
        id: 'ch',
        icon: 'icon_list_recall',
        title: i18n.t('撤回'),
        permission: ['O_02_0146'],
        visibleCondition: (data) => {
          return data.acceptanceStatus == 3 && data.acceptanceType !== '1527602264118325250'
        }
      },
      {
        id: 'qr',
        icon: 'icon_list_acceptance',
        title: i18n.t('确认'),
        permission: ['O_02_1388'],
        visibleCondition: (data) => {
          return data.acceptanceStatus == 3
        }
      },
      {
        id: 'bh',
        icon: 'icon_list_recall',
        title: i18n.t('驳回'),
        permission: ['O_02_1389'],
        visibleCondition: (data) => {
          return data.acceptanceStatus == 3 && data.acceptanceType !== '1527602264118325250'
        }
      }
    ]
  },
  {
    field: 'acceptanceCode',
    headerText: i18n.t('验收单号'),
    width: '150'
  },
  {
    field: 'acceptanceCodeRel',
    headerText: i18n.t('同步关联单据号'),
    width: '150'
  },
  {
    field: 'advanceInvoiceStatus',
    headerText: i18n.t('提前开票'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'deliveryStatus1',
    headerText: i18n.t('延期状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        {
          value: i18n.t('已延期'),
          text: i18n.t('已延期'),
          cssClass: 'col-synced1'
        },
        {
          value: i18n.t('未延期'),
          text: i18n.t('未延期'),
          cssClass: 'col-notSynced1'
        }
      ]
    }
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用空格隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(' ')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'source',
    headerText: i18n.t('订单来源'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('采购申请转化'),
        1: i18n.t('手动创建'),
        2: i18n.t('商城申请转化'),
        3: i18n.t('外部SAP'),
        4: i18n.t('合同转换')
      }
    }
  },
  {
    field: 'orderDate',
    headerText: i18n.t('订单日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'projectCode',
    headerText: i18n.t('项目编号'),
    width: '150'
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    width: '150'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '230',
    searchOptions: MasterDataSelect.businessCompany,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '300',
    searchOptions: MasterDataSelect.supplier,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    width: '150'
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '150',
    ignore: true
  },
  {
    field: 'requireName',
    headerText: i18n.t('需求名称'),
    width: '150'
  },
  {
    field: 'demandCode',
    headerText: i18n.t('需求编码'),
    width: '160'
  },
  {
    field: 'requirementDescription',
    headerText: i18n.t('需求描述'),
    width: '150'
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编号'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.sku,
      renameField: 'skuCode'
    }
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称'),
    width: '150',
    ignore: true
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '150'
  },
  {
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组织'),
    searchOptions: MasterDataSelect.businessGroupUnit,
    width: '250',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.staff,
      renameField: 'buyerUserCode'
    }
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    width: '150',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.currencyCode}}-{{data.currencyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'orderRemark',
    headerText: i18n.t('备注'),
    width: '155'
  },
  {
    field: 'itemNo',
    headerText: i18n.t('行号'),
    width: '150'
  },
  {
    field: 'acceptanceTypeName',
    headerText: i18n.t('验收类型'),
    width: '150'
  },
  {
    field: 'advancePayStatus',
    headerText: i18n.t('是否预付'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    field: 'chargeStatus',
    headerText: i18n.t('是否挂账'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    field: 'description',
    headerText: i18n.t('验收描述'),
    width: '150'
  },
  {
    field: 'acceptanceQuantity',
    headerText: i18n.t('验收数量'),
    width: '150'
  },
  {
    field: 'freeTotal',
    headerText: i18n.t('未税总价'),
    width: '150',
    visible: () => {
      // 需求申请人角色登录时，隐藏未税总价，含税总价字段；
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const roleList = userInfo.roleList
      return !roleList.includes('FCXQSQYH')
    }
  },
  {
    field: 'taxTotal',
    headerText: i18n.t('含税总价'),
    width: '150',
    visible: () => {
      // 需求申请人角色登录时，隐藏未税总价，含税总价字段；
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      const roleList = userInfo.roleList
      return !roleList.includes('FCXQSQYH')
    }
  },
  {
    field: 'actualCheckUntaxedAmt',
    headerText: i18n.t('实际验收未税总价'),
    width: '150'
  },
  {
    field: 'actualCheckTaxedAmt',
    headerText: i18n.t('实际验收含税总价'),
    width: '150'
  },
  {
    field: 'payTypeName',
    headerText: i18n.t('付款类型'),
    width: '150'
  },
  {
    field: 'acceptor',
    headerText: i18n.t('验收人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.acceptor) {
        if (data.acceptor.split('-')[3]) {
          return data?.acceptor.split('-')[3]
        } else {
          return data?.acceptor
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'preAcceptanceTime',
    headerText: i18n.t('计划验收时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'acceptanceTime',
    headerText: i18n.t('实际验收时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'contractRel',
    headerText: i18n.t('价格记录编号'),
    width: '186'
  },
  {
    field: 'file',
    headerText: i18n.t('验收附件数量'),
    width: '150',
    ignore: true,
    template: function () {
      return {
        template: cellFileView
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('验收备注'),
    width: '150'
  }
]
export const columnData3 = [
  //验收计划
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    field: 'acceptanceStatus',
    headerText: i18n.t('验收状态'),
    width: '200',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未验收'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('验收中'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('验收确认'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('已验收'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: []
  },
  {
    field: 'acceptanceCode',
    headerText: i18n.t('验收单号'),
    width: '150'
  },
  {
    field: 'acceptanceCodeRel',
    headerText: i18n.t('同步关联单据号'),
    width: '150'
  },
  {
    field: 'advanceInvoiceStatus',
    headerText: i18n.t('提前开票'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('否'), 1: i18n.t('是') }
    }
  },
  {
    field: 'deliveryStatus1',
    headerText: i18n.t('延期状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: [
        {
          value: i18n.t('已延期'),
          text: i18n.t('已延期'),
          cssClass: 'col-synced1'
        },
        {
          value: i18n.t('未延期'),
          text: i18n.t('未延期'),
          cssClass: 'col-notSynced1'
        }
      ]
    }
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用空格隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(' ')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    }
  },
  {
    field: 'source',
    headerText: i18n.t('订单来源'),
    width: '150',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('采购申请转化'),
        1: i18n.t('手动创建'),
        2: i18n.t('商城申请转化'),
        3: i18n.t('外部SAP'),
        4: i18n.t('合同转换')
      }
    }
  },
  {
    field: 'orderDate',
    headerText: i18n.t('订单日期'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'projectCode',
    headerText: i18n.t('项目编号'),
    width: '150'
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    width: '150'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '250',
    searchOptions: MasterDataSelect.businessCompany,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '300',
    searchOptions: MasterDataSelect.supplier,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    width: '150'
    // searchOptions: {
    //   ...MasterDataSelect.material,
    //   renameField: "itemCode",
    // },
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    width: '150',
    ignore: true
  },
  {
    field: 'requireName',
    headerText: i18n.t('需求名称'),
    width: '160'
  },
  {
    field: 'demandCode',
    headerText: i18n.t('需求编码'),
    width: '160'
  },
  {
    field: 'requirementDescription',
    headerText: i18n.t('需求描述'),
    width: '150'
  },
  {
    field: 'skuCode',
    headerText: i18n.t('SKU编号'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.sku,
      renameField: 'skuCode'
    }
  },
  {
    field: 'skuName',
    headerText: i18n.t('SKU名称'),
    width: '150',
    ignore: true
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '92'
  },
  {
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组织'),
    searchOptions: MasterDataSelect.businessGroupUnit,
    width: '250',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '150',
    searchOptions: {
      ...MasterDataSelect.staff,
      renameField: 'buyerUserCode'
    }
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    width: '150',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.currencyCode}}-{{data.currencyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '150'
  },
  {
    field: 'orderRemark',
    headerText: i18n.t('备注'),
    width: '155'
  },
  {
    field: 'itemNo',
    headerText: i18n.t('行号'),
    width: '150'
  },
  {
    field: 'acceptanceTypeName',
    headerText: i18n.t('验收类型'),
    width: '150'
  },
  {
    field: 'qaTimeLimitValue',
    headerText: i18n.t('质保期'),
    width: '150',
    ignore: true
  },
  {
    field: 'advancePayStatus',
    headerText: i18n.t('是否预付'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    field: 'chargeStatus',
    headerText: i18n.t('是否挂账'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: { 1: i18n.t('是'), 0: i18n.t('否') }
    }
  },
  {
    field: 'description',
    headerText: i18n.t('验收描述'),
    width: '150'
  },
  {
    field: 'percentage',
    headerText: i18n.t('付款比例'),
    width: '150'
  },
  {
    field: 'freeTotal',
    headerText: i18n.t('未税总价'),
    width: '150'
  },
  {
    field: 'taxTotal',
    headerText: i18n.t('含税总价'),
    width: '150'
  },
  {
    field: 'actualCheckUntaxedAmt',
    headerText: i18n.t('实际验收未税总价'),
    width: '150'
  },
  {
    field: 'actualCheckTaxedAmt',
    headerText: i18n.t('实际验收含税总价'),
    width: '150'
  },
  {
    field: 'payTypeName',
    headerText: i18n.t('付款类型'),
    width: '150'
  },
  {
    field: 'acceptor',
    headerText: i18n.t('验收人'),
    width: '150',
    valueAccessor: function (field, data) {
      if (data?.acceptor) {
        if (data.acceptor.split('-')[3]) {
          return data?.acceptor.split('-')[3]
        } else {
          return data?.acceptor
        }
      } else {
        return ''
      }
    }
  },
  {
    field: 'preAcceptanceTime',
    headerText: i18n.t('计划验收时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'acceptanceTime',
    headerText: i18n.t('实际验收时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'contractRel',
    headerText: i18n.t('价格记录编号'),
    width: '186'
  },
  {
    field: 'file',
    headerText: i18n.t('验收附件数量'),
    width: '150',
    ignore: true,
    template: function () {
      return {
        template: cellFileView
      }
    }
  },
  {
    field: 'remark',
    headerText: i18n.t('验收备注'),
    width: '150'
  }
]
export const columnData = [
  //采购订单列表
  // {
  //   width: '50',
  //   type: 'checkbox',
  //   showInColumnChooser: false
  // },
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    isPrimaryKey: true
  },
  {
    field: 'contractNo',
    headerText: i18n.t('合同编号'),
    width: '150'
  },
  {
    field: 'orderCode',
    headerText: i18n.t('采购订单号'),
    width: '150',
    searchOptions: {
      placeholder: i18n.t('多个订单号需要用空格隔开'),
      operator: 'in',
      serializeValue: (e) => {
        let obj = e.split(' ')
        //自定义搜索值，规则
        return obj
      },
      maxQueryValueLength: 10000
    },
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        permission: ['O_02_0142'],
        visibleCondition: (data) => {
          let isShow = false
          if (
            (data.showStatus === i18n.t('草稿') ||
              data.showStatus === i18n.t('待发布') ||
              data.showStatus === i18n.t('审批不通过')) &&
            data.showStatus !== i18n.t('完成') &&
            data.showStatus !== i18n.t('关闭') &&
            data.showStatus !== i18n.t('待审批') &&
            data.source !== 3 // 3 来自SAP的数据
          ) {
            isShow = true
          }
          if (
            (data.feedbackStatus == 1 || data.feedbackStatus == 2) &&
            data.businessTypeIfEdit &&
            data.showStatus !== i18n.t('完成') &&
            data.showStatus !== i18n.t('关闭') &&
            data.showStatus !== i18n.t('待审批') &&
            data.source !== 3 // 3 来自SAP的数据
          ) {
            // 对于供方接收/供方拒绝状态下 && 根据策略配置中的该业务类型决定是否允许编辑
            isShow = true
          }

          // 反馈正常的状态不能编辑，针对非采（业务类型为：固资新增，固资装修，费用类）的订单；
          if (
            data.showStatus === i18n.t('反馈正常') &&
            (data.businessTypeCode === 'BTTCL001' ||
              data.businessTypeCode === 'BTTCL002' ||
              data.businessTypeCode === 'BTTCL003')
          ) {
            // 对于供方接收/供方拒绝状态下 && 根据策略配置中的该业务类型决定是否允许编辑
            isShow = false
          }

          return isShow
        }
      }
      // {
      //   id: "see",
      //   icon: "icon_Editor",
      //   title: i18n.t("查看"),
      //   visibleCondition: (data) => {
      //     return !data.orderCode;
      //   },
      // },
    ]
  },
  // {
  //   field: "status",
  //   headerText: i18n.t("订单状态"),
  //   width: 0,
  //   valueConverter: {
  //     type: "map",
  //     map: {
  //       0: i18n.t("草稿"),
  //       1: i18n.t("待审批"),
  //       2: i18n.t("审批通过"),
  //       3: i18n.t("审批拒绝"),
  //       4: i18n.t("关闭"),
  //       5: i18n.t("完成"),
  //     },
  //   },
  // },
  {
    field: 'showStatus',
    headerText: i18n.t('订单状态'),
    width: '103',
    valueConverter: {
      type: 'map',
      map: [
        { value: i18n.t('草稿'), text: i18n.t('草稿'), cssClass: 'col-active' },
        {
          value: i18n.t('待审批'),
          text: i18n.t('待审批'),
          cssClass: 'col-active'
        },
        // {
        //   value: i18n.t("审批通过"),
        //   text: i18n.t("审批通过"),
        //   cssClass: "col-active",
        // },
        {
          value: i18n.t('审批不通过'),
          text: i18n.t('审批不通过'),
          cssClass: 'col-active'
        },
        {
          value: i18n.t('待发布'),
          text: i18n.t('待发布'),
          cssClass: 'col-active'
        },
        {
          value: i18n.t('发布待确认'),
          text: i18n.t('发布待确认'),
          cssClass: 'col-active'
        },
        { value: i18n.t('完成'), text: i18n.t('完成'), cssClass: 'col-active' },
        {
          value: i18n.t('关闭'),
          text: i18n.t('关闭'),
          cssClass: 'col-inactive'
        },
        {
          value: i18n.t('反馈正常'),
          text: i18n.t('反馈正常'),
          cssClass: 'col-active'
        },
        {
          value: i18n.t('反馈异常'),
          text: i18n.t('反馈异常'),
          cssClass: 'col-error'
        }
      ]
    },
    cellTools: [
      {
        id: 'btn1',
        icon: 'a-icon_list_Approvalprogress',
        title: i18n.t('查看审批'),
        visibleCondition: () => {
          return false
        }
      },
      {
        id: 'btn2',
        icon: 'icon_Editor',
        title: i18n.t('售后'),
        permission: ['O_02_0144'],
        visibleCondition: (data) => {
          return (data.receiveStatus == 1 || data.receiveStatus == 2) && data.afterSaleFlag == 1 // 收货状态 = 1-部分收货 || 2-全部收货 && 是否售后状态 = 1-是
        }
      },
      {
        id: 'btn3',
        icon: 'icon_list_close',
        title: i18n.t('关闭'),
        permission: ['O_02_0143'],
        visibleCondition: (data) => {
          return (
            // (data.showStatus === i18n.t("草稿") ||
            //   data.showStatus === i18n.t("审批拒绝") ||
            //   data.feedbackStatus === 1 ||
            //   data.feedbackStatus === 2) &&
            data.receiveStatus !== 2 && // 收货状态不等于全部收货
            data.showStatus !== i18n.t('完成') && // 订单状态
            data.showStatus !== i18n.t('关闭') && // 订单状态
            data.source !== 3 && // 订单来源不等于外部SAP
            data.businessTypeCode !== 'BTTCL001' &&
            data.businessTypeCode !== 'BTTCL002' &&
            data.businessTypeCode !== 'BTTCL003'
          )
        }
      },
      {
        id: 'btn4',
        icon: 'icon_list_accept',
        title: i18n.t('完成'),
        permission: ['O_02_0147'],
        visibleCondition: (data) => {
          return (
            (data.feedbackStatus === 1 || data.feedbackStatus === 2) &&
            data.receiveStatus !== 2 &&
            data.showStatus !== i18n.t('完成') &&
            data.source !== 3 &&
            !(
              data.source === 4 &&
              data.showStatus === i18n.t('反馈正常') &&
              (data.businessTypeCode === 'BTTCL001' ||
                data.businessTypeCode === 'BTTCL002' ||
                data.businessTypeCode === 'BTTCL003')
            )
          )
        }
      }
    ],
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    field: 'version1',
    headerText: i18n.t('变更情况'),
    width: '95',
    ignore: true,
    searchOptions: {
      customRule: (e) => {
        return {
          label: i18n.t('版本'),
          field: 'version',
          type: 'number',
          operator: 'contains',
          value: e
        }
      }
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e > 1) {
          return i18n.t('变更')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'urgentStatus',
    headerText: i18n.t('加急状态'),
    width: '96',
    // valueConverter: {
    //   type: 'map',
    //   map: {
    //     0: i18n.t('普通'),
    //     1: i18n.t('加急')
    //   }
    // },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in',
      dataSource: [
        { label: i18n.t('普通'), value: 0 },
        { label: i18n.t('加急'), value: 1 }
      ],
      fields: { text: 'label', value: 'value' }
    },
    template: () => {
      return {
        template: Vue.component('status', {
          template: `
            <div>
              <span :style="urgentStatus === $t('加急') ? {color: 'red'} : {}">{{ urgentStatus }}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          },
          computed: {
            urgentStatus() {
              return this.data.urgentStatus === 0 ? i18n.t('普通') : i18n.t('加急')
            }
          }
        })
      }
    }
  },
  {
    field: 'urgentTime',
    headerText: i18n.t('加急日期'),
    width: '98',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    },
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    field: 'deliveryStatus',
    headerText: i18n.t('发货状态'),
    width: '98',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未发货'),
        1: i18n.t('部分发货'),
        2: i18n.t('全部发货')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  {
    field: 'receiveStatus',
    headerText: i18n.t('收货状态'),
    width: '98',
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('未收货'),
        1: i18n.t('部分收货'),
        2: i18n.t('全部收货')
      }
    },
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    }
  },
  // {
  //   field: "orderRel",
  //   headerText: i18n.t("关联单据号"),
  //   width: "150",
  // },
  {
    field: 'source',
    headerText: i18n.t('订单来源'),
    width: '98',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('采购申请转化'),
        1: i18n.t('手动创建'),
        2: i18n.t('商城申请转化'),
        3: i18n.t('外部SAP'),
        4: i18n.t('合同转换')
      }
    }
  },
  {
    field: 'orderTime',
    headerText: i18n.t('订单日期'),
    width: '98',
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    // searchOptions: {
    //   customRule: (e) => {
    //     return {
    //       label: i18n.t("版本"),
    //       field: "version",
    //       type: "number",
    //       operator: "contains",
    //       value: "123+" + e,
    //     };
    //   },
    // },
    // type: "datetime",
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'voucherDate',
    headerText: i18n.t('凭证日期'),
    width: '98',
    searchOptions: {
      ...MasterDataSelect.timeRange
    },
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e) {
          return e.substring(0, 10)
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'projectName',
    headerText: i18n.t('项目名称'),
    width: '130'
  },
  {
    field: 'companyCode',
    headerText: i18n.t('公司'),
    width: '230',
    searchOptions: MasterDataSelect.businessCompanyPermission,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    width: '170',
    searchOptions: MasterDataSelect.supplier,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <div class="headers">
              <span>{{data.supplierCode}}-{{data.supplierName}}</span>
            </div>
          `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    width: '92'
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('订单类型'),
    width: '153'
  },
  // {
  //   field: "requiredDeliveryDate",
  //   headerText: i18n.t("要求交期"),
  //   width: "150",
  //   valueConverter: {
  //     type: "function",
  //     filter: (e) => {
  //       if (e && e.length === 13) {
  //         e = Number(e);
  //         return UTILS.dateFormat(e, "Y-m-d");
  //       } else {
  //         return "";
  //       }
  //     },
  //   },
  // },
  {
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组织'),
    searchOptions: MasterDataSelect.businessGroupUnit,
    width: '180',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    headerText: i18n.t('采购员'),
    width: '82'
    // searchOptions: {
    //   ...MasterDataSelect.staff,
    //   fields: { text: 'title', value: 'externalCode' },
    //   renameField: 'buyerUserCode'
    // }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组'),
    width: '92',
    searchOptions: MasterDataSelect.businessGroupIn,
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    width: '116',
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.currencyCode}}-{{data.currencyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    field: 'paymentName',
    headerText: i18n.t('付款条件'),
    width: '250'
  },
  // {
  //   field: "orderRel12",
  //   headerText: i18n.t("供应商地点"),
  //   width: "150",
  // },
  {
    field: 'supSalesOrderNo',
    headerText: i18n.t('供方销售订单号'),
    width: '160'
  },
  {
    field: 'syncSupSysStatus',
    headerText: i18n.t('同步供方系统状态'),
    width: '160',
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('成功'),
        2: i18n.t('失败')
      }
    }
  },
  {
    field: 'syncSupSysFailDesc',
    headerText: i18n.t('同步供方系统失败原因'),
    width: '160'
  },
  {
    field: 'remark',
    headerText: i18n.t('采方备注'),
    width: '95'
  },
  {
    field: 'supRemark',
    headerText: i18n.t('供方备注'),
    width: '95'
  },
  {
    field: 'version',
    headerText: i18n.t('版本'),
    width: '70',
    // cellTools: [],
    valueConverter: {
      type: 'function',
      filter: (e) => {
        return 'V' + e
      }
    }
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人'),
    width: '83'
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    field: 'feedbackTime',
    headerText: i18n.t('反馈时间'),
    width: '150',
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length === 13) {
          e = Number(e)
          return UTILS.dateFormat(e)
        } else {
          return ''
        }
      }
    },
    searchOptions: { ...MasterDataSelect.timeRange }
  }
]
