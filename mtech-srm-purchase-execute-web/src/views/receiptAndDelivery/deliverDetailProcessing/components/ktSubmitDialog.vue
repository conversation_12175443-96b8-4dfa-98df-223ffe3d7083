<template>
  <mt-dialog
    ref="dialog"
    css-class="kt-submit-dialog"
    :width="1200"
    min-height="600"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <ScTable
        ref="scTableRef"
        :columns="columns"
        :table-data="tableData"
        :loading="loading"
        :fix-height="350"
        :is-show-right-btn="false"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'

export default {
  components: {
    ScTable
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      tableData: [],
      columns: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          field: 'outsourcedOrder',
          title: this.$t('匹配到的委外订单号'),
          minWidth: 120
        },
        {
          field: 'matchMsg',
          title: this.$t('匹配状态'),
          minWidth: 120
        },
        {
          field: 'deliveryQty',
          title: this.$t('送货数量'),
          minWidth: 120
        },
        {
          field: 'receiveQuantity',
          title: this.$t('收货数量'),
          minWidth: 120
        },
        {
          field: 'deliveryCode',
          title: this.$t('送货单号'),
          minWidth: 150
        },
        {
          field: 'deliveryLineNo',
          title: this.$t('行号'),
          minWidth: 80
        },
        {
          field: 'siteCode',
          title: this.$t('工厂代码'),
          minWidth: 120
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 120
        },
        {
          field: 'proTenantCode',
          title: this.$t('加工商编码'),
          minWidth: 120
        },
        {
          field: 'supplierCode',
          title: this.$t('原材料供应商编码'),
          minWidth: 180
        },
        {
          field: 'siteName',
          title: this.$t('工厂名称'),
          minWidth: 150
        },
        {
          field: 'itemName',
          title: this.$t('物料名称'),
          minWidth: 150
        },
        {
          field: 'proTenantName',
          title: this.$t('加工商名称'),
          minWidth: 150
        },
        {
          field: 'supplierName',
          title: this.$t('原材料供应商名称'),
          minWidth: 180
        }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.scTableRef.$refs.xGrid
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.initData()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    initData() {
      this.tableData = this.modalData.data
    },
    confirm() {
      const selectedRows = this.tableRef.getCheckboxRecords()

      if (selectedRows.length === 0) {
        this.$toast({
          content: this.$t('请至少选择一行数据'),
          type: 'warning'
        })
        return
      }

      // 传出选中的行数据
      this.$emit('confirm-function', selectedRows)
    },

    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px;
}

.kt-submit-dialog {
  .e-dialog-content {
    padding: 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
