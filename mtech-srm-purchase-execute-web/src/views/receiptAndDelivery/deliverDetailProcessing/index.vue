<template>
  <!-- 送货单-收货-详情-加工方（供方） -->
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      v-if="headerInfo"
      ref="headerTop"
      :header-info="headerInfo"
      class="flex-keep"
      @doSubmit="checkSubmitDialog"
    ></top-info>
    <mt-tabs
      :e-tab="false"
      tab-id="deliver-tab"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div v-if="isShow">
      <div class="flex-fit" v-show="currentInfo.title == $t('物料信息')">
        <mt-template-page
          ref="templateRef"
          :template-config="pageConfig1"
          :hidden-tabs="true"
          @cellEdit="cellEdit"
          @notEdit="notEdit"
        />
      </div>
      <bottomInfo
        class="flex-fit"
        ref="bottomInfo"
        v-show="currentInfo.title == $t('物流信息')"
      ></bottomInfo>
    </div>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
// import { BASE_TENANT } from "@/utils/constant";
import topInfo from './components/topInfo.vue'
import bottomInfo from './components/bottominfo.vue'

export default {
  components: {
    topInfo,
    bottomInfo
  },
  data() {
    const id = this.$route.query.id
    const type = this.$route.query.type

    return {
      isShow: false,
      id,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      pageConfig: [{ title: this.$t('物料信息') }, { title: this.$t('物流信息') }],
      userInfo: null,
      headerInfo: null,
      currentInfo: {
        title: this.$t('物料信息')
      },
      type,
      pageConfig1: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false, // 不使用组件中的toolbar配置
          toolbar: [],
          // gridId:
          //   this.$tableUUID.receiptAndDelivery.deliverDetailProcessing
          //     .materialInfo,
          grid: {
            columnData,
            allowPaging: false, // 不分页
            lineIndex: 0,
            // lineSelection: 0, // 选项列
            // lineIndex: 1,
            dataSource: []
            // asyncConfig: {
            //   url: `${BASE_TENANT}/supplierOrderDeliveryItem/pro/page`,
            //   defaultRules: [
            //     {
            //       field: "deliveryId",
            //       operator: "equal",
            //       value: id,
            //     },
            //   ],
            // },
            // frozenColumns: 1,
          }
        }
      ],
      editData: [], // 行编辑过的数据
      currentTabIndex: 0
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 提交
    doSubmit(selectedData) {
      // 如果没有传入selectedData，则使用当前页面数据
      const dataSource = selectedData || this.pageConfig1[0].grid.dataSource
      const param = []

      dataSource.forEach((item) => {
        param.push({
          id: item.id,
          receiveQuantity: item.theNumberOfShipments || 0,
          rejectReason: item.rejectReason
        })
      })
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .supplierOrderDeliveryConfirm(param)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.goBack()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 检查提交弹窗
    checkSubmitDialog() {
      const selectedData = this.pageConfig1[0].grid.dataSource
      let params = selectedData.map((item) => {
        return {
          id: item.id,
          receiveQuantity: item.theNumberOfShipments || 0,
          rejectReason: item.rejectReason
        }
      })
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryMatchOutsourcedOrder(params)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code === 200) {
            if (res.data.length !== 0) {
              let data = res.data || []
              // 有匹配到的委外订单，弹框
              this.showKtSubmitDialog(data)
            } else {
              // 没有匹配到的委外订单，直接提交
              this.doSubmit(selectedData)
            }
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          // 接口调用失败，直接提交
          this.doSubmit(selectedData)
        })
    },
    // 显示提交弹窗
    showKtSubmitDialog(matchedData) {
      this.$dialog({
        modal: () => import('./components/ktSubmitDialog.vue'),
        data: {
          title: this.$t('委外订单提交确认'),
          data: matchedData
        },
        success: (selectedRows) => {
          if (selectedRows.length > 0) {
            this.doSubmit(selectedRows)
          }
        }
      })
    },
    goBack() {
      this.$router.push('deliver-list-processing')
    },
    // 行编辑
    cellEdit(e) {
      // 更新当前页 dataSource
      this.pageConfig1[0].grid.dataSource.splice(e.index, 1, {
        ...this.pageConfig1[0].grid.dataSource[e.index],
        [e.key]: e.value,
        rejectQuantity: Number(
          this.pageConfig1[0].grid.dataSource[e.index].deliveryQuantity - e.value
        )
      })

      console.log(this.pageConfig1[0].grid.dataSource)
    },
    notEdit(e) {
      if (this.pageConfig1[0].grid.dataSource[e.index].rejectReason !== e.value) {
        this.pageConfig1[0].grid.dataSource.splice(e.index, 1, {
          ...this.pageConfig1[0].grid.dataSource[e.index],
          [e.key]: e.value
        })
      }
    },
    // 初始化
    init() {
      this.headerInfo = JSON.parse(localStorage.getItem('deliverDetailSupplier'))
      if (this.headerInfo.deliveryType == 1) {
        this.headerInfo.deliveryType = this.$t('采购订单')
      } else if (this.headerInfo.deliveryType == 2) {
        this.headerInfo.deliveryType = this.$t('交货计划')
      } else if (this.headerInfo.deliveryType == 3) {
        this.headerInfo.deliveryType = this.$t('JIT')
      } else if (this.headerInfo.deliveryType == 4) {
        this.headerInfo.deliveryType = this.$t('无需求')
      } else if (this.headerInfo.deliveryType == 5) {
        this.headerInfo.deliveryType = this.$t('vmi')
      } else if (this.headerInfo.deliveryType == 6) {
        this.headerInfo.deliveryType = this.$t('钢材')
      }
      this.headerInfo.status === 1
        ? this.$t('新建')
        : this.headerInfo.status === 2
        ? this.$t('发货中')
        : this.headerInfo.status === 3
        ? this.$t('已完成')
        : this.headerInfo.status === 4
        ? this.$t('已取消')
        : this.headerInfo.status === 5
        ? this.$t('已关闭')
        : ''
      this.headerInfo.supplier = this.headerInfo.supplierCode + '-' + this.headerInfo.supplierName
      this.headerInfo.site = this.headerInfo.siteCode + '-' + this.headerInfo.siteName
      this.headerInfo.warehouse = this.headerInfo.warehouseCode
        ? this.headerInfo.warehouseCode + '-' + this.headerInfo.warehouseName
        : ''
      this.headerInfo.company = this.headerInfo.companyCode + '-' + this.headerInfo.companyName
      this.headerInfo.receiver = this.headerInfo.receiverCode
        ? this.headerInfo.receiverCode + '-' + this.headerInfo.receiverName
        : ''
      this.headerInfo.thirdTenant = this.headerInfo.thirdTenantCode
        ? this.headerInfo.thirdTenantCode + '-' + this.headerInfo.thirdTenantName
        : ''
      this.headerInfo.vmiWarehouse = this.headerInfo.vmiWarehouseCode
        ? this.headerInfo.vmiWarehouseCode + '-' + this.headerInfo.vmiWarehouseName
        : ''

      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .postSupplierOrderDeliveryItemQuery([this.$route.query.id])
        .then((res) => {
          if (res?.code == 200) {
            this.pageConfig1[0].grid.dataSource = this.serializeList(res?.data || [])
            this.isShow = true
            this.$store.commit('endLoading')
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleSelectTab(index, item) {
      this.currentInfo = item
      console.log(item)
      // this.currentTabIndex = e;
    },
    // 序列化表格数据
    serializeList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          if (item.receiveQuantity) {
            item.theNumberOfShipments = item.receiveQuantity || 0
          } else {
            // 收货数量(前端定义) 默认等于 发货数量
            item.theNumberOfShipments = item.deliveryQuantity || 0
          }
          // 将编辑过的数据设置到表格中
          const editData = this.editData.find((itemEdit) => itemEdit.id === item.id)
          if (editData !== undefined) {
            item[editData.key] = editData.value
          }
        })
      }
      return list
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  // 消失
  beforeDestroy() {
    localStorage.removeItem('deliverDetailSupplier')
  }
}
</script>

<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
