<template>
  <!-- 送货单-收货-加工方（供方） -->
  <!--
     @rowSelecting="rowSelecting"
      @rowDeselecting="rowdeSelecting"
   -->
  <div class="full-height">
    <mt-template-page
      @handleClickCellTool="handleClickCellTool"
      @handleClickToolBar="handleClickToolBar"
      @rowSelecting="rowSelecting"
      @rowDeselected="rowDeselecting"
      :current-tab="currentTabIndex"
      @cellEdit="cellEdit"
      :permission-obj="permissionObj"
      ref="templateRef"
      :template-config="pageConfig1"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData, columnData2, Status, SyncStatus } from './config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

import { cloneDeep } from 'lodash'
export default {
  mounted() {
    if (this.$route.query.from === 'mytodo') {
      this.currentTabIndex = 1
    }
    if (this.$route.query.from === 'mydelivery') {
      // const { tenantId } = JSON.parse(sessionStorage.getItem("userInfo"));
      // this.pageConfig1[0].grid.asyncConfig.defaultRules = [
      //   {
      //     field: "proTenantId", // 加工商租户id
      //     type: "string",
      //     operator: "equal",
      //     value: tenantId,
      //   },
      //   {
      //     field: "syncReceiveStatus",
      //     label: "同步状态",
      //     operator: "equal",
      //     type: "string",
      //     value: 3,
      //   },
      // ];
      this.currentTabIndex = 0
    }
  },
  data() {
    const { tenantId } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息

    return {
      currentTabIndex: 0,
      tenantId,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      editdata: [], //编辑维护的数组
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          // { dataPermission: 'MainSheetView', permissionCode: 'T_02_0015' },
          // { dataPermission: 'DetailView', permissionCode: 'T_02_0016' }
        ]
      },
      pageConfig1: [
        {
          title: this.$t('主单视图'),
          dataPermission: 'MainSheetView',
          permissionCode: 'T_02_0015',
          toolbar: [
            {
              id: 'export1',
              icon: 'icon_solid_Import',
              permission: ['O_02_1683', 'O_02_1685', 'O_02_1687'],
              title: this.$t('导出')
            }
            // {
            //   id: 'syncSap',
            //   icon: 'icon_table_synchronize',
            //   title: this.$t('同步')
            // }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/supplierOrderDelivery/pro/query`,
              defaultRules:
                this.$route.query.from === 'mydelivery'
                  ? [
                      {
                        field: 'proTenantId', // 加工商租户id
                        type: 'string',
                        operator: 'equal',
                        value: tenantId
                      },
                      {
                        field: 'syncReceiveStatus',
                        label: this.$t('同步状态'),
                        operator: 'equal',
                        type: 'string',
                        value: 3
                      }
                    ]
                  : [
                      {
                        field: 'proTenantId', // 加工商租户id
                        type: 'string',
                        operator: 'equal',
                        value: tenantId
                      }
                    ]
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('待收货'),
          dataPermission: 'DetailView',
          permissionCode: 'T_02_0016',
          toolbar: [
            {
              id: 'Submit',
              icon: 'icon_solid_Import',
              permission: ['O_02_1680', 'O_02_1681', 'O_02_1682'],
              title: this.$t('提交')
            }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          grid: {
            columnData: columnData2,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            rowDataBound: (args) => {
              if (args.data.status != 2) {
                args.row.classList.add('forbidCheck')
              }
            },
            // allowPaging: false,

            asyncConfig: {
              // page: {
              //   current: 1,
              //   size: 10000,
              // },
              url: `${BASE_TENANT}/supplierOrderDeliveryItem/pro/page`,
              defaultRules: [
                {
                  field: 'proTenantId', // 加工商租户id
                  type: 'string',
                  operator: 'equal',
                  value: tenantId
                },
                {
                  field: 'status', // 送货中
                  type: 'string',
                  operator: 'equal',
                  value: 2
                }
              ],
              serializeList: (list) => {
                console.log(list, this.editdata)
                if (this.editdata.length > 0) {
                  this.editdata.forEach((item) => {
                    let index = list.findIndex((e) => e.id == item.id)
                    if (index > -1) {
                      list[index] = { ...item }
                    }
                  })
                  return list
                } else {
                  return list
                }
              }
            },
            frozenColumns: 1
          }
        },
        {
          title: this.$t('明细视图'),
          // dataPermission: "DetailView",
          // permissionCode: "T_02_0016",
          toolbar: [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              permission: ['O_02_1684', 'O_02_1686', 'O_02_1688'],
              title: this.$t('导出')
            }
          ],
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置
          gridId: 'c206bdfd-fc93-4015-bf3b-828c0e836e77',
          grid: {
            columnData: columnData2,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            rowDataBound: (args) => {
              if (args.data.status != 2) {
                args.row.classList.add('forbidCheck')
              }
            },

            asyncConfig: {
              url: `${BASE_TENANT}/supplierOrderDeliveryItem/pro/page`,
              defaultRules: [
                {
                  field: 'proTenantId', // 加工商租户id
                  type: 'string',
                  operator: 'equal',
                  value: tenantId
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      checkboxArr: []
    }
  },
  beforeDestroy() {
    localStorage.removeItem('deliverDetailSupplier')
  },
  methods: {
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      if (toolbar.id === 'syncSap') {
        // 同步sap
        const selectedIds = []
        const selectedData = gridRef.getMtechGridRecords()
        selectedData.forEach((item) => {
          selectedIds.push(item.id)
        })
        if (gridRef.getMtechGridRecords()?.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        } else {
          this.postSupplierSyncSap({ selectedIds })
        }
      }
      if (args.toolbar.id == 'export') {
        let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[2].gridId))?.visibleCols
        const headerMap = {}
        if (obj !== undefined && obj.length) {
          obj?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        } else {
          this.pageConfig1[2].grid.columnData?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        }
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        console.log(queryBuilderRules)
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules,
          defaultRules: [
            {
              field: 'proTenantId', // 加工商租户id
              type: 'string',
              operator: 'equal',
              value: this.tenantId
            }
          ]
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery
          .postSupplierOrderDeliveryItem(params, headerMap)
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
        return
      }
      if (args.toolbar.id == 'export1') {
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[0].gridId))?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData.forEach((item) => {
            if (item.fieldCode) {
              field.push(item.field)
            }
          })
        }
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules,
          defaultRules: [
            {
              field: 'proTenantId', // 加工商租户id
              type: 'string',
              operator: 'equal',
              value: this.tenantId
            }
          ],
          sortedColumnStr: field.toString()
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.supplierOrderQueryExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (toolbar.id === 'Submit') {
        const _selectedData = gridRef.getMtechGridRecords()
        this.handleClickToolBarSubmit(_selectedData)
      }
    },
    //点击提交
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editdata.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      incluArr.map((item) => {
        item.receiveQuantity = item.deliveryQuantity
      })
      //编辑的值替换勾选的值
      this.editdata.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)

      // 检查是否需要显示提交弹窗
      this.checkSubmitDialog(_selectedData)
    },
    // 检查提交弹窗
    checkSubmitDialog(selectedData) {
      let params = selectedData.map((item) => {
        return {
          id: item.id,
          receiveQuantity: item.receiveQuantity,
          rejectReason: item.rejectReason
        }
      })
      this.$loading()
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryMatchOutsourcedOrder(params)
        .then((res) => {
          if (res.code === 200) {
            if (res.data.length !== 0) {
              let data = res.data || []
              // 有匹配到的委外订单，弹框
              this.showKtSubmitDialog(data)
            } else {
              // 没有匹配到的委外订单，直接提交
              this.doSubmit(selectedData)
            }
          }
        })
        .catch(() => {
          // 接口调用失败，直接提交
          this.doSubmit(selectedData)
        })
        .finally(() => {
          this.$hloading()
        })
    },
    // 显示提交弹窗
    showKtSubmitDialog(matchedData) {
      this.$dialog({
        modal: () => import('./components/ktSubmitDialog.vue'),
        data: {
          title: this.$t('委外订单提交确认'),
          data: matchedData
        },
        success: (selectedRows) => {
          if (selectedRows.length > 0) {
            this.doSubmit(selectedRows)
          }
        }
      })
    },
    doSubmit(_selectedData) {
      let row = _selectedData
      let rowLine = []
      row.forEach((item) => {
        rowLine.push({
          deliveryCode: item.deliveryCode
        })
      })
      var run = {}
      rowLine = rowLine.reduce(function (item, next) {
        run[next.deliveryCode] ? '' : (run[next.deliveryCode] = true && item.push(next))
        return item
      }, [])
      let obj = []
      rowLine.forEach((item) => {
        let newArr = row.filter((e) => item.deliveryCode === e.deliveryCode)
        let newArrObj = newArr.map((r) => {
          return {
            id: r.id,
            receiveQuantity: r.receiveQuantity,
            rejectReason: r.rejectReason,
            outsourcedOrder: r.outsourcedOrder
          }
        })
        obj.push({
          reqList: newArrObj
        })
      })

      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .batchConfirm(obj)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code == 200) {
            this.editdata = []
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 同步sap
    postSupplierSyncSap(args) {
      const { selectedIds } = args
      const params = selectedIds
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postProcessorReceiveSyncSap(params)
        .then(() => {
          this.apiEndLoading()

          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 判断选择的数据是否符合要求
    checkSelectedValid(data) {
      const { selectedData, actionType } = data
      let valid = true
      let validStatus = true // 状态
      let validSyncStatus = true // 同步状态
      if (selectedData && selectedData.length > 0) {
        if (actionType === 'syncSap') {
          // 同步sap
          for (let i = 0; i < selectedData.length; i++) {
            // 可同步条件： 状态 已完成，同步状态 同步失败

            if (selectedData[i].status !== Status.completed) {
              // 状态 不为 已完成
              validStatus = false
              valid = false
            }

            if (selectedData[i].syncReceiveStatus !== SyncStatus.failed) {
              // 同步状态 不为 同步失败
              validSyncStatus = false
              valid = false
            }
          }
        }
      }

      return {
        valid,
        validStatus,
        validSyncStatus
      }
    },

    handleClickCellTool(e) {
      console.log(e)
      if (e.tool.title === this.$t('确认收货')) {
        e.data.outsourcedType = Number(e.data.outsourcedType)
        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))

        this.$router.push({
          name: 'deliver-detail-processing',
          query: {
            id: e.data.id,
            timeStamp: new Date().getTime()
          }
        })
      }
      // 附件
      if (e.tool.id === 'delivery_scanning') {
        this.handleFile(e.data, e.tool.id)
      }
    },
    handleFile(row, type) {
      const typeMap = {
        delivery_scanning: 9
      }
      this.$dialog({
        modal: () => import('./../deliverListSupplierTV/components/fileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          id: row.id,
          type: typeMap[type],
          row,
          pageType: 'receiptListProcess'
        }
      })
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return
      // if (e?.data?.status != 2) {
      //   let mapArr = e.rowIndexes.filter((item) => {
      //     return item !== e.rowIndex;
      //   });
      //   if (!(e.data instanceof Array)) {
      //     console.log(e, mapArr);
      //     this.$nextTick(() => {
      //       this.$refs.templateRef
      //         .getCurrentUsefulRef()
      //         .ejsRef.selectRows(mapArr);
      //     });
      //   }
      //   return;
      // }
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.deliveryCode === Obj[j]?.deliveryCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
      }
    },
    //取消
    rowDeselecting(e) {
      if (e?.rowIndexes?.length < 1) return
      //获取当前页所有的行
      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef
          .getCurrentUsefulRef()
          .ejsRef.getSelectedRecords()
          .map((e) => e.id)
      )
      let mapArr = []
      Obj.filter((item, index) => {
        if (item.deliveryCode !== e.data.deliveryCode && currentSelect.includes(item.id)) {
          console.log(item)
          mapArr.push(index)
        }
      })
      console.log(e, mapArr)
      if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0) {
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
      }
    },
    //编辑行内
    cellEdit(e) {
      let { type, status, data } = e
      let flag = false
      if (status) {
        if (this.editdata && this.editdata.length < 1) {
          this.editdata.push(data)
        } else {
          for (let i = 0; i < this.editdata.length; i++) {
            if (this.editdata[i].id !== data.id) {
              flag = true
              break
            }
          }
          if (flag) {
            let arr = this.editdata.map((item) => item.id)
            let indexOf = arr.indexOf(data.id)
            if (indexOf == -1) {
              this.editdata.push(data)
            }
          }
          this.editdata.map((item) => {
            if (type === 'text' && item.id === data.id) {
              item.rejectReason = data.rejectReason
            } else if (type === 'number' && item.id === data.id) {
              item.rejectQuantity = data.rejectQuantity
              item.receiveQuantity = data.receiveQuantity
            }
          })
        }
      } else {
        if (this.editdata && this.editdata.length < 1) return
        this.editdata.map((item, i) => {
          if (item.id === data.id) {
            this.editdata.splice(i, 1)
          }
        })
      }

      console.log(this.editdata)
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.forbidCheck {
  .e-frame {
    border: 1px solid #d4d3d3;
    background: #d4d3d3;
    cursor: not-allowed;
  }
  .e-frame:hover {
    border: 1px solid #d4d3d3;
    background: #d4d3d3;
    cursor: not-allowed;
  }
  .e-checkbox-wrapper {
    display: none;
  }
  // .e-checkbox-wrapper:hover {
  //   cursor: not-allowed;
  //   .e-frame {
  //     border: 1px solid #d4d3d3;
  //     background: #d4d3d3;
  //     cursor: not-allowed;
  //   }
  // }
  .e-gridchkbox::after {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    border: 1px solid #d4d3d3;
    border-radius: 1px;
    background: #d4d3d3;
  }

  .e-gridchkbox:hover {
    .e-frame {
      border: 1px solid #d4d3d3;
      background: #d4d3d3;
      cursor: not-allowed;
    }
    cursor: not-allowed;
  }
}
</style>
