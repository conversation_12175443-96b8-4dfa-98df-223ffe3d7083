import { timeNumberToDate, timeStringToDate, numberInputOnKeyDown } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
const timeDate = (dataKey, hasTime) => {
  // const { dataKey, hasTime } = args;
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '80',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('发货中'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已取消'), cssClass: 'col-active' },
        { value: 5, text: i18n.t('已关闭'), cssClass: 'col-active' },
        { value: 6, text: i18n.t('部分收货'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '120',
    field: 'itemCode',
    headerText: i18n.t('物料编号')
  },
  {
    width: '220',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '80',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '100',
    field: 'demandDate',
    headerText: i18n.t('需求日期')
  },
  {
    width: '100',
    field: 'demandTime',
    headerText: i18n.t('需求时间')
  },
  {
    width: '150',
    field: 'deliveryQuantity',
    headerText: i18n.t('本次送货数量')
  },
  {
    width: '150',
    fieldCode: 'theNumberOfShipments', // 发货数量 前端定义
    headerText: i18n.t('收货数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input-number
          v-model="data.theNumberOfShipments"
          :disabled="ifSteelVmi == 1"
          :min="0"
          :max="data.deliveryQuantity"
          precision="3"
          cssClass="e-outline"
          :show-clear-button="false"
          @change="handleInput"
          @keydown.native="numberInputOnKeyDown"
          ></mt-input-number>`,
          data: function () {
            return {
              data: {},
              numberInputOnKeyDown,
              ifSteelVmi: JSON.parse(localStorage.getItem('deliverDetailSupplier')).ifSteelVmi
            }
          },
          mounted() {},
          methods: {
            handleInput(e) {
              console.log(e)
              if (this.data.deliveryQuantity !== undefined && !isNaN(this.data.deliveryQuantity)) {
                if (e > this.data.deliveryQuantity) {
                  this.$toast({
                    content: this.$t('收货数量不可大于本次送货数量'),
                    type: 'warning'
                  })
                  return
                }
              }
              this.data.theNumberOfShipments = e

              this.$parent.$emit('cellEdit', {
                id: this.data.id,
                index: Number(this.data.index),
                key: 'theNumberOfShipments',
                value: Number(this.data.theNumberOfShipments)
              })
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'outsourcedOrder',
    headerText: i18n.t('匹配到的委外订单号')
  },
  {
    width: '150',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量')
  },
  {
    width: '150',
    fieldCode: 'rejectReason', // 发货数量 前端定义
    headerText: i18n.t('拒绝原因'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input v-model="data.rejectReason" :disabled="data.ifSteelVmi == 1"  @blur="notChange"></mt-input>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {
            notChange(e) {
              if (e !== null) {
                if (this.data.theNumberOfShipments === undefined) {
                  this.$toast({
                    content: i18n.t('请先输入收货数量'),
                    type: 'error'
                  })
                  this.data.rejectReason = undefined
                  return
                }
                if (Number(this.data.deliveryQuantity) === Number(this.data.theNumberOfShipments)) {
                  this.$toast({
                    content: i18n.t('入库数量和收货数量相同无需填备注'),
                    type: 'error'
                  })
                  this.data.rejectReason = undefined
                  return
                }
                if (Number(this.data.deliveryQuantity) > Number(this.data.theNumberOfShipments)) {
                  // 非数字、小于 0、大于 待发货数量
                  this.data.rejectReason = e
                } else {
                  this.data.rejectReason = ''
                }
                this.$parent.$emit('notEdit', {
                  index: Number(this.data.index),
                  key: 'rejectReason',
                  value: this.data.rejectReason
                })
              }
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '150',
    field: 'workCenterName',
    headerText: i18n.t('工作中心')
  },
  {
    width: '150',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '150',
    field: 'sendAddress',
    headerText: i18n.t('送货地址')
  },
  {
    width: '150',
    field: 'deliveryNumber',
    headerText: i18n.t('交货编号')
  },
  {
    width: '100',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    field: 'postingDate',
    headerText: i18n.t('过账日期'),
    template: timeDate('postingDate', false)
  },
  {
    width: '150',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '150',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '150',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '150',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '150',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '150',
    field: 'dispatcherName',
    headerText: i18n.t('调度员')
  },
  {
    width: '110',
    field: 'receiveSupplierCode',
    headerText: i18n.t('收货供应商编码')
  },
  {
    width: '150',
    field: 'receiveSupplierName',
    headerText: i18n.t('收货供应商名称')
  },
  {
    width: '150',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人')
  },
  {
    width: '150',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    template: timeDate('cancelTime', true)
  },
  {
    width: '150',
    field: 'collectorMark',
    headerText: i18n.t('代收标识')
  },
  {
    width: '150',
    field: 'collectorName',
    headerText: i18n.t('代收人')
  },
  {
    field: 'inputDate',
    headerText: i18n.t('凭证创建日期'),
    template: timeDate('inputDate', true)
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    template: timeDate('createTime', true)
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]

export const serializeList = (list) => {
  if (list.length > 0) {
    let serialNumber = 1
    list.forEach((item) => {
      // 添加序号
      item.serialNumber = serialNumber++
    })
  }
  return list
}
