<template>
  <!-- 收货-采方-详情 -->
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      v-if="headerInfo"
      ref="headerTop"
      :header-info="headerInfo"
      class="flex-keep"
      @doSubmit="checkSubmitDialog"
    ></top-info>
    <mt-tabs
      :e-tab="false"
      tab-id="deliver-tab"
      :data-source="pageConfig"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="flex-fit" v-show="currentInfo.title == $t('物料信息')">
      <mt-template-page
        ref="templateRef"
        :template-config="templateConfig"
        :hidden-tabs="true"
        @handleClickToolBar="handleClickToolBar"
        @cellEdit="cellEdit"
        @notEdit="notEdit"
      />
    </div>
    <bottomInfo
      class="flex-fit"
      ref="bottomInfo"
      v-show="currentInfo.title == $t('物流信息')"
    ></bottomInfo>
  </div>
</template>

<script>
import { columnData } from './config/index.js'
// import { BASE_TENANT } from "@/utils/constant";

export default {
  components: {
    topInfo: require('./components/topInfo.vue').default,
    bottomInfo: require('./components/bottominfo.vue').default
  },
  data() {
    const id = this.$route.query.id
    const type = this.$route.query.type

    return {
      id,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      pageConfig: [{ title: this.$t('物料信息') }, { title: this.$t('物流信息') }],
      pageCurrent: 1,
      pageSettings: {
        pageSize: 1000, // 当前每页数据量
        totalPages: 0, // 总页数
        pageSizes: [10, 50, 100, 200]
      },
      userInfo: null,
      headerInfo: null,
      currentInfo: {
        title: this.$t('物料信息')
      },
      type,
      templateConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: [],
          gridId: this.$tableUUID.receiptAndDelivery.receiptListDetail.materialInfo,
          grid: {
            allowPaging: true, // 分页
            lineIndex: 0, // 序号列
            // columnData: formatTableColumnData({
            //   data: columnData,
            // }),
            columnData: columnData,
            dataSource: []
            // asyncConfig: {
            //   url: `${BASE_TENANT}/buyerOrderDelivery/query/item/page`, // 采方送货单-采方查询送货单明细分页
            //   defaultRules: [
            //     {
            //       field: "deliveryId",
            //       operator: "equal",
            //       value: id,
            //     },
            //   ],
            // },
          }
        }
      ],
      editData: [], // 行编辑过的数据
      editData2: [],
      currentTabIndex: 0,
      forecastRules: []
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    typeInit() {
      this.headerInfo = JSON.parse(localStorage.getItem('deliverDetailSupplier'))
      // this.headerInfo.deliveryType === 1 ? this.$t('关联采购订单') : this.$t('无采购订单')
      this.headerInfo.status === 1
        ? this.$t('新建')
        : this.headerInfo.status === 2
        ? this.$t('发货中')
        : this.headerInfo.status === 3
        ? this.$t('已完成')
        : this.headerInfo.status === 4
        ? this.$t('已取消')
        : this.headerInfo.status === 5
        ? this.$t('已关闭')
        : ''
      this.headerInfo.supplier = this.headerInfo.supplierCode + '-' + this.headerInfo.supplierName
      this.headerInfo.site = this.headerInfo.siteCode + '-' + this.headerInfo.siteName
      this.headerInfo.warehouse = this.headerInfo.warehouseCode
        ? this.headerInfo.warehouseCode + '-' + this.headerInfo.warehouseName
        : ''
      this.headerInfo.company = this.headerInfo.companyCode + '-' + this.headerInfo.companyName
      this.headerInfo.receiver = this.headerInfo.receiverCode
        ? this.headerInfo.receiverCode + '-' + this.headerInfo.receiverName
        : ''
      this.headerInfo.thirdTenant = this.headerInfo.thirdTenantCode
        ? this.headerInfo.thirdTenantCode + '-' + this.headerInfo.thirdTenantName
        : ''
      this.headerInfo.vmiWarehouse = this.headerInfo.vmiWarehouseCode
        ? this.headerInfo.vmiWarehouseCode + '-' + this.headerInfo.vmiWarehouseName
        : ''
    },
    // 提交
    doSubmit(selectedData) {
      // 如果没有传入selectedData，则使用当前页面数据
      const dataSource = selectedData || this.templateConfig[0].grid.dataSource
      let obj = {
        request: [],
        proTenantId: ''
      }
      console.log(dataSource)
      dataSource.map((item) => {
        obj.request.push({
          id: item.id,
          receiveQuantity: item.theNumberOfShipments || 0,
          rejectReason: item.rejectReason
        })
        obj.proTenantId = item.proTenantId
      })
      console.log(obj)
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .batchPurConfirm(obj)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.goBack()
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 检查提交弹窗
    checkSubmitDialog() {
      const selectedData = this.templateConfig[0].grid.dataSource
      let params = selectedData.map((item) => {
        return {
          id: item.id,
          receiveQuantity: item.theNumberOfShipments || 0,
          rejectReason: item.rejectReason
        }
      })
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryMatchOutsourcedOrder(params)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code === 200) {
            if (res.data.length !== 0) {
              let data = res.data || []
              // 有匹配到的委外订单，弹框
              this.showKtSubmitDialog(data)
            } else {
              // 没有匹配到的委外订单，直接提交
              this.doSubmit(selectedData)
            }
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
          // 接口调用失败，直接提交
          this.doSubmit(selectedData)
        })
    },
    // 显示提交弹窗
    showKtSubmitDialog(matchedData) {
      this.$dialog({
        modal: () => import('./components/ktSubmitDialog.vue'),
        data: {
          title: this.$t('委外订单提交确认'),
          data: matchedData
        },
        success: (selectedRows) => {
          if (selectedRows.length > 0) {
            this.doSubmit(selectedRows)
          }
        }
      })
    },
    handleClickToolBar(e) {
      if (e.toolbar.title === this.$t('刷新数据')) {
        this.init()
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    // 行编辑
    cellEdit(e) {
      console.log(e)
      console.log(this.templateConfig[0].grid.dataSource)
      // 存行编辑的数据 (防止分页数据丢失)
      if (this.templateConfig[0].grid.dataSource[e.index].id !== undefined) {
        let currentEditIndex
        const editItem = this.editData.find((item, index) => {
          currentEditIndex = index
          return item.id == this.templateConfig[0].grid.dataSource[e.index].id
        })
        if (editItem) {
          // 更新编辑的数据
          editItem[e.key] = e.value
          this.editData.splice(currentEditIndex, 1, editItem)
        } else {
          // 保存编辑的数据
          if (e.value) {
            this.editData.push({
              id: this.templateConfig[0].grid.dataSource[e.index].id, // 行数据id
              [e.key]: e.value, // 编辑的字段和值
              key: e.key, // 编辑的字段
              index: e.index
            })
          }
        }
      }

      // 更新当前页 dataSource
      this.templateConfig[0].grid.dataSource.splice(e.index, 1, {
        ...this.templateConfig[0].grid.dataSource[e.index],
        [e.key]: e.value
      })
    },
    // 将行编辑过的数据设置到表格中
    setEditedData(data) {
      if (this.editData.length > 0) {
        this.editData.forEach((itemEdit) => {
          for (let i = 0; i < data.length; i++) {
            if (data[i].id === itemEdit.id) {
              data[i][itemEdit.key] = itemEdit[itemEdit.key]
              break
            }
          }
        })
      }
      if (this.editData2.length > 0) {
        this.editData2.forEach((itemEdit) => {
          for (let i = 0; i < data.length; i++) {
            if (data[i].id === itemEdit.id) {
              data[i][itemEdit.key] = itemEdit[itemEdit.key]
              break
            }
          }
        })
      }

      return data
    },
    notEdit(e) {
      if (this.templateConfig[0].grid.dataSource[e.index].id !== undefined) {
        let currentEditIndex
        const editItem = this.editData2.find((item, index) => {
          currentEditIndex = index
          return item.id == this.templateConfig[0].grid.dataSource[e.index].id
        })
        if (editItem) {
          // 更新编辑的数据
          editItem[e.key] = e.value
          this.editData2.splice(currentEditIndex, 1, editItem)
        } else {
          // 保存编辑的数据
          if (e.value) {
            this.editData2.push({
              id: this.templateConfig[0].grid.dataSource[e.index].id, // 行数据id
              [e.key]: e.value, // 编辑的字段和值
              key: e.key, // 编辑的字段
              index: e.index
            })
          }
        }
      }
      if (this.templateConfig[0].grid.dataSource[e.index].rejectReason !== e.value) {
        this.templateConfig[0].grid.dataSource.splice(e.index, 1, {
          ...this.templateConfig[0].grid.dataSource[e.index],
          [e.key]: e.value
        })
      }
    },
    // 初始化
    init() {
      this.typeInit()

      let obj = {
        page: {
          size: this.pageSettings.pageSize,
          current: this.pageCurrent
        },
        condition: 'and',
        defaultRules: [
          {
            field: 'deliveryId',
            operator: 'equal',
            value: this.id
          }
        ]
      }
      this.$store.commit('startLoading')

      this.$API.receiptAndDelivery
        .postBuyerOrderDeliveryQuery(obj)
        .then((res) => {
          if (res.code === 200) {
            if (this.editData.length > 0 || this.editData2.length > 0) {
              const records = this.setEditedData(res?.data?.records || [])
              this.templateConfig[0].grid.dataSource = this.serializeList(records)
              this.$store.commit('endLoading')
            } else {
              this.templateConfig[0].grid.dataSource = this.serializeList(res?.data?.records || [])
              this.$store.commit('endLoading')
            }
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    // 序列化表格数据
    serializeList(list) {
      if (list.length > 0) {
        list.forEach((item) => {
          // 收货数量(前端定义) 默认等于 发货数量
          item.theNumberOfShipments = item.deliveryQuantity || 0
        })
      }
      return list
    },
    // 分页 页面
    handleCurrentChange(currentPage) {
      this.pageCurrent = currentPage

      this.init()
    },
    // 分页 页数据量
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize

      this.init()
    },
    handleSelectTab(index, item) {
      this.currentInfo = item
    },
    // 消失
    beforeDestroy() {
      sessionStorage.removeItem('deliverDetailSupplier')
    }
  }
}
</script>

<style lang="scss" scoped>
.repeat-template {
  .common-template-page {
    /deep/ .mt-tabs {
      display: none;
    }
  }
}
.e-content {
  height: 200px;
}

.mt-tabs {
  width: 100%;
  /deep/.mt-tabs-container {
    width: 100%;
  }
}
</style>
