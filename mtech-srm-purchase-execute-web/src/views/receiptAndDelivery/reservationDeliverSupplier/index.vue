<template>
  <!-- 预约送货-供方 -->
  <div class="full-height pt20">
    <div class="top-info" v-if="isShowGoBackDeliverListSupplier">
      <div class="header-box">
        <div class="middle-blank"></div>
        <!-- 右侧操作按钮 -->
        <mt-button css-class="e-flat" :is-primary="true" @click="goBackDeliverListSupplier">{{
          $t('返回')
        }}</mt-button>
      </div>
    </div>
    <!-- 列模板 -->
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="false"
      :template-config="templateConfig"
      class="frozenFistColumns"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @dataBound="handleDataBound"
      @handleSearch="handleSearch"
      @handleReset="handleReset"
    />
    <!-- 预约送货-供方-勾选关联ASN -->
    <asn-table-dialog ref="asnTableDialog" @confirm="asnTableDialogConfirm"></asn-table-dialog>
    <!-- 预约送货-供方-勾选关联ASN -->
    <asn-warehouse-table-dialog
      ref="AsnWarehouseTableDialog"
      @confirm="asnTableDialogConfirm"
    ></asn-warehouse-table-dialog>
    <!-- 预约送货-供方-选择送货司机 -->
    <driver-info-table-dialog
      ref="driverInfoTableDialog"
      @confirm="driverInfoTableDialogConfirm"
    ></driver-info-table-dialog>
    <!-- 预约送货-供方-选择预约时间 -->
    <forecast-time-table-dialog
      ref="forecastTimeTableDialog"
      @confirm="forecastTimeTableDialogConfirm"
    ></forecast-time-table-dialog>
    <!-- 预约送货-供方-编辑随车人信息 -->
    <accompany-list-dialog
      ref="accompanyListDialog"
      @confirm="accompanyListDialogConfirm"
    ></accompany-list-dialog>
  </div>
</template>

<script>
import { formatTableColumnData, serializeList } from './config/index'
import {
  EditSettings,
  ReservationColumnData,
  ReservationToolbar,
  RequestType,
  ActionType,
  NewRowData,
  DeliveryStatusText,
  IntoStatus,
  Status
} from './config/constant'
import { rowDataTemp } from './config/variable'
import { cloneDeep } from 'lodash'
import { BASE_TENANT, RegExpMap } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
import Vue from 'vue'

export default {
  components: {
    AsnTableDialog: () => import('./components/asnTableDialog'),
    AsnWarehouseTableDialog: () => import('./components/asnWarehouseTableDialog'),
    DriverInfoTableDialog: () => import('./components/driverInfoTableDialog'),
    ForecastTimeTableDialog: () => import('./components/forecastTimeTableDialog'),
    AccompanyListDialog: () => import('./components/accompanyListDialog')
  },
  data() {
    const lastTabIndex = JSON.parse(localStorage.getItem('lastTabIndex'))
    const pageType = this.$route.query.type

    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      isShowGoBackDeliverListSupplier: false, // 是否显示 返回送货单列表 按钮
      pageType, // 页面类型
      lastTabIndex, // 前一页面的 Tab index
      isReservationUpgradeRowIndex: null, // 存点击了升级预约的行index
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 不使用组件中的toolbar配置
          toolbar: ReservationToolbar,
          buttonQuantity: 7,
          gridId: this.$tableUUID.receiptAndDelivery.reservationDeliverSupplier.list,
          grid: {
            editSettings: EditSettings,
            allowPaging: true, // 分页
            columnData: formatTableColumnData({
              data: ReservationColumnData
            }),
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/supplierForecastDelivery/query`,
              serializeList: serializeList
            }
            // frozenColumns: 1, 使用 class="frozenFistColumns" 实现
          }
        }
      ],
      isEditing: false, // 正在编辑数据
      forecasetType: 0
    }
  },
  mounted() {},
  beforeDestroy() {
    localStorage.removeItem('lastTabIndex')
  },
  methods: {
    // toolbar 按钮点击
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectedRecords = gridRef.getMtechGridRecords()
      const commonToolbar = [
        'ReservationAdd',
        'ReservationUpdate',
        'Filter',
        'Refresh',
        'refreshDataByLocal',
        'filterDataByLocal',
        'resetDataByLocal',
        'Setting'
      ]

      if (this.isEditing && toolbar.id !== 'refreshDataByLocal') {
        // 结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }
      if (toolbar.id === 'export') {
        this.handleExport()
        return
      }

      if (selectedRecords.length == 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (toolbar.id === 'copy') {
        this.handleCopy(selectedRecords)
      }

      if (toolbar.id === 'print') {
        if (selectedRecords.some((v) => [4, 5].includes(v.onWayStatus))) {
          this.$toast({
            content: this.$t('不可以打印已取消和已关闭的单据'),
            type: 'warning'
          })
          return
        }
        this.handlePrint(selectedRecords)
      }

      if (toolbar.id === 'ReservationAdd') {
        // 新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'ReservationSubmit') {
        // 提交
        this.handleReservationSubmit({ selectedRecords, idList })
      } else if (toolbar.id === 'ReservationCancel') {
        // 取消
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消选中的数据？')
          },
          success: () => {
            this.handleReservationCancel({ selectedRecords, idList })
          }
        })
      } else if (toolbar.id === 'ReservationUpdate') {
        // 更新-结束行编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'synchronization') {
        this.hSynchronization(idList)
      }
    },
    handleCopy(selectRows) {
      const deliveryCodeList = selectRows.map((i) => {
        return i.deliveryCode
      })
      const str = [...new Set(deliveryCodeList)].join(' ')
      this.copyToClipboard(str)
      this.$router.push({
        name: `deliver-list-supplier-kt`,
        query: {
          timeStamp: new Date().getTime()
        }
      })
    },
    copyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    },
    handlePrint(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      const api =
        this.$route.name === 'reservation-deliver-supplier-kt'
          ? this.$API.receiptAndDelivery.ktPrintSupplierForecastDeliveryApi
          : this.$API.receiptAndDelivery.bdPrintSupplierForecastDeliveryApi
      api(ids).then((res) => {
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(
          new Blob([content], { type: 'text/html;charset=utf-8' })
        )
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    handleExport() {
      const asyncParams = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
      let obj = JSON.parse(
        sessionStorage.getItem(this.$tableUUID.receiptAndDelivery.reservationDeliverSupplier.list)
      )?.visibleCols
      let includeColumnFiledNames = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            includeColumnFiledNames.push(item.field)
          }
        })
      } else {
        ReservationColumnData.forEach((item) => {
          if (item.code) {
            includeColumnFiledNames.push(item.code)
          }
        })
      }
      let params = { ...asyncParams, sortedColumnStr: includeColumnFiledNames.join(',') }
      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery.exportSupplierForecastDeliveryApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 同步
    hSynchronization(arr) {
      if (arr.length > 1) {
        this.$toast({
          content: this.$t('只允许对一条数据进行操作'),
          type: 'warning'
        })
        return
      }
      const id = {
        id: arr.toString()
      }
      this.$API.receiptAndDelivery.purforecastSync(id).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('同步成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('同步失败'), type: 'warning' })
        }
      })
    },
    // CellTool
    handleClickCellTool(args) {
      const { tool, data } = args

      if (this.isEditing) {
        // 正在编辑时，若不是点击刷新按钮，结束编辑状态
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
        return
      }

      if (tool.id === 'ReservationCancel') {
        // 取消
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认取消选中的数据？')
          },
          success: () => {
            this.handleReservationCancel({
              selectedRecords: [data],
              idList: [data.id]
            })
          }
        })
      } else if (tool.id === 'ReservationSubmit') {
        // 提交
        this.handleReservationSubmit({
          selectedRecords: data,
          idList: [data.id]
        })
      } else if (tool.id === 'ReservationUpgrade') {
        // 升级预约
        this.isReservationUpgradeRowIndex = Number(data.index)
        // 指定行进入编辑状态templateReftemplateRef
        this.$refs.templateRef
          .getCurrentUsefulRef()
          .gridRef.ejsRef.selectRow(this.isReservationUpgradeRowIndex)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
      }
    },
    // 处理从送货单跳转过来的情况
    dealWithJumpFromDeliveryList() {
      if (localStorage.getItem('toReservationDeliverData')) {
        // 送货单列表 跳转过来的数据
        const toReservationDeliverData = JSON.parse(
          localStorage.getItem('toReservationDeliverData')
        )
        localStorage.removeItem('toReservationDeliverData')
        const deliveryCodeList = toReservationDeliverData?.deliveryCodeList || []
        const deliveryStatusList = toReservationDeliverData?.deliveryStatusList || []
        const selectedRowData = toReservationDeliverData?.selectedRowData || []
        const deliveryStatus = []
        const purTenantId = toReservationDeliverData?.purTenantId // 采方租户id
        if (this.pageType === 'new' && deliveryCodeList && deliveryCodeList.length > 0) {
          // 显示返回 送货单列表 的按钮
          this.isShowGoBackDeliverListSupplier = true
          deliveryStatusList.forEach((item) => {
            deliveryStatus.push({
              value: item,
              text: DeliveryStatusText[item] || item
            })
          })
          setTimeout(() => {
            // 新增
            this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
            this.$bus.$emit('reservationDeliverSupplierRowSearchDialogConfirm', {
              requestKey: 'deliveryCode', // 关联ASN
              data: {
                deliveryCode: [...new Set(deliveryCodeList)], // 去重
                deliveryStatus: deliveryStatus,
                companyName: selectedRowData[0].companyName, // 公司名称 // 产品说取第一个
                companyCode: selectedRowData[0].companyCode, // 公司编码 // 产品说取第一个
                supplierName: selectedRowData[0].supplierName, // 供应商名称 // 产品说取第一个
                supplierCode: selectedRowData[0].supplierCode, // 供应商编码 // 产品说取第一个
                buyerOrgName: selectedRowData[0].buyerOrgName, // 采购组名称 // 产品说取第一个
                buyerOrgCode: selectedRowData[0].buyerOrgCode, // 采购组编码 // 产品说取第一个
                purTenantId // 采方租户id
              },
              modifiedKeys: [
                'deliveryCode', // 关联ASN
                'deliveryStatus', // 交货方式
                'companyName', // 公司名称
                'companyCode', // 公司编码
                'supplierName', // 供应商名称
                'supplierCode', // 供应商编码
                'buyerOrgName', // 采购组名称
                'buyerOrgCode' // 采购组编码
              ]
            })
          }, 500)
        }
      }
    },
    // 提交预约
    handleReservationSubmit(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.status != Status.notYet && item.status != Status.reject) {
          isValid = false
          this.$toast({
            content: this.$t('请选择未预约或预约拒绝的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // 未预约 || 预约拒绝
        this.postSupplierForecastDeliverySubmit(idList)
      }
    },
    // 取消预约
    handleReservationCancel(args) {
      const { selectedRecords, idList } = args
      let isValid = true
      for (let i = 0; i < selectedRecords.length; i++) {
        const item = selectedRecords[i]
        if (item.intoStatus == IntoStatus.already) {
          isValid = false
          this.$toast({
            content: this.$t('请选择未入园的数据'),
            type: 'warning'
          })
          break
        } else if (item.status == Status.cancel) {
          isValid = false
          this.$toast({
            content: this.$t('请选择未取消的数据'),
            type: 'warning'
          })
          break
        }
      }
      if (isValid) {
        // (未预约 || 审批中 || 预约拒绝 || 预约成功) && 未入园
        this.postSupplierForecastDeliveryCancel({
          idList,
          length: idList.length
        })
      }
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData, rowIndex } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 判断该行数据是否为预约成功的数据
        if (rowData.status == Status.success) {
          // 预约成功的数据，校验是否点击了行的升级预约按钮
          if (this.isReservationUpgradeRowIndex !== rowIndex) {
            args.cancel = true
            return
          }
        } else if (
          rowData.status == Status.cancel ||
          rowData.status == Status.inProgress ||
          rowData.status == Status.reservate
        ) {
          // 数据不可编辑 状态 = 取消 || 审批中
          args.cancel = true
          return
        }
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          this.postSupplierForecastDeliverySave({ rowData, rowIndex })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        // rowData, index
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          this.postSupplierForecastDeliverySave({ rowData, rowIndex: index })
        }
      } else if (requestType === RequestType.sorting || requestType === RequestType.refresh) {
        // 丢弃编辑中状态
        this.isEditing = false
      }
    },
    // 校验数据
    isValidSaveData(data) {
      const {
        forecastType, // 预约类型
        deliveryCode, // 关联ASN
        companyCode, // 公司
        deliveryStatus, // 交货方式
        driverName, // 司机姓名
        driverPhone, // 司机手机号
        driverIdNo, // 司机身份证号
        carNo, // 车牌号
        quantity, // 件数
        subSiteCode, // 分厂
        gateNumber, // 门岗号
        forecastTime // 预约时间
      } = data
      const { idCardReg, phoneNumReg, numberPlateReg } = RegExpMap
      let valid = true
      if ([0, 1].includes(forecastType) && (!deliveryCode || deliveryCode.length === 0)) {
        // 预约类型为送货单预约或入库单预约时，校验关联ASN
        this.$toast({ content: this.$t('关联ASN不可为空'), type: 'warning' })
        valid = false
      } else if (!companyCode) {
        this.$toast({
          content: this.$t('请选择公司'),
          type: 'warning'
        })
        valid = false
      } else if (!deliveryStatus) {
        this.$toast({
          content: this.$t('请选择交货方式'),
          type: 'warning'
        })
        valid = false
      } else if (!driverName) {
        this.$toast({
          content: this.$t('请输入司机姓名'),
          type: 'warning'
        })
        valid = false
      } else if (!driverPhone) {
        this.$toast({
          content: this.$t('请输入司机手机号'),
          type: 'warning'
        })
        valid = false
      } else if (!phoneNumReg.test(driverPhone)) {
        this.$toast({
          content: this.$t('司机手机号格式错误'),
          type: 'warning'
        })
        valid = false
      } else if (!driverIdNo) {
        this.$toast({
          content: this.$t('请输入司机身份证号'),
          type: 'warning'
        })
        valid = false
      } else if (!idCardReg.test(driverIdNo)) {
        this.$toast({
          content: this.$t('司机身份证号格式错误'),
          type: 'warning'
        })
        valid = false
      } else if (!carNo) {
        this.$toast({
          content: this.$t('请输入车牌号'),
          type: 'warning'
        })
        valid = false
      } else if (carNo && !numberPlateReg.test(carNo)) {
        this.$toast({
          content: this.$t('请输入正确的车牌号'),
          type: 'warning'
        })
        valid = false
      } else if (quantity <= 0) {
        // 件数
        this.$toast({ content: this.$t('件数应大于0'), type: 'warning' })
        valid = false
      } else if ([2].includes(forecastType)) {
        if (!subSiteCode) {
          this.$toast({
            content: this.$t('请选择分厂'),
            type: 'warning'
          })
          valid = false
        } else if (!gateNumber) {
          this.$toast({
            content: this.$t('请选择门岗号'),
            type: 'warning'
          })
          valid = false
        }
      } else if (!forecastTime) {
        // 预约时间
        this.$toast({ content: this.$t('预约时间不可为空'), type: 'warning' })
        valid = false
      }

      return valid
    },
    // 表格数据绑定完成
    handleDataBound() {
      // 处理从送货单跳转过来的情况
      this.dealWithJumpFromDeliveryList()
    },
    // 预约送货-供方-取消预约送货
    postSupplierForecastDeliveryCancel(args) {
      const { idList } = args
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierForecastDeliveryCancel(idList)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res.data || [] // ["B 预约单成功","C 预约单失败：XXXX"] => "<div>B 预约单成功</div><div>C 预约单失败：XXXX</div>"
            this.$toast({
              content: data
                .map((item) => {
                  return `<div>${item}</div>`
                })
                .join(''),
              type: 'success'
            })
            // 刷新 预约送货-供方-获取预约送货列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 预约送货-供方-预约送货保存
    postSupplierForecastDeliverySave(args) {
      const { rowData, rowIndex } = args
      // 交货方式
      const deliveryStatusList = (rowData?.deliveryStatus || []).map((item) => {
        return item.value
      })
      const params = {
        ...rowData,
        thePrimaryKey: undefined,
        // 关联ASN
        deliveryCode: (rowData.deliveryCode || []).join(','),
        // 交货方式
        deliveryStatus: deliveryStatusList.join(',')
      }
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierForecastDeliverySave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 预约送货-供方-获取预约送货列表
            this.$refs.templateRef.refreshCurrentGridData()
            // 完成编辑保存，置空升级预约行index
            this.isReservationUpgradeRowIndex = null
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 预约送货-供方-提交预约送货
    postSupplierForecastDeliverySubmit(params) {
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierForecastDeliverySubmit(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            // 操作成功
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新 预约送货-供方-获取预约送货列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 行编辑 点击搜索按钮
    handleSearch(args) {
      const { data, dataKey } = args
      if (dataKey === 'deliveryCode') {
        // 送货单号 关联ASN弹框
        if (![0, 1, 2].includes(data.forecastType)) {
          this.$toast({
            content: this.$t('请先选择预约类型'),
            type: 'warning'
          })
          return
        }
        if (data.forecastType === 1) {
          this.$refs.AsnWarehouseTableDialog.dialogInit({
            title: this.$t('关联ASN/VMI入库单'),
            data
          })
        } else {
          this.$refs.asnTableDialog.dialogInit({
            title: this.$t('关联ASN/VMI入库单'),
            data
          })
        }
      } else if (dataKey === 'driverName') {
        // 司机名称 选择送货司机弹框
        this.$refs.driverInfoTableDialog.dialogInit({
          title: this.$t('选择送货司机'),
          data
        })
      } else if (dataKey === 'forecastTime') {
        // 预约时间 选择预约时间弹框
        if ([0, 1].includes(data.forecastType)) {
          if (data?.deliveryCode?.length > 0) {
            this.$refs.forecastTimeTableDialog.dialogInit({
              title: this.$t('预约时间'),
              data
            })
          } else {
            this.$toast({ content: this.$t('请先关联 ASN'), type: 'warning' })
          }
        } else {
          this.$refs.forecastTimeTableDialog.dialogInit({
            title: this.$t('预约时间'),
            data
          })
        }
      } else if (dataKey === 'accompanyInfo') {
        // 随车人姓名 编辑随车人信息弹框
        this.$refs.accompanyListDialog.dialogInit({
          title: this.$t('随车人员信息'),
          data: data.accompanyList // 随车人员信息
        })
      }
    },
    handleReset(v) {
      this.$bus.$emit('reservationDeliverSupplierRowSearchDialogConfirm', {
        requestKey: 'deliveryCode', // 关联ASN
        data: {
          deliveryCode: [], // 去重
          deliveryStatus: '',
          companyName: '', // 公司名称 // 产品说取第一个
          companyCode: '', // 公司编码 // 产品说取第一个
          supplierName: '', // 供应商名称 // 产品说取第一个
          supplierCode: '', // 供应商编码 // 产品说取第一个
          buyerOrgName: '', // 采购组名称 // 产品说取第一个
          buyerOrgCode: '' // 采购组编码 // 产品说取第一个
        },
        modifiedKeys: [
          'deliveryCode', // 关联ASN
          'deliveryStatus', // 交货方式
          'companyName', // 公司名称
          'companyCode', // 公司编码
          'supplierName', // 供应商名称
          'supplierCode', // 供应商编码
          'buyerOrgName', // 采购组名称
          'buyerOrgCode' // 采购组编码
        ]
      })
      this.forecasetType = v
    },
    // 关联ASN弹框点击确认
    asnTableDialogConfirm(args) {
      const { data } = args
      if (data?.length > 0) {
        const deliveryCodeTmp = []
        let deliveryStatusTmp = []
        const purTenantId = data[0].purTenantId // 采方租户id
        data.forEach((item) => {
          // 送货单号
          deliveryCodeTmp.push(item.deliveryCode)
          // 交货方式
          deliveryStatusTmp.push(item.deliveryType)
        })

        // 交货方式 去空、去重
        deliveryStatusTmp = [...new Set(deliveryStatusTmp.filter((item) => item))].map((item) => {
          return {
            value: item,
            text: DeliveryStatusText[item] || item
          }
        })
        this.$bus.$emit('reservationDeliverSupplierRowSearchDialogConfirm', {
          requestKey: 'deliveryCode', // 关联ASN
          data: {
            deliveryCode: [...new Set(deliveryCodeTmp)], // 去重
            deliveryStatus:
              this.forecasetType === 0 ? deliveryStatusTmp : [{ text: 'VMI', value: 5 }],
            companyName: data[0].companyName, // 公司名称 // 产品说取第一个
            companyCode: data[0].companyCode, // 公司编码 // 产品说取第一个
            supplierName: data[0].supplierName, // 供应商名称 // 产品说取第一个
            supplierCode: data[0].supplierCode, // 供应商编码 // 产品说取第一个
            buyerOrgName: data[0].buyerOrgName, // 采购组名称 // 产品说取第一个
            buyerOrgCode: data[0].buyerOrgCode, // 采购组编码 // 产品说取第一个
            purTenantId // 采方租户id
          },
          modifiedKeys: [
            'deliveryCode', // 关联ASN
            'deliveryStatus', // 交货方式
            'companyName', // 公司名称
            'companyCode', // 公司编码
            'supplierName', // 供应商名称
            'supplierCode', // 供应商编码
            'buyerOrgName', // 采购组名称
            'buyerOrgCode' // 采购组编码
          ]
        })
      }
    },
    // 选择送货司机弹框点击确认
    driverInfoTableDialogConfirm(args) {
      const { data } = args
      if (data?.length > 0) {
        this.$bus.$emit('reservationDeliverSupplierRowSearchDialogConfirm', {
          requestKey: 'driverName', // 司机姓名
          data: {
            driverName: data[0].name, // 司机姓名
            driverIdNo: data[0].idCard, // 司机身份证号
            driverPhone: data[0].contact, // 司机手机号
            carNo: data[0].license // 车牌号
          },
          modifiedKeys: [
            'driverName', // 司机姓名
            'driverIdNo', // 司机身份证号
            'driverPhone', // 司机手机号
            'carNo' // 车牌号
          ]
        })
      }
    },
    // 选择预约时间弹框点击确认
    forecastTimeTableDialogConfirm(args) {
      const { data } = args
      if (data.forecastTime && data.carAbility) {
        this.$bus.$emit('reservationDeliverSupplierRowSearchDialogConfirm', {
          requestKey: 'forecastTime', // 预约时间
          data: {
            id: data.id,
            forecastTime: data.forecastTime, // 预约时间段
            carAbility: data.carAbility, // 装车能力
            warehouseCode: data.warehouseCode,
            warehouse: data.warehouse,
            sendAddressCode: data.sendAddressCode,
            sendAddress: data.sendAddress
          },
          modifiedKeys: [
            'id',
            'forecastTime', // 预约时间段
            'carAbility', // 装车能力
            'warehouseCode',
            'warehouse',
            'sendAddressCode',
            'sendAddress'
          ]
        })
      }
    },
    // 编辑随车人信息弹框点击确认
    accompanyListDialogConfirm(args) {
      const { data } = args
      this.$bus.$emit('reservationDeliverSupplierRowSearchDialogConfirm', {
        requestKey: 'accompanyInfo', // 随车人姓名
        data: { accompanyList: data },
        modifiedKeys: [
          'accompanyList' // 随车人信息
        ]
      })
    },
    goBackDeliverListSupplier() {
      localStorage.setItem('tabIndex', JSON.stringify(this.lastTabIndex))
      // 返回 入库管理列表
      if (this.$route.params?.sourceType == 1) {
        this.$router.push({
          name: 'supplier-warehousing-index'
        })
        return
      }
      // 返回 送货单列表
      this.$router.push({
        name: 'deliver-list-supplier'
      })
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 行内 cellTool
/deep/ .column-tool {
  margin-top: 8px;

  .template-svg {
    cursor: pointer;
    font-size: 12px;
    color: #6386c1;

    &:nth-child(n + 2) {
      margin-left: 10px;
    }
  }
}

// 行编辑时包含按钮的项目
/deep/ .input-search-content {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .mutliselect-container {
    width: 100%;
  }
  // 超过宽度显示省略号
  .text-ellipsis {
    width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .mt-icons {
    width: 20px;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
  }
}

/deep/ .grid-edit-column {
  padding: 12px 0;
}

.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  // padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .middle-blank {
      flex: 1;
    }
  }
}
</style>
