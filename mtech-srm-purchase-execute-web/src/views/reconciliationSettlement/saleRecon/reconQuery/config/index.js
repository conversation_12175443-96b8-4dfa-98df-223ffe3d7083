import { i18n } from '@/main.js'
import { timeNumberToDate, judgeFormatCodeName } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
// 销售对账单列表
export const sumCols = [
  {
    width: '150',
    field: 'code',
    headerText: i18n.t('对账单号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    },
    cellTools: [
      {
        id: 'view',
        icon: 'icon_outline_Preview',
        title: i18n.t('查看')
      }
    ]
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: -1, text: i18n.t('关闭'), cssClass: 'col-inactive' },
        { value: 0, text: i18n.t('待发布'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('发布待反馈'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('反馈异常'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已确认'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '150',
    field: 'syncStatus',
    headerText: i18n.t('创建同步状态'), //  0-未同步；1-部分成功；2-同步成功；2-同步失败
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('部分成功'), cssClass: 'col-inactive' },
        { value: 2, text: i18n.t('同步成功'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'writeOffStatus',
    headerText: i18n.t('关闭同步状态'), // 冲销状态 0-未同步；1-部分成功；2-同步成功；2-同步失败
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('部分成功'), cssClass: 'col-inactive' },
        { value: 2, text: i18n.t('同步成功'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    width: '150',
    field: 'saleGroupName',
    headerText: i18n.t('销售组织')
  },
  {
    width: '150',
    field: 'saleGroupCode',
    headerText: i18n.t('销售组织编码')
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '150',
    field: 'customerCode',
    headerText: i18n.t('客户编码')
  },
  {
    width: '150',
    field: 'untaxedTotalPrice',
    headerText: i18n.t('执行未税总金额')
  },
  {
    width: '150',
    field: 'taxedTotalPrice',
    headerText: i18n.t('执行含税总金额')
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('税额')
  },
  {
    width: '250',
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'feedbackTime',
    headerText: i18n.t('供应商确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'purRemark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '150',
    field: 'supRemark',
    headerText: i18n.t('供方备注')
  },
  {
    width: '150',
    field: 'confirmTime',
    headerText: i18n.t('财务审核时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    field: 'operation',
    headerText: i18n.t('操作'),
    valueConverter: {
      type: 'function',
      filter: () => {
        return `查看日志`
      }
    },
    cellTools: []
  }
]

export const detailCols = [
  {
    width: '150',
    field: 'code',
    headerText: i18n.t('对账单号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    }
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: -1, text: i18n.t('关闭'), cssClass: 'col-inactive' },
        { value: 0, text: i18n.t('待发布'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('发布待反馈'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-active' },
        { value: 3, text: i18n.t('反馈异常'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已确认'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '150',
    field: 'syncStatus',
    headerText: i18n.t('创建同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('同步成功'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'writeOffStatus',
    headerText: i18n.t('关闭同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: 'col-inactive' },
        { value: 1, text: i18n.t('同步成功'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('同步失败'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'invoiceNo',
    headerText: i18n.t('发票号'),
    valueConverter: {
      type: 'placeholder',
      placeholder: '--' //placeholder可不传，默认值为"未填"
    }
  },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  {
    width: '150',
    field: 'saleGroupName',
    headerText: i18n.t('销售组织')
  },
  {
    width: '150',
    field: 'saleGroupCode',
    headerText: i18n.t('销售组织编码')
  },
  {
    width: '150',
    field: 'channelName',
    headerText: i18n.t('分销渠道')
  },
  // {
  //   width: "150",
  //   field: "channelCode",
  //   headerText: i18n.t("分销渠道编码"),
  // },
  {
    width: '150',
    field: 'typeName',
    headerText: i18n.t('条件类型')
  },
  // {
  //   width: "150",
  //   field: "typeCode",
  //   headerText: i18n.t("条件类型编码"),
  // },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    width: '150',
    field: 'untaxedTotalPrice',
    headerText: i18n.t('执行未税总金额')
  },
  {
    width: '150',
    field: 'taxedTotalPrice',
    headerText: i18n.t('执行含税总金额')
  },
  {
    width: '150',
    field: 'taxPrice',
    headerText: i18n.t('税额')
  },
  {
    width: '250',
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.money,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'purRemark',
    headerText: i18n.t('采方备注')
  },
  {
    width: '150',
    field: 'supRemark',
    headerText: i18n.t('供方备注')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('行号'),
    ignore: true
  },
  {
    width: '150',
    field: 'receiveCode',
    headerText: i18n.t('交货单号')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编号'),
    searchOptions: MasterDataSelect.material
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称'),
    ignore: true
  },
  {
    width: '250',
    field: 'unitCode',
    headerText: i18n.t('单位'),
    searchOptions: MasterDataSelect.unit,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.unitCode, data?.unitName)
    }
  },
  {
    width: '150',
    field: 'quantity',
    headerText: i18n.t('数量')
  },
  {
    width: '150',
    field: 'receiveDate',
    headerText: i18n.t('事务日期'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return '-'
        }
      }
    },
    searchOptions: MasterDataSelect.dateRange
  },
  {
    width: '150',
    field: 'untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    width: '150',
    field: 'untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },
  {
    width: '150',
    field: 'taxRate',
    headerText: i18n.t('税率')
  },
  {
    width: '150',
    field: 'taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    width: '150',
    field: 'taxedTotalPrice',
    headerText: i18n.t('含税总价')
  }
]
// 操作日志 表格列数据
export const LogColumnData = [
  {
    field: 'operTypeName',
    headerText: i18n.t('操作类型'),
    width: '150'
  },
  {
    field: 'operDescription',
    headerText: i18n.t('操作内容'), // 操作描述 操作内容
    width: '150'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('操作人'),
    width: '150'
  },
  {
    field: 'createTime',
    headerText: i18n.t('操作时间'),
    width: '150'
  }
]
