import { API } from '@mtech-common/http'
import { PROXY_PRICEING, PROXY_SOURCING } from 'CONFIG/proxy.config'
const NAME = 'priceService'
const APIS = {
  /*
    价格接口
  */
  // 价格服务 列表
  getPriceRecords: `${PROXY_PRICEING}/tenant/pricerecord/query/item?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  getPriceRecordsNew: `${PROXY_PRICEING}/tenant/pricerecord/query/item/new?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  getPriceRecordsNewSeires: `${PROXY_PRICEING}/tenant/pricerecord/query/item/new/series?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  getPriceRecordsTable: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/pricerecord/query/item?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data),
  // 分页查询价格明细行
  getPriceRecordItems: `${PROXY_PRICEING}/tenant/pricerecord/item/query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  // 成本因子 列表
  getCostRecords: `${PROXY_PRICEING}/tenant/pricerecord/query/marketfactor?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  //分页查询物流记录
  getLogicRecords: `${PROXY_PRICEING}/tenant/pricerecord/tcl/query/logistics?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  // 查询 价格历史 折线图
  getLineRecords: (data = {}) =>
    API.post(`${PROXY_PRICEING}/tenant/pricerecord/trend/chart/query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data),
  //单个新增
  singleSavePrice: (data = {}) => API.post(`${PROXY_PRICEING}/tenant/pricerecord/`, data),
  //批量新增
  batchSavePrice: (data = {}) => API.post(`${PROXY_PRICEING}/tenant/pricerecord/add/batch`, data),
  //批量禁用
  batchDisabledPrice: (data = {}) =>
    API.put(`${PROXY_PRICEING}/tenant/pricerecord/disable/batch`, data),
  //批量启动
  batchEnablePrice: (data = {}) =>
    API.put(`${PROXY_PRICEING}/tenant/pricerecord/enable/batch`, data),
  // 导入Excel
  importExcel: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/pricerecord/excel/import`, data)
  },
  // 新增成本因子
  addCBPrice: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/pricerecord/`, data)
  },
  // 同步价格SPA
  importSPA: (data) => {
    return API.post(
      `${PROXY_PRICEING}/tenant/pricerecord/tcl/sync?BU_CODE=${localStorage.getItem('currentBu')}`,
      data
    )
  },
  //获取直送地
  getDeliveryPlace: (data) => {
    return API.get('/masterDataManagement/tenant/dict-item/getByDictCode/DELIVERY_PLACE', data)
  },
  //获取组织机构
  getOrgListByCid: (data) => {
    return API.get(
      `/masterDataManagement/tenant/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    )
  },

  //导出物料
  exportPrice: (data) => {
    return API.post('/sourcing/tenant/series/item//export/detail/price', data, {
      responseType: 'blob'
    })
  },

  //获取采购组织
  getByOrgIdAndBgOrgTypeCode: (data) => {
    return API.get(
      `/masterDataManagement/tenant/business-organization/getByOrgIdAndBgOrgTypeCode?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data
    )
  },
  //推送SAP
  syncSap: (data) => {
    return API.post('/price/price/trace/syncSap', data)
  },
  //导出
  traceExport: (data) => {
    return API.post('/price/price/trace/traceExport', data, {
      responseType: 'blob'
    })
  },
  /*
  /*
    sku价格接口
  */
  // sku价格服务 列表
  getHistoryRecords: `${PROXY_PRICEING}/tenant/pricerecord/history/query?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,
  // getSkuPriceRecords: `${PROXY_PRICEING}/tenant/price/sku/query/page`,
  getSkuPriceRecords: `${PROXY_PRICEING}/tenant/purchasemall/price/query/page`,
  //获取来源单号
  getReferList: `/sourcing/tenant/point2/price/refer/list`,
  // 新的获取来源单号
  getReferListNew: `/sourcing/tenant/point2/priceRecord/refer/list`,
  // 新的获取来源单号
  getReferListNewTv: `/sourcing/tenant/point2/priceRecord/refer/list?BU_CODE=TV`,

  //查询新品估价详情
  queryEvaluateDetail: (data) => {
    return API.get('/sourcing/tenant/evaluate/queryDetail', data)
  },
  //删除新品估价
  delEvaluate: (data) => {
    return API.post('/sourcing/tenant/evaluate/delEvaluate', data)
  },
  //新增新品估价
  addEvaluate: (data) => {
    return API.post('/sourcing/tenant/evaluate/addEvaluate', data)
  },
  //更新新品估价
  updateEvaluate: (data) => {
    return API.post('/sourcing/tenant/evaluate/updateEvaluate', data)
  },
  //导入估价明细
  uploadEvaluateDetail: (params = {}) => {
    return API.post(
      `/sourcing/tenant/evaluate/uploadEvaluateDetail?rfxId=${params.rfxId}`,
      params.data,
      {
        responseType: 'blob'
      }
    )
  },
  //导出估价明细
  exportEvaluateDetail: (data) => {
    return API.post('/sourcing/tenant/evaluate/exportEvaluateDetail', data, {
      responseType: 'blob'
    })
  },
  exportExcel: (data) => {
    return API.post(`/sourcing/tenant/evaluate/exportEvaluateTemplate`, data, {
      responseType: 'blob'
    })
  },
  //导出价格记录
  exportPriceRecord: (data) => {
    return API.post(`/price/tenant/pricerecord/query/item/export?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data, {
      responseType: 'blob'
    })
  },
  //导出价格记录 - 动态列导出
  exportDynamicPriceRecord: (data) => {
    return API.post(`/price/tenant/pricerecord/record/dynamic/export?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data, {
      responseType: 'blob'
    })
  },
  // 价格记录 - 定价后转合同
  addFromPricePv: (data) => {
    return API.post('/srm-purchase-pv/tenant/pv/contract/addFromPrice', data)
  },
  // 价格记录-新 - 动态列导出
  exportPriceRecordNew: (data) => {
    return API.post(
      `${PROXY_PRICEING}/tenant/priceRecord/new/record/export?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      data,
      {
        responseType: 'blob'
      }
    )
  },
  // 价格记录物流 - 动态列导出
  exportLogistics: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/price/logistics/export`, data, {
      responseType: 'blob'
    })
  },
  //部品模具价格记录
  getItemMoldPriceRecords: `${PROXY_PRICEING}/tenant/pricerecord/query/mouldItem?BU_CODE=${localStorage.getItem(
    'currentBu'
  )}`,

  /**********    液晶屏销售协议 ***********/
  // 查询列表
  getLcdScreenSaleAgreementList: `${PROXY_SOURCING}/tenant/sales/agreement/list`,
  // 屏销售协议打印
  xsAgreementPrint: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/sales/agreement/agreementPrint`, data, {
      responseType: 'blob'
    }),
  // 抵账协议打印
  dzAgreementPrint: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/sales/agreement/creditPrint`, data, {
      responseType: 'blob'
    }),

  // 屏编码对照表 - 导入
  excelimport: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/screen/contrast/contrastImport`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 屏编码对照表 - 模板下载
  exportTpl: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/screen/contrast/downloadTemplate`, data, {
      responseType: 'blob'
    })
  },

  // 屏编码对照表 - 导出
  reFpExport(data = {}) {
    return API.post(`${PROXY_SOURCING}/tenant/screen/contrast/contrastExport`, data, {
      responseType: 'blob'
    })
  },
  // 屏编码对照 供应商查询
  encodingSupplierQuery: (data) => {
    return API.post('/masterDataManagement/tenant/supplier/pagedQueryByOrgCode', data)
  },
  // 屏编码对照 行编辑新增保存
  encodingRowSave: (data) => {
    return API.post('/sourcing/tenant/screen/contrast/saveItem', data)
  },
  // 屏编码对照 删除
  encodingRowDelete: (data) => {
    return API.get('/sourcing/tenant/screen/contrast/deleteByIds', data)
  },
  // odmin价格记录 - 获取数据
  getOdminPricingRecords: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/odmin/priceRecord/pageQuery?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data)
  },
  // odmin价格记录 - 同步SAP
  odminSyncSap: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/odmin/priceRecord/syncSap?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data)
  },
  // odmin价格记录 - 导出
  odminExport: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/odmin/priceRecord/exportPriceRecord?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data, {
      responseType: 'blob'
    })
  },
  // 海运年约需求 - 获取数据
  getSeaAnnualPricingRecords: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/price/record/sea/list`, data)
  },
  // 海运年约需求 - 推送TMS
  seaAnnualSyncTMS: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/price/record/sea/push`, data)
  },
  // 海运年约需求 - 导出
  exportSeaAnnual: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/price/record/sea/export/list`, data, {
      responseType: 'blob'
    })
  },
  // 铁运 - 获取数据
  getRailwayAnnualPricingRecords: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/RailwayLogistics/list`, data)
  },
  // 铁运 - 推送TMS
  railwayAnnualSyncTMS: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/RailwayLogistics/push`, data)
  },
  // 铁运 - 导出
  exportRailwayAnnual: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/RailwayLogistics/export/list`, data, {
      responseType: 'blob'
    })
  },
  // 铁运班列 - 获取数据
  getRailwayBAnnualPricingRecords: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/point/priceRecordRailway/list`, data)
  },
  // 铁运班列 - 推送TMS
  railwayBAnnualSyncTMS: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/point/priceRecordRailway/push`, data)
  },
  // 铁运班列 - 导出
  exportRailwayBAnnual: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/point/priceRecordRailway/export/list`, data, {
      responseType: 'blob'
    })
  },
  // 干线 - 获取数据
  getTrunkAnnualPricingRecords: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/trunk/logistics/list`, data)
  },
  // 干线 - 推送TMS
  trunkAnnualSyncTMS: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/trunk/logistics/push`, data)
  },
  // 干线 - 导出
  exportTrunkBAnnual: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/trunk/logistics/export/list`, data, {
      responseType: 'blob'
    })
  },
  // 价格记录差异 - 分页查询
  pageDiffApi: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/price/diff/page`, data)
  },
  // 价格记录差异 - 导出
  exportDiffApi: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/price/diff/export`, data, {
      responseType: 'blob'
    })
  },
  // 差异对比
  codesDiffApi: (data) => {
    return API.post(`${PROXY_PRICEING}/tenant/price/diff/codes?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`, data)
  },

  //  bom文件管理

  // 获取台账分页数据
  pageDataApi: (data) => {
    return API.post(`/contract/tenant/purchase/bom/manage/query`, data)
  },
  // 权限申请
  applyPermissionApi: (data) => {
    return API.post(`/contract/tenant/purchase/bom/applyCommon/apply`, data)
  },
  // 删除数据
  deleteDataApi: (data) => {
    return API.post(`/contract/tenant/purchase/bom/manage/delete`, data)
  },
  // 导出数据
  exportDataApi: (data) => {
    return API.post(`/contract/tenant/purchase/bom/manage/excelExport`, data, {
      responseType: 'blob'
    })
  },
  // 导入
  importDataApi: (data) => {
    return API.post(`/contract/tenant/purchase/bom/manage/importData`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
      responseType: 'blob'
    })
  },
  // 模板下载
  templateDownloadApi: (data) => {
    return API.get(`/contract/tenant/purchase/bom/manage/excelImportTemplate`, data, {
      responseType: 'blob'
    })
  }
}

export default {
  NAME,
  APIS
}
