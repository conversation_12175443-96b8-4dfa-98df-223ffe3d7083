/*
 * 基础字段
 *（目前整理阶梯相关字段，后续持续改进）
 **/
import { i18n } from '@/main.js'
import { formatTime } from '@/utils/utils'
import { makeTextFields, filteringByText } from '@/views/common/columnData/utils'
import { useFiltering } from '@/utils/ej/select'
// 供应商列表字段转换
const whetherMap = {
  0: i18n.t('否'),
  1: i18n.t('是')
}
// 阶梯类型
const stepQuoteTypeMap = {
  '-1': i18n.t('无梯度'),
  0: i18n.t('数量累计阶梯'),
  3: i18n.t('数量逐层阶梯')
}
// 状态
const priceStatusMap = {
  1: i18n.t('未审核'),
  2: i18n.t('已审核'),
  3: i18n.t('已拒绝'),
  4: i18n.t('已议价'),
  5: i18n.t('已中标'),
  6: i18n.t('待定点'),
  7: i18n.t('议价中')
}
// 报价属性
const quoteAttributeMap = {
  standard_price: i18n.t('标准价'),
  mailing_price: i18n.t('寄售价'),
  outsource: i18n.t('委外价')
}
// 报价方式
const quoteModeMap = {
  in_warehouse: i18n.t('按照入库'),
  out_warehouse: i18n.t('按出库'),
  order_date: i18n.t('按订单日期')
}
// 来源
const sourceTypeMap = {
  0: i18n.t('当前来源'),
  1: i18n.t('历史配额')
}
// 价格来源
const referChannelMap = {
  '-1': '',
  0: i18n.t('手工定价'),
  2: i18n.t('价格记录')
}
// 价格单位 - 通采
const generalPriceUnitList = [
  { text: '1', value: '1' },
  { text: '1000', value: '1000' }
]
// 价格单位 - 非采
const priceUnitList = [
  { text: i18n.t('元'), value: '1' },
  { text: i18n.t('万元'), value: '0.0001' }
]
// 阶梯类型
const stepQuoteTypeList = [
  { text: i18n.t('数量累计阶梯'), value: 0 },
  { text: i18n.t('数量逐层阶梯'), value: 3 }
]

// 报价有效期日期验证辅助函数
const validateQuoteEffectiveDate = (params, fieldType) => {
  if (!params.newValue || params.newValue === params.oldValue) {
    return true
  }

  const currentDate = new Date()
  currentDate.setHours(0, 0, 0, 0)

  // 处理输入值，可能是时间戳或日期字符串
  let inputDate
  if (typeof params.newValue === 'number') {
    inputDate = new Date(params.newValue)
  } else if (typeof params.newValue === 'string') {
    inputDate = new Date(params.newValue)
  } else {
    inputDate = params.newValue
  }

  const rowData = params.data

  // 验证不能早于当前日期
  if (inputDate.getTime() < currentDate.getTime()) {
    params.node.setDataValue(params.colDef.field, params.oldValue)
    console.warn(`报价有效期${fieldType === 'start' ? '开始' : '结束'}日期不能早于当前日期`)
    return false
  }

  // 验证开始日期和结束日期的关系
  if (fieldType === 'start' && rowData.quoteEffectiveEndDate) {
    const endDate = new Date(rowData.quoteEffectiveEndDate)
    if (inputDate.getTime() > endDate.getTime()) {
      params.node.setDataValue(params.colDef.field, params.oldValue)
      console.warn('报价有效期开始日期不能晚于结束日期')
      return false
    }
  } else if (fieldType === 'end' && rowData.quoteEffectiveStartDate) {
    const startDate = new Date(rowData.quoteEffectiveStartDate)
    if (inputDate.getTime() < startDate.getTime()) {
      params.node.setDataValue(params.colDef.field, params.oldValue)
      console.warn('报价有效期结束日期不能早于开始日期')
      return false
    }
  }

  return true
}

// 获取动态日期限制
const getDateLimits = (params, fieldType) => {
  const currentDate = new Date()
  currentDate.setHours(0, 0, 0, 0)
  const rowData = params?.data || {}

  const limits = {
    min: currentDate
  }

  if (fieldType === 'start' && rowData.quoteEffectiveEndDate) {
    limits.max = new Date(rowData.quoteEffectiveEndDate)
  } else if (fieldType === 'end' && rowData.quoteEffectiveStartDate) {
    const startDate = new Date(rowData.quoteEffectiveStartDate)
    limits.min = new Date(Math.max(startDate.getTime(), currentDate.getTime()))
  }

  return limits
}

// 自动填充报价有效期至的逻辑
const autoFillQuoteEffectiveEndDate = (params, companyCode, rfxGeneralType) => {
  if (!params.newValue || params.newValue === params.oldValue) {
    return
  }

  // 处理输入值，可能是时间戳或日期字符串
  let startDate
  if (typeof params.newValue === 'number') {
    startDate = new Date(params.newValue)
  } else if (typeof params.newValue === 'string') {
    startDate = new Date(params.newValue)
  } else {
    startDate = params.newValue
  }
  let endDateValue = null

  // 1503、0602公司的自动设置逻辑
  if (['1503', '0602'].includes(companyCode)) {
    const year = startDate.getFullYear()
    const yearTwo = year + 1
    const june30 = new Date(year, 5, 30) // 6月30日
    const july1 = new Date(year, 6, 1)   // 7月1日

    if (startDate.getTime() >= july1.getTime()) {
      endDateValue = `${yearTwo}-12-31`
    } else if (startDate.getTime() <= june30.getTime()) {
      endDateValue = `${year}-12-31`
    }
  }
  // 2M01、2S06公司的自动设置逻辑
  else if ((() => {
    const txCompanyList = JSON.parse(sessionStorage.getItem('txCompanyList') || '["2M01", "2S06"]')
    return txCompanyList.includes(companyCode)
  })()) {
    const isNonProcurement = rfxGeneralType === 2
    if (isNonProcurement) {
      // 非采：报价有效期至为报价有效期从的半年后日期
      const endDate = new Date(startDate)
      endDate.setMonth(startDate.getMonth() + 6)
      endDateValue = endDate.getFullYear() + '-' +
        String(endDate.getMonth() + 1).padStart(2, '0') + '-' +
        String(endDate.getDate()).padStart(2, '0')
    } else {
      // 通采：按原有逻辑设置为2099-12-31
      endDateValue = '2099-12-31'
    }
  }
  // 其他公司的默认逻辑
  else {
    endDateValue = '2099-12-31'
  }

  // 设置结束日期值
  if (endDateValue) {
    params.node.setDataValue('quoteEffectiveEndDate', endDateValue)

    // 强制刷新该行以确保显示正确
    params.api.refreshCells({
      rowNodes: [params.node],
      columns: ['quoteEffectiveEndDate'],
      force: true
    })
  }
}

// 列配置
export function columnData(args) {
  return [
    {
      field: 'rowGroupIndex',
      headerText: i18n.t('序号'),
      width: 70,
      editable: false
    },
    {
      field: 'rowIndex',
      headerText: i18n.t('序号'),
      width: 70,
      editable: false
    },
    {
      field: 'lineNo',
      headerText: i18n.t('行号'),
      width: 70,
      editable: false
    },
    {
      field: 'priceStatus',
      headerText: i18n.t('状态'),
      valueFormatter: (params) => {
        return !params.value && params.value !== 0 ? i18n.t('未报价') : priceStatusMap[params.value]
      }
    },
    {
      field: 'stepQuote',
      width: 120,
      headerText: i18n.t('是否阶梯报价'),
      valueFormatter: (params) => {
        return whetherMap[params.value]
      }
    },
    {
      field: 'stepQuoteType',
      width: 122,
      headerText: i18n.t('阶梯类型'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          dataSource: stepQuoteTypeList
        }
      },
      valueFormatter: (params) => {
        return stepQuoteTypeMap[params.value]
      },
      editable: (params) => {
        return params.data.stepQuote
      },
      cellClass: (params) => {
        return params.data.stepQuote ? 'singleCellDisable' : ''
      }
    },
    {
      field: 'quoteAttribute',
      width: 122,
      headerText: i18n.t('报价属性'),
      valueFormatter: (params) => {
        return quoteAttributeMap[params.value]
      }
    },
    {
      field: 'quoteMode',
      headerText: i18n.t('报价方式'),
      valueFormatter: (params) => {
        return quoteModeMap[params.value]
      }
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      option: 'customEdit',
      width: 220,
      editable: true
    },
    {
      field: 'sourceType',
      headerText: i18n.t('来源'),
      valueFormatter: (params) => {
        return sourceTypeMap[params.value]
      }
    },
    {
      field: 'stepNum',
      headerText: i18n.t('阶梯数量'),
      valueFormatter: (params) => {
        return params.value || ''
      }
    },
    {
      field: 'allocationRatio',
      headerText: i18n.t('配额'),
      valueFormatter: (params) => {
        let val = params?.value ? Number(params.value).toFixed(0) : 0
        return val + '%'
      },
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: 100,
          precision: '0'
          // validateDecimalOnType: true
        }
      }
    },
    {
      field: 'minSplitQuantity',
      headerText: i18n.t('最小起拆点'),
      option: 'customEdit',
      editConfig: {
        type: 'number',
        props: {
          min: 0
        }
      }
    },
    {
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { text: 'orgCode', value: 'orgCode' },
          dataSource: args?.siteNameList
        }
      }
    },

    // {
    //   field: 'itemName',
    //   headerText: i18n.t('物料名称')
    // },
    // {
    //   field: 'temporaryItemCode',
    //   headerText: i18n.t('临时物料编码')
    // },

    {
      field: 'paymentCondition',
      headerText: i18n.t('付款条件'),
      editable: (params) => args?.isTx && params?.data?.editable, // 判断通讯及是否多种付款条件
      option: 'customEdit',
      editConfig: {
        type: 'cellRemoteSelect',
        props: {
          url: '/masterDataManagement/tenant/paymentTermsSj/paged-query',
          params: {},
          paramsKey: 'paymentTermsCode',
          fields: { text: 'paymentTermsCode-paymentTermsName', value: 'paymentTermsCode' }
        }
      }
    },

    {
      field: 'categoryCode',
      width: 100,
      headerText: i18n.t('品类编码'),
      editable: false
    },
    {
      field: 'categoryName',
      width: 200,
      headerText: i18n.t('品类名称'),
      option: 'customEdit',
      editable: (params) => !args?.isSup && !params?.data?.itemCode,
      editConfig: {
        type: 'cellRemoteSelect',
        props: {
          url: '/sourcing/tenant/permission/queryCategorys',
          searchFields: ['categoryName', 'categoryCode'],
          fields: { text: 'categoryCode-categoryName', value: 'categoryName' }
        }
      },
      valueFormatter: (params) => {
        return params.data.categoryCode
          ? params.data.categoryCode + '-' + params.data.categoryName
          : params.data.categoryName
          ? params.data.categoryName
          : ''
      }
    },
    {
      field: 'priceUnitName',
      headerText: i18n.t('价格单位'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          dataSource: args?.isFc ? priceUnitList : generalPriceUnitList
        }
      }
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位'),
      option: 'customEdit',
      editable: (params) => !params?.data?.itemCode,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('unitName'),
          'allow-filtering': true,
          filtering: useFiltering(filteringByText),
          dataSource: args?.unitNameList || []
        }
      }
    },
    {
      field: 'massTrialFlag',
      headerText: i18n.t('试产/量产标识'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          dataSource: args?.massTrialFlagData || []
        }
      }
    },
    {
      field: 'currencyName',
      headerText: i18n.t('币种名称'),
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          'allow-filtering': true,
          filtering: useFiltering(filteringByText),
          dataSource: args?.currencyNameData || []
        }
      }
    },
    // {
    //   field: 'unitCode',
    //   headerText: i18n.t('基本单位编码')
    // },
    // {
    //   field: 'drawing',
    //   headerText: i18n.t('附件'),
    // },
    // {
    //   field: 'supplierDrawing',
    //   headerText: i18n.t('供应商附件'),
    //   editable: true //强控
    // },
    {
      field: 'costModelId',
      headerText: i18n.t('成本模型ID'),
      hide: true //强控
    },
    {
      field: 'costModelVersionCode',
      headerText: i18n.t('成本模型版本编码'),
      hide: true //强控
    },
    {
      field: 'costModelCode',
      headerText: i18n.t('成本模型编码'),
      hide: true //强控
    },
    {
      field: 'costModelName',
      headerText: i18n.t('成本模型'),
      option: 'customEdit',
      editConfig: {
        type: 'cellCost',
        props: {
          rfxId: args?.rfxId,
          detailInfo: args?.detailInfo
        }
      },
      editable: (params) => params.data.costModelQuote,
      cellClassRules: {
        singleCellDisable: (params) => !params.data.costModelQuote
      }
    },
    {
      field: 'quoteEffectiveStartDate',
      headerText: i18n.t('报价有效期从'),
      width: 120,
      valueFormatter: (params) => {
        if (!params.value) return ''
        // 处理不同格式的日期值
        if (typeof params.value === 'number') {
          return formatTime(new Date(params.value), 'Y-mm-dd')
        } else if (typeof params.value === 'string') {
          return formatTime(new Date(params.value), 'Y-mm-dd')
        } else if (params.value instanceof Date) {
          return formatTime(params.value, 'Y-mm-dd')
        }
        return params.value
      },
      editable: () => args?.isTx,
      option: 'customEdit',
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'show-clear-button': false,
          // 动态设置最小日期
          min: (() => {
            const currentDate = new Date()
            currentDate.setHours(0, 0, 0, 0)
            return currentDate
          })()
        }
      },
      // 添加单元格值变化事件处理
      onCellValueChanged: (params) => {
        // 先进行验证
        const isValid = validateQuoteEffectiveDate(params, 'start')
        if (isValid) {
          // 验证通过后，执行自动填充逻辑
          autoFillQuoteEffectiveEndDate(params, args?.companyCode, args?.rfxGeneralType)
        }
        return isValid
      },
      // 添加编辑器参数配置
      cellEditorParams: (params) => {
        const limits = getDateLimits(params, 'start')
        return {
          min: limits.min,
          max: limits.max
        }
      }
    },
    {
      field: 'quoteEffectiveEndDate',
      headerText: i18n.t('报价有效期至'),
      width: 120,
      valueFormatter: (params) => {
        if (!params.value) return ''
        // 处理不同格式的日期值
        if (typeof params.value === 'number') {
          return formatTime(new Date(params.value), 'Y-mm-dd')
        } else if (typeof params.value === 'string') {
          return formatTime(new Date(params.value), 'Y-mm-dd')
        } else if (params.value instanceof Date) {
          return formatTime(params.value, 'Y-mm-dd')
        }
        return params.value
      },
      editable: () => args?.isTx,
      option: 'customEdit',
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'show-clear-button': false,
          // 动态设置最小日期
          min: (() => {
            const currentDate = new Date()
            currentDate.setHours(0, 0, 0, 0)
            return currentDate
          })()
        }
      },
      // 添加单元格值变化事件处理
      onCellValueChanged: (params) => {
        return validateQuoteEffectiveDate(params, 'end')
      },
      // 添加编辑器参数配置
      cellEditorParams: (params) => {
        const limits = getDateLimits(params, 'end')
        return {
          min: limits.min,
          max: limits.max
        }
      }
    },
    {
      field: 'quotaEffectiveStartDate',
      headerText: i18n.t('报价有效期从'),
      width: 120,
      valueFormatter: (params) => {
        return params.value ? formatTime(new Date(params.value), 'Y-mm-dd') : ''
      }
    },
    {
      field: 'quotaEffectiveEndDate',
      headerText: i18n.t('报价有效期至'),
      width: 120,
      valueFormatter: (params) => {
        return params.value ? formatTime(new Date(params.value), 'Y-mm-dd') : ''
      }
    },
    {
      field: 'itemExtMap.referChannel',
      headerText: i18n.t('价格来源'),
      valueFormatter: (params) => {
        return referChannelMap[params.value]
      }
    },
    {
      field: 'costModelQuote',
      headerText: i18n.t('是否成本模型'),
      width: 100,
      valueFormatter: (params) => {
        return whetherMap[params.value]
      },
      editable: (params) => params.data.itemCode && params.data.categoryCode,
      cellClassRules: {
        singleCellDisable: (params) => !params.data.itemCode || !params.data.categoryCode
      }
    },
    // {
    //   field: 'costModelEstimatePrice',
    //   headerText: i18n.t('测算价格'),
    //   editable: false // 强控
    // },
    {
      field: 'selfPurchasing',
      headerText: i18n.t('是否自购'),
      valueFormatter: (params) => {
        return whetherMap[params.value]
      }
    },
    {
      field: 'adviseMinPackageQuantity',
      headerText: i18n.t('建议最小包装量'),
      width: 90
    },
    {
      field: 'purGroupCode',
      headerText: i18n.t('采购组代码'),
      width: 230,
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('groupCode'),
          'allow-filtering': true,
          'filter-type': 'Contains',
          dataSource: args?.purGroupList || []
        }
      }
    },
    {
      field: 'taxRateName',
      headerText: i18n.t('税率名称'),
      width: 230,
      option: 'customEdit',
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('taxItemName'),
          'allow-filtering': true,
          'filter-type': 'Contains',
          dataSource: args?.taxRateNameList || []
        }
      }
    }
  ]
}
