import { i18n } from '@/main.js'
import { fmtSelect, fmtDatetime, createFmtDatetime } from '@/utils/ej/dataGrid/formatter'
import { useFiltering } from '@/utils/ej/select'
import { getValueByPath } from '@/utils/obj'
import { addArrTextField, makeTextFields, filteringByText } from './utils'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'

// 涉及单价和总价，后端预留4位小数。前端限制最多输入12个9（整数位），前端输入小数不改
const MAX_SAFE_INTEGER = 999999999999

// mt_supplier_bidding_item biddingItemDTO rfx_item_ext
export const getFields = ({
  currencyList = [],
  taxList = [],
  purUnitList = [],
  editInstance,
  quotedPriceData
} = {}) => {
  const currencyListCN = addArrTextField(currencyList, 'currencyCode', 'currencyName')
  const taxListCN = addArrTextField(taxList, 'taxItemCode', 'taxItemName')
  const purUnitListCN = addArrTextField(purUnitList, 'unitCode', 'unitName')

  return {
    // 报价方式
    quoteMode: {
      formatter: fmtSelect,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 'in_warehouse', text: i18n.t('按照入库') },
            { value: 'out_warehouse', text: i18n.t('按出库') },
            { value: 'order_date', text: i18n.t('按订单日期') }
          ]
        }
      }
    },

    // 报价属性
    quoteAttribute: {
      formatter: fmtSelect,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { value: 'standard_price', text: i18n.t('标准价') },
            { value: 'mailing_price', text: i18n.t('寄售价') },
            { value: 'outsource', text: i18n.t('委外价') }
          ]
        }
      }
    },

    // 币种编码
    bidCurrencyCode: {
      formatter: fmtSelect,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyCode'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },
    // 币种名称
    bidCurrencyName: {
      formatter: fmtSelect,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('currencyName'),
          dataSource: currencyListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // quoteEffectiveStartDate 报价有效期从
    quoteEffectiveStartDate: {
      formatter: createFmtDatetime('YYYY-MM-DD'), //有效期取消时分秒
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'value-format': 'yyyy-MM-dd',
          'show-clear-button': false
        }
      },
      edit:
        editInstance &&
        editInstance.create({
          onInput: (ctx, { value, field }) => {
            if (value) {
              if (['1503', '0602'].includes(quotedPriceData.companyCode)) {
                var date = new Date()
                var year = date.getFullYear()
                var yearTwo = date.getFullYear() + 1
                let datae = year + '-' + '6' + '-' + '30'
                let dateTwo = year + '-' + '7' + '-' + '1'
                if (new Date(value).getTime() >= new Date(dateTwo).getTime()) {
                  ctx.setValueByField(
                    'biddingItemDTO.quoteEffectiveEndDate',
                    yearTwo + '-' + '12' + '-' + '31 '
                  )
                } else if (new Date(value).getTime() <= new Date(datae).getTime()) {
                  ctx.setValueByField(
                    'biddingItemDTO.quoteEffectiveEndDate',
                    year + '-' + '12' + '-' + '31 '
                  )
                }
              }
              const txCompanyList = JSON.parse(sessionStorage.getItem('txCompanyList') || '["2M01", "2S06"]')
              if (txCompanyList.includes(quotedPriceData.companyCode)) { // 通讯公司特殊处理
                // 判断是否为非采 (rfxGeneralType: 1=通采, 2=非采)
                const isNonProcurement = quotedPriceData.rfxGeneralType === 2

                if (isNonProcurement) {
                  // 非采：报价有效期至为报价有效期从的半年后日期
                  const startDate = new Date(value)
                  const endDate = new Date(startDate)
                  endDate.setMonth(startDate.getMonth() + 6) // 设置为半年后
                  const formattedEndDate = endDate.getFullYear() + '-' +
                    String(endDate.getMonth() + 1).padStart(2, '0') + '-' +
                    String(endDate.getDate()).padStart(2, '0')
                  ctx.setValueByField(
                    'biddingItemDTO.quoteEffectiveEndDate',
                    formattedEndDate
                  )
                } else {
                  // 通采：按原有逻辑设置为2099-12-31
                  ctx.setValueByField(
                    'biddingItemDTO.quoteEffectiveEndDate',
                    '2099-12-31'
                  )
                }
              }
              ctx.setOptions(field.replace('quoteEffectiveStartDate', 'quoteEffectiveEndDate'), {
                // 报价有效期至 必须大于当前时间
                min: new Date(Math.max(new Date(value).getTime(), Date.now()))
              })
            } else {
              ctx.setOptions(field.replace('quoteEffectiveStartDate', 'quoteEffectiveEndDate'), {
                min: new Date()
              })
            }
          },
          getEditConfig: ({ rowData }) => {
            return {
              type: 'date',
              format: 'yyyy-MM-dd',
              'value-format': 'yyyy-MM-dd',
              'show-clear-button': false,
              max: new Date(
                getValueByPath(rowData, 'biddingItemDTO.quoteEffectiveEndDate') || 0,
                Date.now()
              )
            }
          }
        })
    },
    // quoteEffectiveEndDate 报价有效期至
    quoteEffectiveEndDate: {
      formatter: createFmtDatetime('YYYY-MM-DD'),
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'value-format': 'yyyy-MM-dd',
          'show-clear-button': false
          // min: new Date(),
          // max: new Date(),
        }
      },
      // [报价有效期至] 必须大于 [报价有效期从]
      // 报价有效期至 必须大于当前时间
      edit:
        editInstance &&
        editInstance.create({
          onInput: (ctx, { value, field }) => {
            // if (value && typeof value === 'string') {
            ctx.setOptions(field.replace('quoteEffectiveEndDate', 'quoteEffectiveStartDate'), {
              max: new Date(new Date(value).getTime())
            })
            // } else {
            //   ctx.setOptions(field.replace('quoteEffectiveEndDate', 'quoteEffectiveStartDate'), {
            //     max: new Date()
            //   })
            // }
          },
          // eslint-disable-next-line no-unused-vars
          getEditConfig: ({ rowData }) => {
            return {
              type: 'date',
              format: 'yyyy-MM-dd',
              'value-format': 'yyyy-MM-dd',
              'show-clear-button': false
              // min: new Date(
              //   Math.max(
              //     getValueByPath(rowData, 'biddingItemDTO.quoteEffectiveStartDate') || 0,
              //     Date.now()
              //   )
              // )
            }
          }
        })
    },

    // 模具T1时间
    // 必须晚于当前时间，只填写日期，不需要时间（时、分、秒）
    dieT1: {
      formatter: createFmtDatetime('YYYY-MM-DD'),
      editConfig: {
        type: 'date',
        props: {
          format: 'yyyy-MM-dd',
          'value-format': 'yyyy-MM-dd',
          'show-clear-button': false,
          min: new Date(),
          'time-stamp': true
        }
      }
    },

    // 分摊价格（未税）
    sharePriceUntaxed: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },

    // 分摊价格（含税）
    sharePriceTaxed: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },

    // taxedTotalPrice 总价（含税）
    taxedTotalPrice: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          // max: MAX_SAFE_INTEGER,
          readonly: true
          // precision: 2,
        }
      }
    },
    // untaxedTotalPrice 总价（未税）
    untaxedTotalPrice: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          // max: MAX_SAFE_INTEGER,
          readonly: true
          // precision: 2,
        }
      }
    },
    // taxedUnitPrice 单价（含税）
    taxedUnitPrice: {
      // editConfig: {
      //   type: 'number',
      //   props: {
      //     min: 0,
      //     // max: MAX_SAFE_INTEGER,
      //     readonly: true
      //     // precision: 2,
      //   }
      // }
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    allocationQuantity: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          // max: MAX_SAFE_INTEGER,
          readonly: true
          // precision: '0',
        }
      }
    },
    // untaxedUnitPrice 单价（未税）
    untaxedUnitPrice: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: (() => {
            const txCompanyList = JSON.parse(sessionStorage.getItem('txCompanyList') || '["2M01", "2S06"]')
            return txCompanyList.includes(quotedPriceData?.companyCode) ? 5 : 2
          })()
        }
      }
    },
    // bidTaxRateValue 税率值
    bidTaxRateValue: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          // max: MAX_SAFE_INTEGER,
          readonly: true
          // precision: 2,
        }
      }
    },
    // 最小包装数量
    minPackageQuantity: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: '0'
        }
      }
    },

    // 最小采购量
    minPurQuantity: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: '0'
        }
      }
    },

    // 报价说明
    quoteRemark: {
      editConfig: {
        type: 'text',
        props: {}
      }
    },
    // 供应商物料编码
    supplierItemCode: {
      editConfig: {
        type: 'text',
        props: {}
      }
    },

    // 供方税率编码
    bidTaxRateCode: {
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('taxItemCode'),
          dataSource: taxListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // 供方税率名称
    bidTaxRateName: {
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('taxItemName'),
          dataSource: taxListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // 价格单位
    priceUnitName: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 价格单位编码
    priceUnitCode: {
      editConfig: {
        type: 'text',
        props: {}
      }
    },

    // 运费
    transportCost: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 加工方材料费
    processCost: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 加工费
    processPartyMaterialCost: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },

    // 模具价格（未税)
    diePriceUntaxed: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 模具价格（含税）
    diePriceTaxed: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },

    // 非屏价格（含税）
    notScreenPrice: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 上次报价（未税）
    lastQuoteUntaxed: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          // max: MAX_SAFE_INTEGER,
          readonly: true
          // precision: 2,
        }
      }
    },
    // 上次报价（含税）
    lastQuoteTaxed: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          // max: MAX_SAFE_INTEGER,
          readonly: true
          // precision: 2,
        }
      }
    },
    // 汇率
    exchangeRate: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },

    // 无条件L/T
    unconditionalLeadTime: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // L/T
    leadTime: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 建议L/T
    adviseLeadTime: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 建议无条件L/T
    adviseUnconLeadTime: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },
    // 运输周期
    transportCycle: {
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        props: {
          format: 'yyyy-MM-dd HH:mm:ss',
          'value-format': 'yyyy-MM-dd HH:mm:ss',
          // "time-stamp": true,
          'show-clear-button': false,
          min: new Date()
        }
      }
    },
    // 生产周期
    productionCycle: {
      formatter: fmtDatetime,
      editConfig: {
        type: 'datetime',
        props: {
          format: 'yyyy-MM-dd HH:mm:ss',
          'value-format': 'yyyy-MM-dd HH:mm:ss',
          // "time-stamp": true,
          'show-clear-button': false,
          min: new Date()
        }
      }
    },

    // 采购单位编码
    bidPurUnitCode: {
      formatter: fmtSelect,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('unitCode'),
          dataSource: purUnitListCN,
          disabled: true,
          readonly: true
        }
      }
    },
    // 采购单位
    bidPurUnitName: {
      formatter: fmtSelect,
      editConfig: {
        type: 'select',
        props: {
          'show-clear-button': true,
          fields: makeTextFields('unitName'),
          dataSource: purUnitListCN,
          'allow-filtering': true,
          filtering: useFiltering(filteringByText)
        }
      }
    },

    // 转换率
    bidConversionRate: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        }
      }
    },

    // 报价类型（0报价 1议价）
    priceType: {
      formatter: fmtSelect,
      editConfig: {
        type: 'select',
        props: {
          disabled: true,
          readonly: true,
          fields: { value: 'value', text: 'text' },
          dataSource: [
            { text: i18n.t('报价'), value: 0 },
            { text: i18n.t('议价'), value: 1 }
          ]
        }
      }
    },

    // 实际分摊价（含税）
    realSharePriceTaxed: {
      editConfig: {
        type: 'number',
        props: {
          ...PRICE_EDIT_CONFIG,
          disabled: true,
          readonly: true
        }
      }
    },
    // 实际分摊价（未税）
    realSharePriceUntaxed: {
      editConfig: {
        type: 'number',
        props: {
          ...PRICE_EDIT_CONFIG,
          disabled: true,
          readonly: true
        }
      }
    },
    // 规划分摊价（含税）
    planSharePriceTaxed: {
      editConfig: {
        type: 'number',
        props: {
          ...PRICE_EDIT_CONFIG,
          disabled: true,
          readonly: true
        }
      }
    },
    // 规划分摊价（未税）
    planSharePriceUntaxed: {
      editConfig: {
        type: 'number',
        props: {
          ...PRICE_EDIT_CONFIG,
          disabled: true,
          readonly: true
        }
      }
    },
    // 报价有效自然日
    quoteEffectiveNatureDay: {
      editConfig: {
        type: 'number',
        props: {
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: '0'
        }
      }
    }
  }
}
