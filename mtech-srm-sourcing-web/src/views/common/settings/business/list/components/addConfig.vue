<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :header="header"
      @beforeClose="cancel"
    >
      <div class="dialog-content">
        <mt-form ref="dialogRef" :model="formObjects" :rules="formRules">
          <mt-form-item prop="templateCode" :label="$t('模板编码')">
            <mt-input
              v-model="formObjects.templateCode"
              float-label-type="Never"
              :placeholder="$t('请输入模板编码')"
              maxlength="50"
            ></mt-input>
          </mt-form-item>
          <mt-form-item prop="businessTypeId" :label="$t('业务类型')"
            ><mt-select
              ref="businessTypeRef"
              v-model="formObjects.businessTypeId"
              float-label-type="Never"
              :allow-filtering="true"
              :show-clear-button="true"
              :data-source="businessTypeList"
              :fields="{ text: 'itemName', value: 'id' }"
              :placeholder="$t('请选择业务类型')"
              @change="$nextTick(() => getReferTemplate())"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="sourcingObj" :label="$t('询价对象')">
            <mt-input
              v-model="formObjects.sourcingObj"
              float-label-type="Never"
              :placeholder="$t('请输入询价对象')"
              maxlength="50"
            ></mt-input>
            <!-- <mt-select
              ref="sourcingObjRef"
              v-model="formObjects.sourcingObj"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="sourcingObjList"
              :placeholder="$t('请选择询价对象')"
            ></mt-select> -->
          </mt-form-item>
          <mt-form-item prop="sourcingMode" :label="$t('寻源方式')">
            <mt-select
              :data-source="sourcingModeList"
              v-model="formObjects.sourcingMode"
              :show-clear-button="true"
              :placeholder="$t('请选择寻源方式')"
              @change="changeSourcingMode"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="sourcingObjType" :label="$t('询价对象模型')">
            <mt-select
              ref="sourcingObjTypeRef"
              v-model="formObjects.sourcingObjType"
              float-label-type="Never"
              :show-clear-button="true"
              :data-source="getSourcingObjTypeList"
              :placeholder="$t('请选择询价模型')"
            ></mt-select>
          </mt-form-item>
          <mt-form-item prop="refTemplateCode" :label="$t('参考模板编码')">
            <mt-select
              ref="templateCodeRef"
              v-model="formObjects.refTemplateCode"
              float-label-type="Never"
              :show-clear-button="true"
              :allow-filtering="true"
              :fields="{ text: 'templateCode', value: 'templateCode' }"
              :data-source="referTemplateList"
              :placeholder="$t('请选择参考模板编码')"
            ></mt-select>
          </mt-form-item>

          <mt-form-item prop="remark" :label="$t('备注')">
            <mt-input
              v-model="formObjects.remark"
              float-label-type="Never"
              :placeholder="$t('请输入备注')"
              maxlength="200"
            ></mt-input>
          </mt-form-item>
        </mt-form>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { sourcingObjTypeList } from '@/constants'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formObjects: {
        businessTypeCode: '',
        businessTypeId: '',
        businessTypeName: '',
        // configId: "",
        remark: '', //备注
        sourcingMode: 'rfq', //寻源方式
        sourcingObj: '', //询价对象
        sourcingObjType: 'common', //询价对象模型
        templateCode: '', //模板编码
        version: 0 //版本号
      },
      businessTypeList: [],
      //寻源方式  列表
      sourcingModeList: [
        { text: this.$t('询报价'), value: 'rfq' },
        // { text: this.$t("直接定价"), value: "direct_pricing" },
        { text: this.$t('招投标'), value: 'invite_bids' },
        { text: this.$t('竞价'), value: 'bidding_price' }
      ],
      //询价模型  列表
      sourcingObjTypeList,
      formRules: {
        templateCode: [
          {
            required: true,
            message: this.$t('请输入模板编码'),
            trigger: 'blur'
          }
        ],
        businessTypeId: [
          {
            required: true,
            message: this.$t('请选择业务类型'),
            trigger: 'blur'
          }
        ],
        sourcingObj: [
          {
            required: true,
            message: this.$t('请输入询价对象'),
            trigger: 'blur'
          }
        ],
        sourcingMode: [
          {
            required: true,
            message: this.$t('请选择寻源方式'),
            trigger: 'blur'
          }
        ],
        sourcingObjType: [
          {
            required: true,
            message: this.$t('请选择模型'),
            trigger: 'blur'
          }
        ]
      },
      referTemplateList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    getSourcingObjTypeList() {
      if (this.formObjects.sourcingMode === 'rfq') {
        return sourcingObjTypeList
      } else {
        let _list = JSON.parse(JSON.stringify(sourcingObjTypeList))
        let _findIndex = _list.findIndex((e) => e.value === 'HIERARCHY')
        _list.splice(_findIndex, 1)
        _findIndex = _list.findIndex((e) => e.value === 'combination')
        _list.splice(_findIndex, 1)
        return _list
      }
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getBusinessTypeList()
  },
  methods: {
    changeSourcingMode() {
      this.formObjects.sourcingObjType = null
      this.$nextTick(() => this.getReferTemplate())
    },
    getBusinessTypeList() {
      // 业务类型 - businessType
      this.$API.masterData
        .dictionaryGetList({
          dictCode: 'businessType'
        })
        .then((res) => {
          this.businessTypeList = res.data
          if (Array.isArray(res?.data) && res.data.length) {
            this.formObjects.businessTypeId = res.data[0]['id']
          }
        })
    },
    // 获取参考模板编码列表
    async getReferTemplate() {
      const params = {
        page: {
          current: 1,
          size: 99999
        },
        rules: [
          { field: 'status', label: this.$t('状态'), operator: 'equal', type: 'string', value: 1 },
          {
            field: 'businessTypeId',
            label: this.$t('业务类型'),
            operator: 'equal',
            type: 'string',
            value: this.formObjects.businessTypeId || ''
          },
          {
            field: 'sourcingMode',
            label: this.$t('寻源方式'),
            operator: 'equal',
            type: 'string',
            value: this.formObjects.sourcingMode || ''
          }
        ]
      }
      const res = await this.$API.businessConfig.getConfigList(params)
      if (res.code === 200) {
        this.referTemplateList = res.data.records
      }
    },
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = { ...this.formObjects }
          //处理下拉框数据赋值
          this.$utils.assignDataFromRefs(params, [
            {
              key: 'businessTypeId', //业务类型 businessTypeId下拉框数据
              ref: this.$refs.businessTypeRef.ejsRef,
              fields: {
                businessTypeCode: 'itemCode',
                businessTypeName: 'itemName'
              }
            }
          ])
          this.$API.businessConfig.saveConfig(params).then((res) => {
            if (res.code == 200) {
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  padding-top: 20px;
}
</style>
