import Vue from 'vue'
import { i18n, permission } from '@/main.js'
import { sourcingObjTypeList } from '@/constants'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Delete', icon: 'icon_solid_edit', title: i18n.t('删除') },
  { id: 'import_json', icon: 'icon_solid_edit', title: i18n.t('导入(JSON)') },
  { id: 'export_json', icon: 'icon_solid_edit', title: i18n.t('导出(JSON)') }

  // { id: "save", icon: "icon_solid_edit", title: i18n.t("保存") },
]
sourcingObjTypeList.forEach((e) => (e.cssClass = 'title'))

//模块功能流程配置
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'lineIndexComponent',
    headerText: i18n.t('行号'),
    allowFiltering: false, // 序号列，不参与数据筛选
    allowResizing: false, // 序号列，不参与列顺序变化
    allowSorting: false, // 序号列，不参与列宽变化
    ignore: true, // 序号列，不参与数据筛选
    width: 80,
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template: '<div>{{+data.index+1}}</div>',
          data() {
            return { data: {} }
          }
        })
      }
    }
  },
  {
    width: '100',
    field: 'status',
    headerText: i18n.t('状态'), //状态 （1 启用 2 禁用）
    valueConverter: {
      type: 'map',
      map: [
        { status: 0, label: i18n.t('草稿'), cssClass: 'title-#9baac1' },
        { status: 1, label: i18n.t('启用'), cssClass: 'title-#6386c1' },
        { status: 2, label: i18n.t('禁用'), cssClass: 'title-#9baac1' }
      ],
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'Start',
        // icon: "icon_solid_Createorder",
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['status'] !== 1
        }
      },
      {
        id: 'Stop',
        // icon: "icon_solid_Cancel",
        title: i18n.t('禁用'),
        visibleCondition: (data) => {
          return data['status'] == 1
        }
      }
    ]
  },
  {
    width: '150',
    field: 'templateCode',
    headerText: i18n.t('模板编码'),
    cssClass: 'field-content'
  },
  {
    width: '120',
    field: 'businessTypeName',
    headerText: i18n.t('业务类型')
  },
  {
    width: '120',
    field: 'sourcingObj',
    headerText: i18n.t('询价对象')
  },
  {
    width: '120',
    field: 'sourcingObjType',
    headerText: i18n.t('模板类型'),
    valueConverter: {
      type: 'map',
      map: sourcingObjTypeList
    }
  },
  {
    width: '120',
    field: 'sourcingMode',
    headerText: i18n.t('寻源方式'),
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 'rfq',
          label: i18n.t('询报价'),
          cssClass: 'title'
        },
        // {
        //   status: "direct_pricing",
        //   label: i18n.t("直接定价"),
        //   cssClass: "title",
        // },
        { status: 'invite_bids', label: i18n.t('招投标'), cssClass: 'title' },
        { status: 'bidding_price', label: i18n.t('竞价'), cssClass: 'title' }
      ],
      fields: { text: 'label', value: 'status' }
    }
  },
  {
    width: '80',
    field: 'version',
    headerText: i18n.t('版本')
  },
  {
    width: '100',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '100',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]

export const pageConfig = (url) => [
  {
    toolbar,
    gridId: permission.gridId['purchase']['businessConfig'],
    grid: {
      allowFiltering: true,
      allowSorting: false,
      columnData,
      asyncConfig: {
        url
      }
    }
  }
]
