<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :padding-top="true"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { pageConfig } from './config'
export default {
  data() {
    return {
      pageConfig: pageConfig(this.$API.businessConfig.getConfigListUrl)
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field == 'templateCode') {
        this.redirectConfigDetail(e.data)
      }
    },
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      console.log('use-handleClickToolBar', e, _selectGridRecords)
      if (
        _selectGridRecords.length <= 0 &&
        (e.toolbar.id == 'Delete' || e.toolbar.id == 'export_json')
      ) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id == 'Add') {
        this.handleAddConfig()
      } else if (e.toolbar.id == 'Delete') {
        this.handleBatchDelete(_selectGridRecords)
      } else if (e.toolbar.id === 'import_json') {
        this.handleImport(_selectGridRecords)
      } else if (e.toolbar.id === 'export_json') {
        this.handleExport(_selectGridRecords)
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      // if (e.tool.id == "edit") {
      //   //编辑操作
      //   this.handleEditStrategyConfig(e.data);
      // } else
      if (e.tool.id == 'Start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.templateCode], 1, e.data.version)
      } else if (e.tool.id == 'Stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.templateCode], 2, e.data.version)
      } else if (e.tool.id == 'delete') {
        this.handleDeleteConfig([e.data.templateCode])
      }
    },
    //批量启动操作
    handleBatchUpdateStart(_selectGridRecords) {
      //状态 0 草稿 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.templateCode)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.status === 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在‘启用’状态
        this.$toast({
          content: this.$t("选中数据中，存在'启用'状态的数据"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 1, _selectGridRecords[0]['version'])
      }
    },
    //批量停用操作
    handleBatchUpdateStop(_selectGridRecords) {
      //状态 0 草稿 1 启用 2 停用
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.templateCode)
      })
      let _disableStatusRecords = _selectGridRecords.filter((s) => {
        return s.status !== 1
      })
      if (_disableStatusRecords.length > 0) {
        //选中数据中，存在非‘启用’状态
        this.$toast({
          content: this.$t("只有启用状态可执行'停用'操作"),
          type: 'warning'
        })
        return
      } else {
        this.handleUpdateConfigStatus(_selectIds, 2, _selectGridRecords[0]['version'])
      }
    },
    //更新分组规则的状态(行内操作+批量操作)
    handleUpdateConfigStatus(ids, status, version) {
      //状态 0 草稿 1 启用 2 停用
      let _params = {
        status,
        templateCode: ids.join(','),
        version
      }
      let _statusMap = [this.$t('草稿'), this.$t('启用'), '停用']
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `确认更新状态为${_statusMap[status]}？`
        },
        success: () => {
          this.$API.businessConfig.enableConfig(_params).then(() => {
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    redirectConfigDetail(data) {
      localStorage.sourceModuleConfigInfo = JSON.stringify(data)
      this.$router.push({
        path: `/sourcing/structure-config-detail`,
        query: {
          templateCode: data.templateCode,
          key: this.$utils.randomString()
        }
      })
    },
    handleAddConfig() {
      // 业务字段的弹框
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/commom/settings/businessConfig/list/components/index" */ './components/addConfig.vue'
          ),
        data: {
          title: this.$t('新增配置')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    //批量删除操作
    handleBatchDelete(_selectGridRecords) {
      let _selectIds = []
      _selectGridRecords.map((item) => {
        _selectIds.push(item.templateCode)
      })
      this.handleDeleteConfig(_selectIds)
    },
    //删除规则
    handleDeleteConfig(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$API.businessConfig.deleteConfig(ids).then(() => {
            this.$toast({
              content: this.$t('删除成功'),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          })
        }
      })
    },
    //执行停用操作
    handleStopModel() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行停用操作?`)
        },
        success: () => {
          this.$API.businessConfig1.getBusinessConfigDetail({}).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('操作成功'),
                type: 'success'
              })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    // 导入-JSON
    handleImport() {
      this.$dialog({
        modal: () => import('./components/importDialog.vue'),
        data: {
          title: this.$t('导入JSON'),
          type: 'import'
        },
        success: (res) => {
          this.$toast({
            content: res.msg ? res.msg : this.$t('导入成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 导出-JSON
    async handleExport(list) {
      const templateCodeList = []
      list.map((item) => templateCodeList.push(item.templateCode))
      const res = await this.$API.businessConfig.businessCfgExportJson(templateCodeList)
      if (res.code === 200) {
        this.$dialog({
          modal: () => import('./components/importDialog.vue'),
          data: {
            title: this.$t('导出JSON(点击"确定"复制到剪贴板，原有内容将会被覆盖)'),
            type: 'export',
            data: JSON.stringify(res.data)
          },
          success: () => {
            this.handleCopyToClipboard(JSON.stringify(res.data))
            this.$toast({
              content: res.msg || this.$t('复制成功'),
              type: 'success'
            })
          }
        })
      }
    },
    // 复制到剪切板
    handleCopyToClipboard(str) {
      const el = document.createElement('textarea')
      el.value = str
      el.setAttribute('readonly', '')
      el.style.position = 'absolute'
      el.style.left = '-9999px'
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  /deep/.e-headercelldiv {
    padding: 0 0.6em 0 0.6em !important;
  }
}
/deep/.e-dialog {
  width: 900px !important;
  height: 600px !important;
}
</style>
