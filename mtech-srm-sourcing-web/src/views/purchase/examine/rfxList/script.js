import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      pageConfig: [
        {
          toolbar: [{ id: 'ExportRfx', icon: 'icon_solid_edit', title: this.$t('导出') }],
          useToolTemplate: false,
          fieldDefines: [],
          grid: {
            gridId: this.$permission.gridId.purchase.examine.rfxList,
            asyncConfig: {
              url: this.$API.examine.pageQueryBuilder,
              serializeList: (list) => {
                let now = Date.now()
                list.forEach((e) => {
                  if (e.responseBidEndTime <= now) {
                    e.responseBidEndTimeStatus = 0
                  } else if (e.responseBidEndTime > now) {
                    e.responseBidEndTimeStatus = 1
                  }
                })
                return list
              }
            },
            columnData: columnData.call(this),
            dataSource: [],
            allowFiltering: false,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'ExportRfx') {
        this.handleExport()
      }
    },
    handleExport() {
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          // {
          //   condition: 'and',
          //   field: 'rfx_item.rfxHeaderId',
          //   operator: 'equal',
          //   value: this.rfxId
          // }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.examine.exportQueryBuilder(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTitle({ data, field }) {
      if (field === 'rfxCode') {
        this.$router.push({
          name: 'examine-qualification',
          query: {
            rfxId: data.rfxId
          }
        })
      }
    }
  }
}

function columnData() {
  return [
    {
      field: 'rfxCode',
      headerText: this.$t('寻源单号'),
      cssClass: 'field-content',
      searchOptions: {
        renameField: 'eh.rfx_code'
      }
    },
    {
      field: 'rfxName',
      headerText: this.$t('寻源标题'),
      searchOptions: {
        renameField: 'rh.rfx_name'
      }
    },
    {
      field: 'businessTypeName',
      headerText: this.$t('业务类型'),
      searchOptions: {
        renameField: 'rh.business_type_name'
      }
    },
    {
      field: 'companyName',
      headerText: this.$t('公司'),
      searchOptions: {
        renameField: 'rh.company_name'
      }
    },
    {
      field: 'sourcingMode',
      headerText: this.$t('寻源方式'),
      valueConverter: {
        type: 'map',
        map: [
          {
            status: 'rfq',
            label: this.$t('询报价'),
            cssClass: 'title'
          },
          {
            status: 'direct_pricing',
            label: this.$t('直接定价'),
            cssClass: 'title'
          },
          {
            status: 'invite_bids',
            label: this.$t('招投标'),
            cssClass: 'title'
          },
          {
            status: 'bidding_price',
            label: this.$t('竞价'),
            cssClass: 'title'
          }
        ],
        fields: { text: 'label', value: 'status' }
      },
      searchOptions: {
        renameField: 'rh.sourcing_mode'
      }
    },
    {
      field: 'sourcingObj',
      headerText: this.$t('寻源对象'),
      searchOptions: {
        renameField: 'rh.sourcing_obj'
      }
    },
    {
      field: 'needEarnestMoney',
      headerText: this.$t('需要保证金'),
      valueConverter: {
        type: 'map',
        map: { 0: this.$t('否'), 1: this.$t('是') }
      },
      searchOptions: {
        renameField: 'eh.need_earnest_money'
      }
    },
    {
      field: 'responseBidEndTimeStatus',
      headerText: this.$t('审查状态'),
      ignore: true,
      valueConverter: {
        type: 'map',
        map: { 0: this.$t('审查结束'), 1: this.$t('审查中') }
      }
    }
  ]
}
