<template>
  <div class="full-height">
    <input type="file" accept=".xlsx" hidden ref="excel-uploader" />
    <a
      :href="`${prefixPath}/static/${$t('价格导入模板')}.xlsx`"
      ref="exportTemplate"
      class="hidden"
      target="_blank"
      :download="`${$t('价格导入模板')}.xlsx`"
    ></a>
    <mt-template-page :template-config="pageConfigOuter" @handleSelectTab="handleSelectTab">
      <mt-template-page
        ref="templateRef"
        slot="slot-0"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      />
      <mt-template-page
        ref="templateRef1"
        slot="slot-1"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfigCb"
        @handleClickToolBar="handleClickToolBarCb"
      />
      <mt-template-page
        ref="templateRef"
        slot="slot-2"
        :padding-top="true"
        :template-config="logisticsConfig"
      >
        <mt-template-page
          ref="templateRef"
          slot="slot-0"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(1, $t('海运'))"
        />
        <mt-template-page
          ref="templateRef"
          slot="slot-1"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(2, $t('空运'))"
        />
        <mt-template-page
          ref="templateRef"
          slot="slot-2"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(3, $t('铁路'))"
        />
        <mt-template-page
          ref="templateRef"
          slot="slot-3"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(4, $t('陆运'))"
        />
        <mt-template-page
          ref="templateRef"
          slot="slot-4"
          :hidden-tabs="true"
          :padding-top="true"
          :template-config="pageConfigLoFnBase(5, $t('拖车'))"
        />
      </mt-template-page>
      <mt-template-page
        ref="templateRef3"
        slot="slot-3"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfigSi"
        @handleClickCellTitle="handleClickCellTitleSi"
      />
      <mt-template-page
        ref="templateRef4"
        slot="slot-4"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfigMold"
      />
      <mt-template-page
        ref="templateRef5"
        slot="slot-5"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfigStructureAndArt('structure')"
        @handleClickToolBar="handleClickToolBar"
      />
      <mt-template-page
        ref="templateRef6"
        slot="slot-6"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfigStructureAndArt('art')"
        @handleClickToolBar="handleClickToolBar"
      />
      <mt-template-page
        ref="templateRef7"
        slot="slot-7"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfigItemMold"
        @handleClickToolBar="handleClickToolBar"
      />
      <!-- 外发阶梯 -->
      <mt-template-page
        ref="templateRef8"
        slot="slot-8"
        :hidden-tabs="true"
        :padding-top="true"
        :template-config="pageConfigWf"
        @handleClickToolBar="handleClickToolBar"
      />
    </mt-template-page>
  </div>
</template>

<script>
import {
  toolbar,
  toolbarCost,
  // toolbarLogic,
  toolbarCom,
  columnData,
  columnDataCb,
  listColumnData,
  moldColumnData,
  itemMoldColumnData,
  structureAndArtColumnData,
  // columnDataLogic,
  pageConfigOuter,
  logisticsConfig,
  pageConfigLoFn,
  pageConfigStructureAndArt,
  columnDataWf
} from './config'
import { permission } from '@/main.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      Valid: true,
      pageConfigOuter,
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar,
          gridId: 'af4e4625-3b8a-4588-8042-417ff49fe13b',
          grid: {
            allowFiltering: true,
            columnData: columnData,
            asyncConfig: {
              url: this.$API.priceService.getPriceRecords,
              params: {
                queryCode: 'batchCodeQuery'
              }
              // ignoreDefaultSearch: true
            }
          }
        }
      ],
      pageConfigCb: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: toolbarCost,
          gridId: this.$permission.gridId['purchase']['price']['factor'],
          grid: {
            allowFiltering: true,
            columnData: columnDataCb,
            asyncConfig: {
              url: this.$API.priceService.getCostRecords
            }
          }
        }
      ],
      pageConfigLoFn,
      logisticsConfig,
      pageConfigSi: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: [],
          gridId: permission.gridId['purchase']['seriesItem']['seriesItemPriceManagement']['list'],
          grid: {
            allowFiltering: true,
            columnData: listColumnData,
            asyncConfig: {
              url: this.$API.priceService.getPriceRecordsNew,
              params: {
                queryCode: 'batchCodeQuery'
              },
              defaultRules: [
                {
                  label: '',
                  field: 'priceType',
                  type: 'string',
                  operator: 'equal',
                  value: 7
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.siteList) {
                    item.companyName = item.siteList[0].companyName
                    item.siteName = item.siteList[0].siteName
                    item.purOrgName = item.siteList[0].purOrgName
                  }
                })
                return list
              }
            }
          }
        }
      ],
      pageConfigMold: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: [],
          gridId: '16d6f775-b8bb-474b-9b8e-fbed9187b761',
          grid: {
            allowFiltering: true,
            columnData: moldColumnData,
            asyncConfig: {
              url: this.$API.priceService.getPriceRecordsNew,
              params: {
                queryCode: 'batchCodeQuery'
              },
              defaultRules: [
                {
                  label: '',
                  field: 'priceType',
                  type: 'string',
                  operator: 'equal',
                  value: 8
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.siteList) {
                    item.companyName = item.siteList[0].companyName
                    item.siteName = item.siteList[0].siteName
                    item.purOrgName = item.siteList[0].purOrgName
                  }
                })
                return list
              }
            }
          }
        }
      ],
      pageConfigItemMold: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: toolbarCom,
          gridId: '73eea03b-cff2-4d2e-a330-f7f5c5d8e141',
          grid: {
            allowFiltering: true,
            columnData: itemMoldColumnData,
            asyncConfig: {
              url: this.$API.priceService.getItemMoldPriceRecords,
              params: {
                queryCode: 'batchCodeQuery'
              },
              defaultRules: [
                {
                  label: '',
                  field: 'priceType',
                  type: 'string',
                  operator: 'equal',
                  value: 9
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  if (item.siteList) {
                    item.companyName = item.siteList[0].companyName
                    item.siteName = item.siteList[0].siteName
                    item.purOrgName = item.siteList[0].purOrgName
                  }
                })
                return list
              }
            }
          }
        }
      ],
      pageConfigWf: [
        {
          useToolTemplate: false,
          toolbar: toolbarCom,
          gridId: '244ac4c0-0610-4020-b6d2-248e4762386e',
          grid: {
            allowFiltering: true,
            columnData: columnDataWf,
            asyncConfig: {
              url: this.$API.priceService.getPriceRecordsNew,
              params: {
                queryCode: 'batchCodeQuery'
              },
              defaultRules: [
                {
                  label: '',
                  field: 'priceType',
                  type: 'string',
                  operator: 'in',
                  value: [12, 13, 14]
                }
              ],
              serializeList: (list) => {
                list.forEach((item) => {
                  item.siteList?.length && (item.innerCounter = item.siteList[0].innerCounter)
                })
                return list
              }
            }
          }
        }
      ],
      buttons: [
        {
          click: this.btnConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      prefixPath: window.__POWERED_BY_QIANKUN__ ? '/sourcing' : '',
      tabInfo: [
        {
          tabIndex: 0,
          gridId: 'af4e4625-3b8a-4588-8042-417ff49fe13b',
          columns: columnData
        },
        {
          tabIndex: 5,
          gridId: '20f07d20-6ba8-4c3d-88ff-d8ba8818cdaf',
          columns: structureAndArtColumnData
        },
        {
          tabIndex: 6,
          gridId: '8192e1fe-bbd7-4303-943b-3f8456603fcc',
          columns: structureAndArtColumnData
        },
        {
          tabIndex: 7,
          gridId: '73eea03b-cff2-4d2e-a330-f7f5c5d8e141',
          columns: itemMoldColumnData
        },
        {
          tabIndex: 8,
          gridId: '244ac4c0-0610-4020-b6d2-248e4762386e',
          columns: columnDataWf
        }
      ],
      currentTabInfo: null
    }
  },
  mounted() {
    this.$refs['excel-uploader'].addEventListener('change', (event) => {
      let file = event.target.files[0]
      if (file) {
        const formData = new FormData()
        formData.append('file', file)
        this.$API.priceService.importExcel(formData).then((res) => {
          this.$toast({
            type: 'success',
            content: `导入成功${res.data.successCount}条，失败${res.data.failureCount}条`
          })
          this.$refs.templateRef.refreshCurrentGridData()
        })
      }
    })
    this.handleSelectTab(0)
  },
  methods: {
    handleSelectTab(e) {
      let _find = this.tabInfo.find((item) => item.tabIndex === e)
      this.currentTabInfo = _find ? _find : null
    },
    handleClickToolBar(ejsEventObject) {
      let _selectGridRecords = ejsEventObject.grid.getSelectedRecords()
      console.log('use-handleClickToolBar', ejsEventObject, _selectGridRecords)
      if (ejsEventObject.toolbar.id == 'Add') {
        this.handleAddPrice()
      } else if (ejsEventObject.toolbar.id == 'ImportEXCEL') {
        this.$refs['excel-uploader'].click()
      } else if (ejsEventObject.toolbar.id == 'ImportSAP') {
        this.importSPA(_selectGridRecords)
      } else if (ejsEventObject.toolbar.id == 'exportTemplate') {
        this.$refs['exportTemplate'].click()
      } else if (ejsEventObject.toolbar.id === 'export') {
        // 动态字段导出
        this.handleComExport(true)
      } else if (ejsEventObject.toolbar.id === 'ComExport') {
        // 动态字段导出
        this.handleComExport()
      } else if (ejsEventObject.toolbar.id === 'transfer') {
        if (!_selectGridRecords?.length) {
          this.$toast({
            content: this.$t('请先勾选数据'),
            type: 'warning'
          })
          return
        }
        // 定价转合同
        this.handleTransfer(_selectGridRecords)
      }
    },
    // 定价转合同
    handleTransfer(_selectGridRecords) {
      const codes = _selectGridRecords.map((item) => item.priceRecordCode)
      this.$API.priceService.addFromPricePv(codes).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.$router.push({
            path: '/purchase-pv/contract-detail',
            query: {
              type: 'edit',
              id: data.id,
              refreshId: Date.now()
            }
          })
        }
      })
    },
    //导出
    handleExport() {
      this.$store.commit('startLoading')
      // 先要点击“查询”按钮才能取得queryBuilderRules
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        queryCode: 'batchCodeQuery'
      } // 筛选条件
      this.$API.priceService
        .exportPriceRecord(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    //成本因子表格按钮-点击事件
    handleClickToolBarCb(ejsEventObject) {
      // let _selectGridRecords = ejsEventObject.grid.getSelectedRecords();
      if (ejsEventObject.toolbar.id == 'Add') {
        this.handleAddCbPrice()
      } else if (ejsEventObject.toolbar.id == 'ImportEXCEL') {
        this.$refs['excel-uploader'].click()
      }
    },
    //单元格icons点击事件
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'start') {
        //启用操作
        this.handleUpdateConfigStatus([e.data.id], 1)
      } else if (e.tool.id == 'stop') {
        //停用操作
        this.handleUpdateConfigStatus([e.data.id], 2)
      } else if (e.tool.id == 'check') {
        this.handleCheckPrice(e.data.stageList, e.data.taxRate)
      }
    },
    //动态列导出
    handleComExport(isItem) {
      if (!this.currentTabInfo) return
      let obj = JSON.parse(sessionStorage.getItem(this.currentTabInfo?.gridId))?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        this.currentTabInfo?.columns.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      const currentUsefulRef =
        this.$refs[`templateRef${this.currentTabInfo?.tabIndex || ''}`].getCurrentUsefulRef()
          .pluginRef
      const params = currentUsefulRef?.queryBuilderRules
      let { defaultRules } = currentUsefulRef?.asyncParams
      if (isItem) {
        // 物料价格库单独导出
        defaultRules = [
          {
            label: '',
            field: 'priceType',
            type: 'string',
            operator: 'in',
            value: [0, 1, 2, 3, 4]
          }
        ]
      }
      this.$API.priceService
        .exportDynamicPriceRecord({
          page: { current: 1, size: 10000 },
          ...params,
          defaultRules,
          includeColumnFiledNames: field,
          queryCode: 'batchCodeQuery'
        })
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    handleUpdateConfigStatus(ids, status) {
      //enableStatus状态 0 未启用 1 启用 2 禁用
      let _statusMap = [this.$t('草稿'), this.$t('启用'), this.$t('禁用')]
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: `${this.$t('确认更新状态为')}${_statusMap[status]}？`
        },
        success: () => {}
      })
    },
    //单元格标题点击操作
    handleClickCellTitleSi(e) {
      if (e.field == 'detail') {
        this.viewDetail(e.data)
      }
    },
    viewDetail(row) {
      this.$API.seriesItem
        .itemRangeQuery({ itemCode: row.itemCode, itemId: row.itemId })
        .then((res) => {
          this.$dialog({
            modal: () => import('./list/components/applyItemRanageDialog/index.vue'),
            data: {
              title: this.$t('适用物料范围'),
              suitItemDTOList: res.data.suitItemDTOList,
              seriesItemCode: row.itemCode,
              seriesItemId: row.itemId,
              seriesItemName: row.itemName,
              organizationId: row.siteList[0].siteId,
              siteCode: row.siteList[0].siteCode
            },
            success: () => {
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        })
    },
    handleClickCellTitle(ejsEventObject) {
      console.log('use-handleClickCellTitle', ejsEventObject)
      if (ejsEventObject.field === 'priceRecordCode') {
        this.$dialog({
          modal: () =>
            import(
              /* webpackChunkName: "router/purchase/price/list/components/addPrice" */ './components/details/index.vue'
            ),
          data: {
            title: this.$t('价格条款明细'),
            data: ejsEventObject.data
          }
        })
      }
    },
    handleAddPrice() {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/price/list/components/addPrice" */ './components/addPrice/index.vue'
          ),
        data: {
          title: this.$t('新增价格记录')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    handleAddCbPrice() {
      this.$dialog({
        modal: () => import('./components/addCbPrice/index'),
        data: {
          title: this.$t('新增成本因子')
        },
        success: () => {
          this.$refs.templateRef1.refreshCurrentGridData()
        }
      })
    },
    handleCheckPrice(stageList, taxRate) {
      this.$dialog({
        modal: () =>
          import(
            /* webpackChunkName: "router/purchase/price/list/princeDialog" */ './princeDialog.vue'
          ),
        data: {
          title: this.$t('阶梯价格'),
          stageList: stageList.map((item) => {
            item.taxRate = taxRate
            return item
          })
        },
        success: () => {
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    },
    pageConfigLoFnBase(i, str) {
      return pageConfigLoFn(i, str)
    },
    pageConfigStructureAndArt(type) {
      return pageConfigStructureAndArt(type)
    },
    btnConfirm() {
      this.$refs.dialogPrice.ejsRef.hide()
    },
    async importSPA(data) {
      if (data.length <= 0) {
        this.$toast({
          content: this.$t('请至少选择一条记录'),
          type: 'warning'
        })
        return
      }
      const ids = data.map((i) => i.id)
      const res = await this.$API.priceService.importSPA({ ids })
      this.$toast({ content: res.msg, type: 'success' })
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  height: 100%;
}
.hidden {
  display: none;
}
.title {
  position: fixed;
  top: 40%;
  left: 45%;
  font-size: 20px;
}
</style>
