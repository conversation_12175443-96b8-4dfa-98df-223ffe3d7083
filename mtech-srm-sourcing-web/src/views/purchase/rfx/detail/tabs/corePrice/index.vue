<template>
  <div class="full-height">
    <mt-loading v-if="loading" />
    <mt-template-page
      ref="templateRef"
      class="frozenColumns"
      :template-config="pageConfig"
      @actionBegin="actionBegin"
      @dataBound="dataBound"
      @handleClickToolBar="handleClickToolBar($event, 'parent', {}, this)"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { v1 as uuidv1 } from 'uuid'
import { cloneDeep, isEqual } from 'lodash'
import {
  toolbar,
  toolbarNoBatch,
  toolbarNoImport,
  noEditToolbar,
  subToolbar,
  editColumnBefore,
  editSettings,
  notAllowedEditSettings,
  editColumn,
  editFieldCacheMapGenerator,
  numberEditFields,
  priceFields,
  dateEditFields,
  booleanEditField,
  handleSelectChange
} from './config'
import { fmtSelect } from '@/utils/ej/dataGrid/formatter'
import { getValueByPath } from '@/utils/obj'
import { sliceArray } from '@/utils/arr'
import { utils } from '@mtech-common/utils'
import Decimal from 'decimal.js'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
import { createEditInstance } from '@/utils/ej/dataGrid'
import * as combination from './utils/combination'
import { download, getHeadersFileName } from '@/utils/utils'
const SYMBOL_PK = 'addId'
const rfxTransferStatus = sessionStorage.getItem('rfxTransferStatus')
const DEFAULTPARAM = {
  condition: '',
  page: {
    current: 1,
    size: 10
  },
  pageFlag: false
}
export default {
  name: 'PurchaseDetail',
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    moduleType: {
      type: Number,
      default: () => {}
    },
    ktFlag: {
      type: Number,
      default: () => {}
    }
  },

  data() {
    return {
      originData: [],
      ids: [],
      calculation: this.$route.query.calculation == 1 ? true : false,
      loading: false,
      siteNameListMap: {},
      pageConfig: [
        {
          useToolTemplate: false,
          activatedRefresh: false,
          toolbar:
            rfxTransferStatus == 44 || rfxTransferStatus == 46
              ? noEditToolbar
                  .splice(0, 2)
                  .concat([{ id: 'export', icon: 'icon_solid_Download ', title: this.$t('导出') }])
              : rfxTransferStatus == 45
              ? noEditToolbar
                  .splice(2)
                  .concat([{ id: 'export', icon: 'icon_solid_Download ', title: this.$t('导出') }])
              : [{ id: 'export', icon: 'icon_solid_Download ', title: this.$t('导出') }],
          fieldDefines: this.fieldDefines,
          grid: {
            allowFiltering: true,
            columnData: editColumnBefore,
            dataSource: [],
            allowPaging: false,
            actionComplete: this.actionComplete,
            queryCellInfo: this.customiseCell,
            recordDoubleClick: this.handleChildEndEdit,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ],
      rowsSelectedInfo: {},
      requiredCols: [], //必填字段，全局变量
      submitTableData: [], // 新增或修改的数据，最终要提交给后台的
      currencyNameData: null, //币种信息
      supplierNameData: null, //供应商信息
      siteNameList: null, //地点/工厂 信息
      // itemNameList: null, //物料信息
      skuNameList: null, //sku信息
      taxRateNameList: null, //taxRate信息
      receiveUserIdData: null, //人员信息
      unitNameList: null, //单位列表
      purUnitNameList: null, // 订单单位列表
      companyNameList: null, //公司列表
      purGroupList: null, //采购组列表
      deptNameList: null, //部门列表
      dictItems: [], // dict-item 接口统一维护字段
      busEvent: new Set(), // 事件收集
      oldList: [], //接口数据与编辑后数据进行比较
      requestUrls: {},
      downTemplateName: this.$t('采购明细模板'),
      downTemplateParams: {
        page: {
          current: 1,
          size: 10
        },
        rules: []
      },
      newRecordsTag: null,
      localTempRecord: {},
      isEdit: false,
      txCompanyList: ['2M01', '2S06']
    }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.loading = true
    sessionStorage.removeItem('siteParam')
    sessionStorage.removeItem('dieItemParam')
    await this.initDictItems().catch(() => {})
    await this.getDropDownData().catch(() => {})
    this.loading = false
    this.resetAsyncConfigParams('mounted')
    this.handleUnionColumns()
    this.$bus.$on('contentDialog', () => {
      if (!this.txCompanyList.includes(this.$route.query?.companyCode)) {
        this.$toast({
          content: this.$t('最小采购量需是最小包装数量的整数倍'),
          type: 'warning'
        })
      }
    })
  },
  beforeDestroy() {
    sessionStorage.removeItem('siteParam')
    sessionStorage.removeItem('dieItemParam')
    this.busEvent.forEach((event) => {
      this.$bus.$off(event)
    })
  },
  computed: {
    // 贸易条款
    tradeClauseNameData: function () {
      return this.dictItems.filter((e) => e.dictCode === 'TradeClause')
    },
    // 物流方式
    shippingMethodNameData: function () {
      return this.dictItems.filter((e) => e.dictCode === 'TransportMode')
    },
    // 直送地值集
    deliveryPlaceData: function () {
      return this.dictItems.filter((e) => e.dictCode === 'DELIVERY_PLACE')
    },
    sourceType() {
      return this.$route?.query?.source //rfq | invite_bids | bidding_price
    }
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    //单元格icons，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'editCost') {
        // this.handleFormulaDialog(e.data);
        if (e.data.id) {
          this.$router.push({
            path: 'purchase-cost',
            query: {
              rfxId: this.$route.query.rfxId,
              rfxItemId: e.data.id,
              costModelId: e.data.costModelId
            }
          })
        } else {
          this.$toast({
            content: this.$t('请保存当前数据后操作！'),
            type: 'warning'
          })
        }
      } else if (e.tool.id == 'calculation') {
        if (e.data.id) {
          this.$router.push({
            path: 'calculation-purchase-cost',
            query: {
              rfxId: this.$route.query.rfxId,
              rfxItemId: e.data.id
            }
          })
        }
      }
    },
    isRfqModding() {
      return this.$route?.query?.source === 'rfq'
    },
    // 初始化字典数据
    async initDictItems() {
      // 没有批量查询接口
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing) {
        args.cell.classList.add('bg-grey')
      }
    },
    // 获取下拉数据
    async getDropDownData() {
      if (
        this.currencyNameData &&
        this.supplierNameData &&
        this.siteNameList &&
        this.skuNameList &&
        this.taxRateNameList &&
        this.tradeClauseNameData &&
        this.shippingMethodNameData &&
        this.receiveUserIdData &&
        this.unitNameList &&
        this.purUnitNameList &&
        this.deptNameList &&
        this.companyNameList &&
        this.purGroupList &&
        this.deliveryPlaceData
      ) {
        this.handleUnionColumns()
        return
      }

      const tasks = []

      // 获取币种
      tasks.push(() =>
        this.$API.masterData.queryAllCurrency().then((res) => {
          this.currencyNameData = res.data || []
        })
      )

      //人员列表
      tasks.push(() =>
        this.$API.masterData.getUserPageList(DEFAULTPARAM).then((res) => {
          this.receiveUserIdData = res?.data?.records || []
        })
      )

      // // 推荐供应商
      // await this.$API.masterData.getSupplierList().then((res) => {
      //   this.supplierNameData = res.data || [];
      // });
      this.supplierNameData = []

      // 部门列表
      tasks.push(() =>
        this.$API.masterData
          .getDepartmentList({
            departmentName: ''
          })
          .then((res) => {
            this.deptNameList = res.data || []
          })
      )

      // 地点/工厂
      tasks.push(() =>
        this.$API.masterData
          .permissionSiteList({
            buOrgId: sessionStorage.getItem('selectOrgId'),
            companyId: sessionStorage.getItem('selectCompanyId')
          })
          .then((res) => {
            this.siteNameList = res?.data || []
            let siteNameListMap = {}
            for (let item of this.siteNameList) {
              item['siteName'] = item['orgName']
              item['siteCode'] = item['orgCode']
              item['organizationId'] = item['id']
              siteNameListMap[item.siteCode] = item.organizationId
            }
            this.siteNameListMap = siteNameListMap
          })
      )
      // // 物料信息 itemName
      // tasks.push(() =>
      //   this.$API.masterData.getItemList().then((res) => {
      //     //逻辑需求：(新逻辑：品类是品项带出来的，不能独立输入)，选择品项，带出品类信息
      //     res.data.forEach((e) => {
      //       if (e?.categoryResponse) {
      //         e.categoryName =
      //           e?.categoryResponse?.categoryName ?? this.$t("未维护");
      //         e.categoryCode =
      //           e?.categoryResponse?.categoryCode ?? this.$t("未维护");
      //         e.categoryId = e?.categoryResponse?.id ?? 0;
      //       } else {
      //         e.categoryName = this.$t("未维护");
      //         e.categoryCode = this.$t("未维护");
      //         e.categoryId = 0;
      //       }
      //     });
      //     this.itemNameList = res?.data || [];
      //   })
      // );
      // SKU信息 skuName
      tasks.push(() =>
        this.$API.masterData.getSKUList().then((res) => {
          this.skuNameList = res?.data?.records || []
        })
      )

      //税率信息
      tasks.push(() =>
        this.$API.masterData.queryAllTaxItem().then((res) => {
          this.taxRateNameList = res?.data || []
        })
      )

      //基本单位
      tasks.push(() =>
        this.$API.masterData.pagedQueryUnit().then((res) => {
          this.unitNameList = res?.data?.records || []
          this.purUnitNameList = res?.data?.records || [] // FIXME 采订单单位和基本单位的区别
        })
      )

      // 获取公司编码、公司名称  {companyCode、companyName、id}
      tasks.push(() =>
        this.$API.masterData.permissionCompanyList().then((res) => {
          this.companyNameList = res?.data || []
        })
      )

      // 获取采购组
      tasks.push(() =>
        this.$API.masterData
          .getbussinessGroup({
            groupTypeCode: 'BG001CG'
          })
          .then((res) => {
            this.purGroupList = res?.data || []
          })
      )

      for (const task of sliceArray(tasks, 5)) {
        await Promise.all(task.map((fn) => fn())).catch(() => {})
      }
    },
    // 组合列
    handleUnionColumns() {
      this.requiredCols = []
      let params = {
        currencyNameData: this.currencyNameData,
        supplierNameData: this.supplierNameData,
        siteNameList: this.siteNameList,
        // itemNameList: this.itemNameList,
        skuNameList: this.skuNameList,
        taxRateNameList: this.taxRateNameList,
        shippingMethodNameData: this.shippingMethodNameData,
        tradeClauseNameData: this.tradeClauseNameData,
        receiveUserIdData: this.receiveUserIdData,
        unitNameList: this.unitNameList, // 基本单位
        purUnitNameList: this.purUnitNameList, // 采购单位
        companyNameList: this.companyNameList,
        purGroupList: this.purGroupList,
        deptNameList: this.deptNameList,
        dictItems: this.dictItems,
        detailInfo: this.detailInfo,
        getCategoryList: this.$API.masterData.getCategoryList,
        categoryPagedQuery: this.$API.masterData.categoryPagedQuery,
        queryCompanyAndRel: this.$API.costModel.queryByCompanyAndRel,
        pagedQueryUnit: this.$API.masterData.pagedQueryUnit,
        getUserDetail: this.$API.iamService.getUserDetail,
        rfxId: this.$route.query.rfxId,
        submitTableData: () => this.submitTableData,
        sourcingType: this.detailInfo.sourcingType,
        source: this.$route?.query?.source,
        self: this,
        sourcingObjType: this.detailInfo.sourcingObjType,
        companyCode: this.detailInfo.companyCode,
        priceClassification: this.detailInfo.priceClassification,
        getPurGroup: this.$API.masterData.getPurGroup
      }

      this.editColumns = editColumn(params, {
        $on: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$on(event, fn)
        },
        $emit: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$emit(event, fn)
        }
      })
      if (Array.isArray(this.fieldDefines) && this.fieldDefines.length) {
        let _columnData = this.defineGridColumns(this.fieldDefines)
        editFieldCacheMapGenerator(_columnData, editSettings)
        this.defineGridEditStatus()
        this.$set(this.pageConfig[0].grid, 'columnData', _columnData)
      }
      if (Array.isArray(this.childFields) && this.childFields.length) {
        let _columnData = this.defineGridColumns(this.childFields)
        if (combination.isCombination(this.detailInfo)) {
          _columnData = combination.defineGridColumnsAfter(_columnData)
        }
        editFieldCacheMapGenerator(_columnData, editSettings)
        let _this = this
        let allowEditing = this.detailInfo.transferStatus < 1
        this.$set(this.pageConfig[0].grid, 'detailTemplate', function () {
          return {
            template: Vue.component('detailTemplate', {
              template: `<div style="padding:10px 0;">
                          <mt-template-page
                            ref="childRef"
                            :template-config="childTable"
                            @actionBegin="subActionBegin"
                            @handleClickToolBar="subClickToolBar"
                          ></mt-template-page>
                        </div>`,
              data: function () {
                return {
                  data: {},
                  childTable: [
                    {
                      useBaseConfig: false,
                      activatedRefresh: false,
                      toolbar: allowEditing ? [subToolbar] : [],
                      grid: {
                        height: 'auto',
                        allowPaging: false,
                        page: {
                          current: 1,
                          size: 1000
                        },
                        allowEditing,
                        editSettings,
                        dataSource: [],
                        columnData: _columnData,
                        queryCellInfo: _this.customiseCell,
                        recordDoubleClick: this.subDoubleClick,
                        class: 'pe-edit-grid custom-toolbar-grid'
                      }
                    }
                  ]
                }
              },
              created() {
                this.childTable[0].grid.actionComplete = (arg) => _this.actionComplete(arg, this)
              },
              mounted() {
                let data = this.data
                console.log('detail-table', data)
                setTimeout(() => {
                  this.childTable[0].grid.dataSource = data?.childItems
                }, 0)
                this.$bus.$on(`subPurchaseGridEndEdit-${data.id}`, () => {
                  let _childRef = this?.$refs?.childRef
                  let _current = _childRef?.getCurrentTabRef()
                  if (_current?.grid) {
                    _current?.grid.endEdit()
                  }
                })
                this.$bus.$on(`subClearRowSelection-${data.id}`, () => {
                  let _childRef = this?.$refs?.childRef
                  let _current = _childRef?.getCurrentTabRef()
                  if (_current?.grid) {
                    _current?.grid.clearRowSelection()
                  }
                })
                combination.detailTemplateMounted({
                  pVm: _this,
                  sVm: this,
                  SYMBOL_PK
                })
              },
              beforeDestroy() {
                combination.detailTemplateBeforeDestroy(this)
                this.$bus.$off(`subPurchaseGridEndEdit-${this.data.id}`)
                this.$bus.$off(`subClearRowSelection-${this.data.id}`)
              },
              methods: {
                subClickToolBar(e) {
                  _this.handleClickToolBar(e, 'subItems', this.data, this)
                },
                subActionBegin(e) {
                  _this.actionBegin(e)
                },
                subDoubleClick() {
                  _this.handleParentEndEdit(this.data.id)
                }
              }
            })
          }
        })
      }
    },
    defineGridColumns(fieldDefines) {
      let _columnData = cloneDeep(editColumnBefore)
      let _originColumns = cloneDeep(fieldDefines)
      // _originColumns = this.handleColumnData(_originColumns)
      let topInfoFormData = this.$parent.$refs.topInfoRef.formData
      if (Array.isArray(_originColumns) && _originColumns.length) {
        _originColumns.forEach((col) => {
          let _field = { field: col.fieldCode }
          if (col.tableName == 'rfx_item_ext' && col.fieldCode != 'drawingUrl') {
            _field = {
              field: `itemExtMap.${col.fieldCode}`
            }
          } else if (col.tableName.trim() === 'mt_supplier_rfx_item_ext') {
            _field = {
              field: `itemExtMap.${col.fieldCode}`
            }
          } else if (col.tableName == 'rfx_item_die') {
            _field = {
              field: `itemDieResponse.${col.fieldCode}`
            }
          } else if (col.tableName == 'rfx_item_logistics') {
            _field = {
              field: `itemLogisticsResponse.${col.fieldCode}`
            }
          }
          var _one = {
            ...col,
            ..._field,
            // field: col.fieldCode,
            headerText: this.$t(col.fieldName),
            width: '150'
          }
          // 如果是必填 0-非必填；1-必填；2-无需配置
          if (_one.required == 1) {
            this.requiredCols.push({
              field: _one.field,
              headerText: this.$t(_one.fieldName)
            })
            //设置必填-表头
            _one.headerTemplate = () => {
              return {
                template: Vue.component('requiredCell', {
                  template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                  data() {
                    return {
                      data: {},
                      fieldName: ''
                    }
                  },
                  mounted() {
                    this.fieldName = _one.fieldName
                  }
                })
              }
            }
          }
          // 如果该列是下拉或者其他类型的复杂情况
          let editFind = this.editColumns.find((editCol) => _one.field == editCol.field)
          let _allowEditing = _one?.allowEditing === false ? false : true
          if (editFind) {
            _one = {
              ..._one,
              ...editFind,
              width: editFind.width || '150'
            }
            _allowEditing = editFind.allowEditing === false ? false : true
          } else if (numberEditFields.indexOf(_one.fieldCode) > -1) {
            if (_one.fieldCode == 'requireQuantity') {
              //需求数量展示整数
              _one = {
                ..._one,
                editConfig: {
                  type: 'number',
                  min: 1,
                  validateDecimalOnType: true,
                  decimals: 1,
                  format: 'N'
                }
              }
            } else if (priceFields.indexOf(_one.fieldCode) > -1) {
              _one = {
                ..._one,
                editConfig: {
                  ...PRICE_EDIT_CONFIG,
                  type: 'number'
                }
              }
            } else {
              //编辑状态，数字类型
              _one = {
                ..._one,
                editConfig: {
                  type: 'number',
                  min: 1
                }
              }
            }
          } else if (dateEditFields.indexOf(_one.fieldCode) > -1) {
            //编辑状态，日期类型
            _one = {
              ..._one,
              editType: 'datepickeredit',
              type: 'date',
              format: 'yyyy-MM-dd',
              editConfig: {
                type: 'date'
              }
            }
          } else if (booleanEditField.indexOf(_one.fieldCode) > -1) {
            //编辑状态，布尔类型
            _one = {
              formatter: fmtSelect,
              ..._one,
              editConfig: {
                type: 'select',
                fields: { value: 'value', text: 'text' },
                dataSource: [
                  { text: this.$t('是'), value: 1 },
                  { text: this.$t('否'), value: 0 }
                ],
                placeholder: this.$t('选择'),
                callback: handleSelectChange
              }
            }
          }

          if (
            topInfoFormData[_one.field] != undefined &&
            topInfoFormData[_one.field] != '' &&
            topInfoFormData[_one.field] != null
          ) {
            _allowEditing = false
          }
          _one.allowEditing = _allowEditing
          if (_one.fieldCode == 'conversionRate') {
            const editInstance = createEditInstance()
            _one.edit = editInstance.create({
              getEditConfig: () => ({
                type: 'number',
                max: ************,
                min: 0,
                precision: 2
              })
            })
          }
          if (
            this.detailInfo.transferStatus == 44 &&
            _one.field !== 'itemExtMap.openBidReviewComment'
          ) {
            _one.allowEditing = false
            delete _one.edit
            delete _one.editConfig
          }
          if (
            this.detailInfo.transferStatus == 45 &&
            _one.field !== 'itemExtMap.accountingReviewComment'
          ) {
            _one.allowEditing = false
            delete _one.edit
            delete _one.editConfig
          }
          if (
            this.detailInfo.transferStatus == 46 &&
            _one.field !== 'itemExtMap.targetReviewComment' &&
            _one.field !== 'itemExtMap.purchaseTargetPrice'
          ) {
            _one.allowEditing = false
            delete _one.edit
            delete _one.editConfig
          }
          if (
            this.detailInfo.transferStatus == 46 &&
            _one.field == 'itemExtMap.purchaseTargetPrice'
          ) {
            _one.editConfig.precision = 5
          }
          if (_one.field == 'priceUnitName' && this.ktFlag == 1) {
            _one.allowEditing = false
            delete _one.edit
          }
          if (_one.field == 'itemExtMap.deliveryPlace') {
            delete _one.formatter
          }
          _columnData.push(_one)
        })
      }
      //系列物料临时物料编码不可编辑
      if (this.detailInfo.sourcingObjType == 'serial_item') {
        _columnData.forEach((e) => {
          if (e.field == 'temporaryItemCode') {
            e.allowEditing = false
          }
        })
      }
      return _columnData
    },
    //行内进入事件
    actionBegin(args) {
      // 新增时赋初始值
      if (args.requestType === 'save' && args.action == 'add') {
        let tempRecord = cloneDeep(this.detailInfo)
        if (this.detailInfo.siteId) {
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: this.detailInfo.siteId,
              siteCode: this.detailInfo.siteCode
            })
          )
        }
        tempRecord.id = undefined
        if (tempRecord.sourcingObjType) {
          Object.assign(tempRecord, {
            itemLogisticsResponse: {
              transportType: this.detailInfo.sourcingObjType
            },
            itemExtMap: {
              referChannel: 0
            },
            itemDieResponse: {
              addUp: 1 //累计至本次  默认'是'
            },
            rfxItemKey: uuidv1(),
            priceUnitName: this.ktFlag == 1 ? '1' : '1000'
          })
        }
        if (this.detailInfo.sourcingObjType === 'combination') {
          tempRecord.childItems = []
        }
        tempRecord[SYMBOL_PK] = uuidv1()
        args.rowData = {
          ...args.rowData,
          ...tempRecord,
          ...this.localTempRecord
        }
        args.data = { ...args.rowData, ...tempRecord, ...this.localTempRecord }
        // args.data.addId = args.data.addId || uuidv1();
      } else if (args.requestType === 'beginEdit') {
        this.defineRowSiteInfo(args.rowData)
      }
    },
    //grid data-bound
    dataBound() {
      if (Array.isArray(this.childFields) && this.childFields.length) {
        //数据加载后，如果是父子结构，展开
        let _current = this.$refs.templateRef.getCurrentTabRef()
        _current?.grid.detailExpandAll()
      }
    },
    //行内操作，获取当前行‘工厂信息’
    defineRowSiteInfo(row) {
      const defineSiteInfo = (rowData) => {
        if (rowData.siteCode != undefined && rowData.siteCode != '') {
          let organizationId = this.siteNameList.filter(
            (detail) => detail.siteCode == rowData.siteCode
          )[0].organizationId
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: organizationId,
              siteCode: rowData.siteCode
            })
          )
        } else {
          sessionStorage.removeItem('siteParam')
        }
      }

      if (row?.parentRfxItemKey) {
        //子层结构  获取工厂信息
        let _parent = this.submitTableData.find((item) => row.parentRfxItemKey == item.rfxItemKey)
        defineSiteInfo(_parent)
      } else {
        //父层结构  获取工厂信息
        defineSiteInfo(row)
      }
    },
    //行内操作，获取当前行‘物料信息’
    defineRowItemInfo(row) {
      const defineItemInfo = (rowData) => {
        if (rowData.itemCode != undefined && rowData.itemCode != '') {
          sessionStorage.setItem(
            'dieItemParam',
            JSON.stringify({
              itemCode: rowData?.itemCode
            })
          )
        } else {
          sessionStorage.removeItem('dieItemParam')
        }
      }

      if (row?.parentRfxItemKey) {
        //子层结构  获取父级物料信息
        let _parent = this.submitTableData.find((item) => row.parentRfxItemKey == item.rfxItemKey)
        defineItemInfo(_parent)
      } else {
        //父层结构  获取物料信息
        defineItemInfo(row)
      }
    },

    actionComplete(args, vm) {
      if (!args.action) {
        if (args.requestType == 'beginEdit') {
          this.defineRowSiteInfo(args.rowData)
          this.defineRowItemInfo(args.rowData)
        } else if (args.requestType == 'save') {
          sessionStorage.removeItem('siteParam')
          sessionStorage.removeItem('dieItemParam')
        }
        return
      }
      if (args.requestType == 'save') {
        //数据存储
        if (args.action !== 'add') {
          //编辑操作
          if (!isEqual(args.data, args.previousData)) {
            this.intergrateEditData(args.data, vm) // 整合编辑数据
          }
        }
        this.isEdit = false
      }
    },
    // 整合编辑数据，更新到 提交数据里
    intergrateEditData(rowData, vm) {
      combination.intergrateEditDataBefore(rowData, vm)
      let _name = rowData.id ? 'id' : 'addId'
      let _index = this.submitTableData.findIndex((item) => rowData[_name] == item[_name])
      this.submitTableData.forEach((e, index) => {
        if (e.itemCode == rowData.itemCode && index != _index) {
          if (this.detailInfo.transferStatus == 44) {
            e.itemExtMap.openBidReviewComment = rowData.itemExtMap.openBidReviewComment
          } else if (this.detailInfo.transferStatus == 45) {
            e.itemExtMap.accountingReviewComment = rowData.itemExtMap.accountingReviewComment
          } else if (this.detailInfo.transferStatus == 46) {
            e.itemExtMap.targetReviewComment = rowData.itemExtMap.targetReviewComment
            e.itemExtMap.purchaseTargetPrice = rowData.itemExtMap.purchaseTargetPrice
          }
          e.reviewPriceFileList = rowData.reviewPriceFileList
        }
      })
      if (_index > -1) {
        // this.$set(this.submitTableData, _index, rowData)
        this.submitTableData[_index] = rowData
      } else {
        this.submitTableData.push(rowData)
      }
      this.pageConfig[0].grid.dataSource = cloneDeep(this.submitTableData)
      console.error('intergrateEditData:', this.submitTableData)
    },
    // 删除事件
    handleDeleteRow(args) {
      args.data.forEach((item) => {
        // 处理 submitTableData
        let findIndex = this.submitTableData.findIndex((i) => i.addId == item.addId)
        if (findIndex >= 0) {
          this.submitTableData.splice(findIndex, 1)
        }
      })
    },
    serializeGridList(list) {
      let _list = []
      list.forEach((e) => {
        e.addId = e.id
        e.drawing = e.fileList ? JSON.stringify(e.fileList) : null //单独处理附件字段
        e.reviewPriceFileList = e.reviewPriceFileList ? JSON.stringify(e.reviewPriceFileList) : null //单独处理附件字段
        e.supplierDrawing = e.supplierFileList ? JSON.stringify(e.supplierFileList) : null //单独处理"供应商附件"字段
        e.stepQuoteName =
          e.itemStageList && e.itemStageList.length > 0 ? JSON.stringify(e.itemStageList) : null //单独处理阶梯报价
        // 接口没有返回税率值, 需要前端匹配
        if (e.taxRateCode && !e.taxRateValue) {
          const taxRow = this.taxRateNameList.find((tax) => tax.taxItemCode === e.taxRateCode)
          taxRow && (e.taxRateValue = taxRow.taxRate)
        }
        // 判断金额型和比例型
        if (e.itemExtMap && e.itemExtMap.minQuoteRangeType == 1) {
          e.itemExtMap.minQuoteRange = new Decimal(e.itemExtMap.minQuoteRange)
            .mul(new Decimal(100))
            .toNumber()
        }

        _list.push(e)
        if (Array.isArray(e?.childItems)) {
          _list = _list.concat(this.serializeGridList(e.childItems))
        }
      })
      this.originData = cloneDeep(_list)
      return _list
    },
    //列表参数重新赋值
    resetAsyncConfigParams(type, rules) {
      if (type === 'resetDataByLocal') {
        this.$API.rfxRequireDetail
          .queryBuilder4ReviewPrice({
            page: { current: 1, size: 1000 },
            defaultRules: [
              {
                field: 'rfx_item.rfxHeaderId',
                operator: 'equal',
                type: 'string',
                value: this.$route.query.rfxId || ''
              }
            ]
          })
          .then((res) => {
            if (res?.data?.records) {
              this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
              this.submitTableData = this.serializeGridList(res.data.records)
              this.oldList = utils.cloneDeep(res.data.records)
            } else {
              this.$set(this.pageConfig[0].grid, 'dataSource', [])
            }
          })
      } else if (type === 'mounted') {
        this.$API.rfxRequireDetail
          .queryBuilder4ReviewPrice({
            page: { current: 1, size: 1000 },
            defaultRules: [
              {
                field: 'rfx_item.rfxHeaderId',
                operator: 'equal',
                type: 'string',
                value: this.$route.query.rfxId || ''
              }
            ]
          })
          .then((res) => {
            if (res?.data?.records) {
              this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
              this.submitTableData = this.serializeGridList(res.data.records)
              this.oldList = utils.cloneDeep(res.data.records)
            } else {
              this.$set(this.pageConfig[0].grid, 'dataSource', [])
            }
          })
      } else if (type === 'refreshDataByLocal') {
        this.$API.rfxRequireDetail
          .queryBuilder4ReviewPrice({
            page: { current: 1, size: 1000 },
            defaultRules: [
              {
                field: 'rfx_item.rfxHeaderId',
                operator: 'equal',
                type: 'string',
                value: this.$route.query.rfxId || ''
              }
            ]
          })
          .then((res) => {
            if (res?.data?.records) {
              this.submitTableData = res.data.records
              this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
              this.submitTableData = this.serializeGridList(res.data.records)
              this.oldList = utils.cloneDeep(res.data.records)
            } else {
              this.$set(this.pageConfig[0].grid, 'dataSource', [])
              this.submitTableData = []
            }
          })
      } else if (type == 'filterDataByLocal') {
        this.$API.rfxRequireDetail
          .queryBuilder4ReviewPrice({
            condition: 'and',
            rules: rules,
            page: { current: 1, size: 1000 },
            defaultRules: [
              {
                field: 'rfx_item.rfxHeaderId',
                operator: 'equal',
                type: 'string',
                value: this.$route.query.rfxId || ''
              }
            ]
          })
          .then((res) => {
            if (res?.data?.records) {
              this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
              this.submitTableData = this.serializeGridList(res.data.records)
              this.oldList = utils.cloneDeep(res.data.records)
            } else {
              this.$set(this.pageConfig[0].grid, 'dataSource', [])
            }
          })
      }
    },

    defineGridEditStatus() {
      if (
        this.detailInfo.transferStatus < 1 &&
        // 招投标、竞价: OA审批状态为审核中、审核通过 禁止编辑
        !(
          ['invite_bids', 'bidding_price'].includes(this.$route.query.source) &&
          [1, 2].includes(Number(this.detailInfo.approveStatus))
        )
      ) {
        this.$set(this.pageConfig[0].grid, 'allowEditing', true)
        this.$set(this.pageConfig[0].grid, 'editSettings', editSettings)

        if (Array.isArray(this.childFields) && this.childFields.length) {
          // 如果存在子层结构  隐藏导入操作
          this.$set(this.pageConfig[0], 'toolbar', combination.toolbar(this, toolbarNoImport))
        } else {
          this.$set(
            this.pageConfig[0],
            'toolbar',
            this.sourceType === 'rfq'
              ? toolbar(this.detailInfo.sourcingObjType == 'cost_factor')
              : this.sourceType === 'invite_bids' || this.sourceType === 'bidding_price'
              ? toolbarNoBatch()
              : toolbarNoImport
          )
        }
      } else {
        this.$set(this.pageConfig[0].grid, 'allowEditing', false)
        if (rfxTransferStatus >= 44 && rfxTransferStatus <= 46) {
          //核价环节
          // 毛正文待补充
          this.$set(this.pageConfig[0].grid, 'editSettings', editSettings)
        } else {
          this.$set(this.pageConfig[0].grid, 'editSettings', notAllowedEditSettings)
        }
      }
      if (this.calculation) {
        console.log('this.calculation', this.pageConfig[0].grid)
        this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', false)
        this.$set(this.pageConfig[0], 'toolbar', [])
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
      this.handleChildEndEdit()
    },
    handleParentEndEdit(id = null) {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      this.handleChildEndEdit(id)
    },
    handleChildEndEdit(id = null) {
      this.oldList?.forEach((data) => {
        if (data.id !== id) {
          this.$bus.$emit(`subPurchaseGridEndEdit-${data.id}`)
        }
      })
    },
    setNewRecordsTag(tag = 'parent', data) {
      this.newRecordsTag = {
        tag,
        data
      }
    },
    resetNewRecordsTag() {
      this.newRecordsTag = {
        tag: null,
        data: null
      }
    },
    async handleClickToolBar(e) {
      if (e.toolbar.id == 'resetDataByLocal') {
        this.resetAsyncConfigParams('resetDataByLocal')
      }
      if (e.toolbar.id == 'refreshDataByLocal') {
        this.resetAsyncConfigParams('refreshDataByLocal')
      }
      if (e.toolbar.id == 'filterDataByLocal') {
        this.resetAsyncConfigParams('filterDataByLocal', e.rules.rules)
      }
      if (e.toolbar.id == 'export') {
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules,
          defaultRules: [
            {
              condition: 'and',
              field: 'rfx_item.rfxHeaderId',
              operator: 'equal',
              value: this.$route.query.rfxId
            }
          ].concat(queryBuilderRules.rules || [])
        } // 筛选条件
        this.$API.rfxRequireDetail.exportCorePrice(params).then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      } else if (e.toolbar.id == 'save') {
        this.endEdit()
        setTimeout(() => {
          this.handleSaveDetails()
        }, 200)
        return
      } else if (e.toolbar.id == 'upload') {
        if (this.detailInfo.sourcingMode == 'rfq') {
          this.handleUpload()
        } else {
          this.handleUploadTwo()
        }
      } else if (e.toolbar.id == 'batchAdd') {
        if (this.detailInfo.siteCode) {
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: this.detailInfo.siteId,
              siteCode: this.detailInfo.siteCode
            })
          )
          this.$dialog({
            modal: () =>
              import('@/components/NormalEdit/checkSelectItemCode/components/selectGrid'),
            data: {
              title: this.$t('选择物料'),
              rfxId: this.$route.query.rfxId,
              siteParam: JSON.parse(sessionStorage.getItem('siteParam')),
              sourcingType: this.detailInfo.sourcingType,
              multiple: true,
              source: this.$route?.query?.source,
              sourcingObjType: this.detailInfo.sourcingObjType,
              priceClassification: this.detailInfo.priceClassification
            },
            success: (data) => {
              const grid = this.$refs.templateRef.getCurrentTabRef().grid
              let dataSource = grid.$options.propsData.dataSource
              let existItemCodeArr = []
              for (let item of dataSource) {
                if (item.itemCode) {
                  existItemCodeArr.push(item.itemCode)
                }
              }
              for (let item of data) {
                if (existItemCodeArr.indexOf(item.itemCode) != -1) {
                  this.$toast({
                    content: this.$t('物料"' + item.itemName + '"在当前单据已存在,不能重复添加'),
                    type: 'warning'
                  })
                  continue
                }
                const row = {}
                if (this.detailInfo.sourcingObjType) {
                  Object.assign(row, {
                    itemLogisticsResponse: {
                      transportType: this.detailInfo.sourcingObjType
                    }
                  })
                }
                let tempRecord = cloneDeep(this.detailInfo)
                tempRecord.id = undefined
                Object.assign(row, tempRecord)
                row.itemId = item.id
                row.itemCode = item.itemCode
                row.itemName = item.itemName
                row.spec = item.itemDescription
                row.material = item.materialTypeName
                row.unitId = item.baseMeasureUnitId
                row.unitCode = item.baseMeasureUnitCode
                row.unitName = item.baseMeasureUnitName
                row.categoryId = item.categoryId
                row.categoryCode = item.categoryCode
                row.categoryName = item.categoryName
                row.priceUnitName = item.purchaseUnitName
                row.purUnitId = item.purUnitId
                row.purUnitCode = item.purUnitCode
                row.purUnitName = item.purUnitName
                this.localTempRecord = row
                e.grid.addRecord(row)
              }
              return
            }
          })
        } else {
          this.$toast({
            content: this.$t('单据未配置工厂'),
            type: 'warning'
          })
        }
        return
      } else if (e.toolbar.id == 'saveReviewPrice') {
        this.handleSaveReviewPrice()
      } else if (e.toolbar.id == 'submitReviewPrice') {
        this.handleSubmitReviewPrice()
      }
    },
    // 重置表格勾选
    clearRowSelection() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.clearRowSelection()
    },
    hasColumn(field) {
      return this.pageConfig[0].grid.columnData.find((e) => e.field === field)
    },
    handleSaveReviewPrice() {
      // this.detailInfo.id
      // let _submitTableData = cloneDeep(this.submitTableData)
      console.log('handleSaveReviewPrice')
      let validateField = ''
      let validateMsg = ''
      //提交核价之前需要填写核价意见
      if (44 == this.detailInfo.transferStatus) {
        //开标核价
        validateField = 'openBidReviewComment'
        validateMsg = this.$t('核价意见不能为空！')
      } else if (45 == this.detailInfo.transferStatus) {
        //财务核价
        validateField = 'accountingReviewComment'
        validateMsg = this.$t('财务核价意见不能为空！')
      } else if (46 == this.detailInfo.transferStatus) {
        //核价目标价
        validateField = 'targetReviewComment'
        validateMsg = this.$t('核价目标价意见不能为空！')
      }

      let _submitTableData = cloneDeep(this.submitTableData)
      console.log(_submitTableData)
      let reviewPriceReq = {
        rfxId: this.detailInfo.id,
        transferStatus: this.detailInfo.transferStatus
      }
      let reviewPriceCommentList = []
      _submitTableData.forEach((row, index) => {
        console.log(row['itemExtMap'][validateField], 2000)
        if (
          reviewPriceCommentList.length == 0 ||
          !reviewPriceCommentList.map((e) => e.rfxItemId).includes(row['rfxItemId'])
        ) {
          if (row['itemExtMap'][validateField] == null || row['itemExtMap'][validateField] == '') {
            //this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`)
            this.$toast({
              content: this.$t(`请填写第${Number(index + 1)}行的${validateMsg}`),
              type: 'error'
            })
            throw new Error(this.$t(`请填写第${Number(index + 1)}行的${validateMsg}`))
          }

          let obj = {
            rfxItemId: row['rfxItemId'],
            reviewCommentFileList: row['reviewPriceFileList']
              ? JSON.parse(row['reviewPriceFileList'])
              : null
          }

          if (46 == this.detailInfo.transferStatus) {
            if (
              row['itemExtMap']['purchaseTargetPrice'] == null ||
              row['itemExtMap']['purchaseTargetPrice'] == ''
            ) {
              this.$toast({
                content: this.$t(`请填写第${Number(index + 1)}行的采购目标价`),
                type: 'error'
              })
              throw new Error(this.$t(`请填写第${Number(index + 1)}行的采购目标价`))
            } else {
              obj.purchaseTargetPrice = row['itemExtMap']['purchaseTargetPrice']
            }
          }

          obj[validateField] = row['itemExtMap'][validateField]
          reviewPriceCommentList.push(obj)
        }
      })

      reviewPriceReq.reviewPriceItemList = reviewPriceCommentList
      console.log(reviewPriceReq, 1000)
      this.$API.rfxRequireDetail.saveReviewPriceApi(reviewPriceReq).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
        this.ids = []
      })
    },
    handleSubmitReviewPrice() {
      console.log('handleSubmitReviewPrice')
      let validateField = ''
      let validateMsg = ''
      if (!isEqual(this.originData, this.submitTableData)) {
        this.$toast({
          content: this.$t(`表格存在未保存的变更，请先保存后再提交！`),
          type: 'error'
        })
        return
      }
      //提交核价之前需要填写核价意见
      if (44 == this.detailInfo.transferStatus) {
        //开标核价
        validateField = 'openBidReviewComment'
        validateMsg = this.$t('核价意见不能为空！')
      } else if (45 == this.detailInfo.transferStatus) {
        //财务核价
        validateField = 'accountingReviewComment'
        validateMsg = this.$t('财务核价意见不能为空！')
      } else if (46 == this.detailInfo.transferStatus) {
        //核价目标价
        validateField = 'targetReviewComment'
        validateMsg = this.$t('核价目标价意见不能为空！')
      }

      let _submitTableData = cloneDeep(this.submitTableData)
      console.log(_submitTableData)
      _submitTableData.forEach((row, index) => {
        if (row['itemExtMap'][validateField] == null || row['itemExtMap'][validateField] == '') {
          this.$toast({
            content: this.$t(`请填写第${Number(index + 1)}行的${validateMsg}`),
            type: 'error'
          })
          throw new Error(this.$t(`请填写第${Number(index + 1)}行的${validateMsg}`))
        }
      })

      //提交核价
      this.$API.rfxRequireDetail
        .submitReviewPriceApi({
          transferStatus: this.detailInfo.transferStatus,
          rfxId: this.detailInfo.id
        })
        .then(() => {
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
          this.$router.push({
            path: `/sourcing/bid-hall`
          })
        })
    },
    handleSaveDetails() {
      if (combination.handleSaveDetailsBefore(this)) {
        return
      }
      let _submitTableData = cloneDeep(this.submitTableData)
      // 1.3 需求明细 做校验
      _submitTableData.forEach((row, index) => {
        // 校验------------------------
        // 校验1. 必填--提交才校验必填
        this.requiredCols.forEach((rc) => {
          if (rc.field.indexOf('.') > -1) {
            let _sp = rc.field.split('.')
            if (!row[_sp[0]] || (!row[_sp[0]][_sp[1]] && row[_sp[0]][_sp[1]] != 0)) {
              this.$toast({
                content: this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`),
                type: 'error'
              })
              throw new Error(this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`))
            }
          } else {
            if (!row[rc.field] && row[rc.field] != 0) {
              this.$toast({
                content: this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`),
                type: 'error'
              })
              throw new Error(this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`))
            }
          }
        })
      })
      for (let i = 0; i < _submitTableData.length; i++) {
        const row = _submitTableData[i]
        //价格下限
        const minQuotePercent = getValueByPath(row, 'itemExtMap.minQuotePercent')
        //价格上限
        const maxQuotePercent = getValueByPath(row, 'itemExtMap.maxQuotePercent')
        // 起价
        const startingPrice = getValueByPath(row, 'itemExtMap.startingPrice')
        const isNullOrUndefined = (value) => typeof value === 'undefined' || value === null
        if (
          this.hasColumn('itemExtMap.minQuotePercent') &&
          !isNullOrUndefined(minQuotePercent) &&
          minQuotePercent > 0 &&
          this.hasColumn('itemExtMap.maxQuotePercent') &&
          !isNullOrUndefined(maxQuotePercent) &&
          maxQuotePercent > 0 &&
          minQuotePercent > maxQuotePercent
        ) {
          //同时设置了‘价格上下限字段’，校验不符合的场景
          //上限、下限  都>0，就要比较大小
          this.$toast({
            content: this.$t(`第${Number(i + 1)}行价格上限和价格下限输入有误`),
            type: 'error'
          })
          throw new Error(this.$t(`第${Number(i + 1)}行价格上限和价格下限输入有误`))
        }

        if (
          this.hasColumn('itemExtMap.startingPrice') &&
          !isNullOrUndefined(startingPrice) &&
          startingPrice > 0
        ) {
          //同时设置了‘起价’，校验不符合的场景
          if (
            this.hasColumn('itemExtMap.minQuotePercent') &&
            !isNullOrUndefined(minQuotePercent) &&
            minQuotePercent > 0 &&
            this.hasColumn('itemExtMap.maxQuotePercent') &&
            !isNullOrUndefined(maxQuotePercent) &&
            maxQuotePercent > 0 &&
            (startingPrice <= minQuotePercent || startingPrice > maxQuotePercent)
          ) {
            // 起价、上限、下限  都>0，就要比较大小
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行，‘起价’需要介于价格上限、价格上限之间。`),
              type: 'error'
            })
            throw new Error(this.$t(`第${Number(i + 1)}行，‘起价’需要介于价格上限、价格上限之间。`))
          } else if (
            this.hasColumn('itemExtMap.minQuotePercent') &&
            !isNullOrUndefined(minQuotePercent) &&
            minQuotePercent > 0 &&
            startingPrice <= minQuotePercent
          ) {
            // 起价、下限  都>0，就要比较大小
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行，‘起价’需要高于‘价格下限’`),
              type: 'error'
            })
            throw new Error(this.$t(`第${Number(i + 1)}行，‘起价’需要高于‘价格下限’`))
          } else if (
            this.hasColumn('itemExtMap.maxQuotePercent') &&
            !isNullOrUndefined(maxQuotePercent) &&
            maxQuotePercent > 0 &&
            startingPrice > maxQuotePercent
          ) {
            // 起价、上限  都>0，就要比较大小
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行，‘起价’需要低于‘价格上限’`),
              type: 'error'
            })
            throw new Error(this.$t(`第${Number(i + 1)}行，‘起价’需要低于‘价格上限’`))
          }
        }
      }
      this.handleSaveSingleTabData(cloneDeep(this.submitTableData))
    },
    serializeSaveParams(_fieldDefines, _data) {
      // let _fieldList = [];
      // let _fieldDefines = this.pageConfig[0]["fieldDefines"];
      let _extSaveRequest = {} //扩展表字段存储  rfx_item_ext
      let _dieSaveRequest = {} //模具信息  字段存储  rfx_item_die
      let _logisticsSaveRequest = {} //物流信息 字段存储 rfx_item_logistics

      // 参考供应商的信息需要携带
      if (getValueByPath(_data, 'itemExtMap.referItemSupplierId')) {
        _extSaveRequest.referItemSupplierId = getValueByPath(
          _data,
          'itemExtMap.referItemSupplierId'
        )
        if (!_data.supplierId) {
          _data.supplierId = getValueByPath(_data, 'itemExtMap.referItemSupplierId')
          _data.supplierCode = getValueByPath(_data, 'itemExtMap.referItemSupplierCode')
          _data.supplierName = getValueByPath(_data, 'itemExtMap.referItemSupplierName')
        }
      }

      for (let i in _fieldDefines) {
        let _temp = _fieldDefines[i]
        // 之前的扩展字段逻辑
        // if (_fieldDefines[i]["tableField"] < 1) {
        //   _temp["fieldData"] = _data[_fieldDefines[i]["fieldCode"]];
        //   _temp.recordId = _data.id ? _data.id : null;
        //   _fieldList.push(_temp);
        //   delete _data[_fieldDefines[i]["fieldCode"]];
        // }
        if (dateEditFields.indexOf(_temp.fieldCode) > -1) {
          //日期类型

          if (_data[_temp['fieldCode']]) {
            _data[_temp['fieldCode']] = this.$utils.formatTime(_data[_temp['fieldCode']])
            console.log('日期类型序列化', _data[_temp['fieldCode']])
          }
        }
        if (_temp['tableName'] === 'rfx_item_ext') {
          if (_data['itemExtMap'] != undefined) {
            _extSaveRequest[_temp['fieldCode']] = _data['itemExtMap'][_temp['fieldCode']]
            delete _data['itemExtMap'][_temp['fieldCode']]
          }
        } else if (_temp['tableName'] === 'rfx_item_logistis') {
          if (_data['itemLogisticsResponse'] != undefined) {
            _logisticsSaveRequest[_temp['fieldCode']] =
              _data['itemLogisticsResponse'][_temp['fieldCode']]
            delete _data['itemLogisticsResponse'][_temp['fieldCode']]
          }
        } else if (_temp['tableName'] === 'rfx_item_die') {
          if (_data['itemDieResponse'] != undefined) {
            _dieSaveRequest[_temp['fieldCode']] = _data['itemDieResponse'][_temp['fieldCode']] ?? ''
            delete _data['itemDieResponse'][_temp['fieldCode']]
          }
        }
        if (_temp.fieldCode === 'temporaryItemCode' && !_data.id) {
          //如果存在 ‘临时物料编码’字段  后端定义，将categoryCode赋值
          //只在‘新增’数据进行赋值   有过id的数据，不再赋值
          _data['temporaryItemCode'] = _data['categoryCode']
        }
      }
      delete _data['itemExtMap']
      delete _data['itemLogisticsResponse']
      delete _data['itemDieResponse']
      _data['extSaveRequest'] = _extSaveRequest
      _data['dieSaveRequest'] = _dieSaveRequest
      _data['logisticsSaveRequest'] = _logisticsSaveRequest
    },
    handleSaveSingleTabData(list) {
      if (Array.isArray(list) && list.length) {
        list.forEach((_data) => {
          delete _data.addId
          delete _data.childItems
          if (_data?.parentRfxItemKey) {
            //存在parentRfxItemKey，使用子层结构 做数据保存
            this.serializeSaveParams(utils.cloneDeep(this.childFields), _data)
          } else {
            this.serializeSaveParams(utils.cloneDeep(this.fieldDefines), _data)
          }
          // _data["fieldDataList"] = _fieldList;
          //附件字段  单独处理
          // _data["fileList"] = _data["reviewPriceFileList"]
          //   ? JSON.parse(_data.reviewPriceFileList)
          //   : null;
          // _data["reviewPriceFileList"];
          // 修复文件类型,从string改成对象

          if (_data['drawing'] && typeof _data['drawing'] == 'string') {
            _data['drawing'] = JSON.parse(_data['drawing'])
            if (_data['drawing'].length) {
              _data['drawing'].forEach((f) => {
                if (!f?.sysFileId) {
                  f.sysFileId = f.id
                  delete f.id
                }
              })
            }
          } else if (!_data['drawing']) {
            _data['drawing'] = []
          }
          _data['fileList'] = _data['drawing']
          _data['drawing'] = ''

          if (_data['reviewPriceFileList'] && typeof _data['reviewPriceFileList'] == 'string') {
            _data['reviewPriceFileList'] = JSON.parse(_data['reviewPriceFileList'])
            if (_data['reviewPriceFileList'].length) {
              _data['reviewPriceFileList'].forEach((f) => {
                if (!f?.sysFileId) {
                  f.sysFileId = f.id
                  delete f.id
                }
              })
            }
          } else if (!_data['reviewPriceFileList']) {
            _data['reviewPriceFileList'] = []
          }
          // _data['fileList'] = _data['reviewPriceFileList']
          // _data['reviewPriceFileList'] = ''

          if (_data['stepNum'] && typeof _data['stepNum'] == 'string') {
            _data['stepNum'] = JSON.parse(_data['stepNum'])
          } else if (!_data['stepNum']) {
            _data['stepNum'] = []
          }
          _data['itemStageList'] = _data['stepNum']
          _data['itemStageList'].forEach((x) => {
            x.stepType = 2
          })
          _data['stepNum'] = ''
          _data['rfxHeaderId'] = this.detailInfo.id
          _data['rfxHeaderCode'] = this.detailInfo.rfxCode
          _data['rfxHeaderName'] = this.detailInfo.rfxName
          _data['businessTypeId'] = this.detailInfo.businessTypeId
          _data['businessTypeCode'] = this.detailInfo.businessTypeCode
          _data['businessTypeName'] = this.detailInfo.businessTypeName
        })
      }
      for (let item of list) {
        if (item.extSaveRequest.requireDate != undefined) {
          item.extSaveRequest.requireDate = Date.parse(new Date(item.extSaveRequest.requireDate))
        }
        if (item.extSaveRequest.requireEndDate != undefined) {
          item.extSaveRequest.requireEndDate = Date.parse(
            new Date(item.extSaveRequest.requireEndDate)
          )
        }
      }
      let flag = false
      list.forEach((x, i) => {
        if (
          x.extSaveRequest.adviseMinPurQuantity != undefined &&
          x.extSaveRequest.adviseMinPurQuantity != null &&
          x.extSaveRequest.adviseMinPurQuantity != '' &&
          x.extSaveRequest.adviseMinPackageQuantity != undefined &&
          x.extSaveRequest.adviseMinPackageQuantity != null &&
          x.extSaveRequest.adviseMinPackageQuantity != ''
        ) {
          let adviseMinPurQuantity = x.extSaveRequest.adviseMinPurQuantity
          let adviseMinPackageQuantity = x.extSaveRequest.adviseMinPackageQuantity
          let number = adviseMinPurQuantity / adviseMinPackageQuantity
          if (
            Math.floor(number) !== number &&
            !this.txCompanyList.includes(this.$route.query?.companyCode)
          ) {
            this.$toast({
              content: `第${i + 1}行` + this.$t('最小采购量需是最小包装数量的整数倍'),
              type: 'warning'
            })
            flag = true
          }
        }
        if (x.extSaveRequest && x.extSaveRequest.minQuoteRangeType == 1) {
          x.extSaveRequest.minQuoteRange = new Decimal(x.extSaveRequest.minQuoteRange)
            .div(new Decimal(100))
            .toNumber()
        } else if (x.extSaveRequest && x.extSaveRequest.minQuoteRangeType == 0) {
          if (
            x.extSaveRequest.maxQuotePercent > 0 &&
            x.extSaveRequest.minQuotePercent > 0 &&
            x.extSaveRequest.minQuoteRange >
              x.extSaveRequest.maxQuotePercent - x.extSaveRequest.minQuotePercent
          ) {
            // 如果价格上限、下限，都有值，且>0，校验‘最小竞价幅度’
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行最小竞价幅度应小于价格上限减去价格下限`),
              type: 'error'
            })
            flag = true
          }
        }
        if (x.extSaveRequest.minQuotePercent === null) x.extSaveRequest.minQuotePercent = ''
        if (x.extSaveRequest.maxQuotePercent === null) x.extSaveRequest.maxQuotePercent = ''
        if (x.extSaveRequest.minQuoteRange === null) x.extSaveRequest.minQuoteRange = ''
        if (x.extSaveRequest.startingPrice === null) x.extSaveRequest.startingPrice = ''
      })
      if (flag) return

      let _params = {
        deleteIdList: { idList: this.ids },
        rfxId: this.$route.query.rfxId,
        rfxItems: list
      }

      this.$API.rfxRequireDetail.saveRFXItem(_params).then(() => {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
        this.ids = []
      })
    },
    handleColumnData(data) {
      const sortArr = ['lineNo', 'itemName', 'siteName']
      const _arr = []
      const _delArr = []
      if (data.length <= 0) return []
      data.map((item) => {
        if (sortArr.includes(item.fieldCode)) {
          _delArr.push(item)
        } else {
          _arr.push(item)
        }
      })
      return [..._delArr, ..._arr]
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  /deep/.e-spinner-pane {
    display: none;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
</style>
