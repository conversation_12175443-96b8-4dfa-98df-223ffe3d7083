import { quotaColumnData, generalQuotaColumnData } from './index'
import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import checkbox from '@/components/AgCellComponents/checkbox'
import cellLink from '@/components/AgCellComponents/cellLink'
import cellDialog from '@/components/AgCellComponents/cellDialog'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item'
import { columnData as itemLogistics } from '@/views/common/columnData/ag/itemLogistics'
import { columnData as biddingItemLogistics } from '@/views/common/columnData/ag/biddingItemLogistics'
import { columnData as itemDie } from '@/views/common/columnData/ag/itemDie'
import { columnData as itemExt } from '@/views/common/columnData/ag/itemExt'
import { columnData as itemLogisticsSea } from '@/views/common/columnData/ag/itemLogisticsSea'
import { columnData as itemLogisticsRailway } from '@/views/common/columnData/ag/itemLogisticsRailway'
import { columnData as itemLogisticsTrunk } from '@/views/common/columnData/ag/itemLogisticsTrunk'
// import selectDieItemCode from "./components/selectDieItemCode.vue"; // 模具
import { getHeadersFileName, download } from '@/utils/utils'
import { stepNotMergeFieldNoPrefix } from '@/views/common/columnData/constant'
import { addRowSpan } from '@/views/common/columnData/utils'
import cloneDeep from 'lodash/cloneDeep'
import {
  quoTableNameMap,
  logisticsTableNameMap
  // notAllowEditFields,
  // ktNotAllowEditFields,
  // costFactorNotAllowEditFields,
  // generalNumberEditFields,
  // integerNumberEditFields,
  // priceNumberEditFields,
  // dateEditFields,
  // booleanEditField
} from '@/views/common/columnData/ag/purConstant'
import clickoutside from '@/directive/clickoutside'
export default {
  inject: ['reload'],
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    // eslint-disable-next-line
    cellFile,
    // eslint-disable-next-line
    checkbox,
    // eslint-disable-next-line
    cellLink, // 单元格点击跳转
    // eslint-disable-next-line
    cellDialog
  },
  directives: { clickoutside: clickoutside },
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tabSource: [],
      moduleType: 0,
      agGrid: null,
      toolbar: [],
      searchConfig: [],
      tableData: [],
      groupedTableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false,
      allocationRange: null, //allocationRange==1时展示配额分配tab
      rfxId: this.$route.query.rfxId,
      context: null,
      dictItems: [],
      taxList: [], // 税率编码
      currencyList: [], // 货币名称
      purUnitList: [], // 采购单位
      priceClassificationList: [
        { text: this.$t('暂估价格'), value: 'predict_price' },
        { text: this.$t('SRM价格'), value: 'srm_price' },
        { text: this.$t('执行价格'), value: 'execute_price' },
        { text: this.$t('基价'), value: 'basic_price' }
      ],
      oldList: [], //接口数据与编辑后数据进行比较
      siteNameList: [], //工厂列表
      childItemList: [], //子集List
      isStepGrid: false, // 是否是阶梯格式的数据，通过columns中有没有stepNum字段来判断
      itemGroupIdList: [], // 阶梯groupId
      currentEditData: null, //当前编辑的数据
      currentTabId: 0,
      currentTab: -1,
      requestUrls: {},
      downTemplateParams: {
        rfxId: this.$route.query.rfxId
      },
      txCompanyList: ['2M01', '2S06']
    }
  },
  watch: {},
  computed: {
    rowClassRules() {
      return {
        stripeEven: (params) => {
          return (
            this.groupedTableData && this.groupedTableData.indexOf(params.data.itemCode) % 2 === 1
          )
        }
      }
    }
  },
  beforeDestroy() {
    this.$bus.$off('refreshPricingTab')
  },
  beforeMount() {
    this.context = { componentParent: this }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.$bus.$on('refreshPricingTab', () => {
      this.refresh()
    })
    await this.getStrategyData()
    await this.initSiteList()
    this.init()
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<< 事件操作 >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // tab - 切换页签
    handleSelectTab(idx) {
      this.moduleType = this.tabSource[idx].moduleType
      // 初始数据
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 点击事件监听
    clickoutside() {
      this.agGrid.api.stopEditing()
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.id
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Save': // 保存
          this.handleSave()
          break
        case 'SubmitExamine': // 提交审批
          this.handleSubmitExamine()
          break
        case 'ExamineProgres': // OA审批进度
          this.handleExamineProgres()
          break
        case 'RejectBargain': // 驳回议价
          this.handleRejectBargain()
          break
        case 'StartNewRound': // 发起新一轮
          this.handleStartNewRound()
          break
        case 'Assistant': // 比价助手
          this.handleAssistant()
          break
        case 'BatchVmi': // 批量VMI
          this.handleBatchVmi()
          break
        case 'PricingExport': // 报价明细导出
          this.handleExport('pricing')
          break
        case 'Import': // 配额导入
          this.handleImport()
          break
        case 'QuotaExport': // 配额导出
          this.handleQuotaExport()
          break
        default:
          break
      }
    },
    // toolbar点击监听 - 保存
    handleSave() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行保存操作？`)
        },
        success: () => {
          let _dataSource = this.serializeSaveParams(cloneDeep(this.tableData))
          this.handleSaveAPI(_dataSource, this.moduleType)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功.'),
                  type: 'success'
                })
              }
            })
            .finally(() => {
              this.refresh()
              // this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    },
    // toolbar点击监听 - 提交审批
    async handleSubmitExamine() {
      // 获取涨价提示
      const res = await this.$API.rfxPricing.submitPricingValid({ rfxId: this.$route.query.rfxId })
      if (res.code === 200 && res.data) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: res.msg,
            cssClass: 'rise-price'
          },
          success: () => {
            this.submitFn()
          }
        })
      } else {
        this.submitFn()
      }
    },
    // toolbar点击监听 - 提交审批 - 提交
    submitFn() {
      let _dataSource = this.serializeSaveParams(cloneDeep(this.tableData))
      // 通讯-校验付款条件必填
      if (this.txCompanyList.includes(this.detailInfo.companyCode)) {
        const invalidItems = _dataSource.filter((item) => {
          // 检查是否可编辑且付款条件为空
          return item.editable && !item.paymentCondition
        })

        if (invalidItems.length > 0) {
          const rowNumbers = invalidItems.map((item) => item.rowIndex).join('、')
          this.$toast({
            content: this.$t(`第${rowNumbers}行请选择付款条件`),
            type: 'error'
          })
          return
        }
      }

      this.$store.commit('startLoading')
      this.handleSaveAPI(_dataSource, this.moduleType).then((r) => {
        if (r.code == 200) {
          let params = {
            rfxId: this.$route.query.rfxId
          }
          this.$API.rfxPricing
            .getRfxPricingSubmit(params)
            .then((res) => {
              if (res.code == 200) {
                this.$store.commit('endLoading')
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.reload()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
            .finally(() => {
              // this.$emit('reGetDetail')
            })
        }
      })
    },
    // toolbar点击监听 - OA审批进度
    handleExamineProgres() {
      //ApproveType  暂时处理：定标/定价页面
      //业务类型 0招标竞价定标 1询报价定价 2商务标晋级 3提交立项
      let params = {
        approveType: this.$route?.query?.source == 'rfq' ? 1 : 0,
        rfxId: this.$route.query.rfxId
      }
      this.$API.rfxExt.getOaLink(params).then((res) => {
        if (res?.data && res.data.indexOf('.com')) {
          window.open(res.data)
        }
      })
    },
    // toolbar点击监听 - 驳回议价
    handleRejectBargain() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'驳回议价'操作？")
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.rfxPricing
            .toEvaluationBid(query)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.reload()
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    // toolbar点击监听 - 发起新一轮
    handleStartNewRound() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'发起新一轮'操作？")
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.comparativePrice.startNewRound(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.reload()
          })
        }
      })
    },
    // toolbar点击监听 - 比价助手
    handleAssistant() {
      this.$dialog({
        modal: () => import('@/views/purchase/rfx/components/comparativePriceDialog.vue'),
        data: {
          rfxId: this.$route.query.rfxId,
          detailInfo: this.detailInfo
        }
      })
    },
    // toolbar点击监听 - 批量VMI
    handleBatchVmi() {
      let _selectedRows = this.agGrid.api.getSelectedRows()
      if (!_selectedRows?.length) return
      let rfxBiddingItemIds = _selectedRows.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行"批量VMI"操作？')
        },
        success: () => {
          this.$API.comparativePrice
            .batchSetVmi({
              rfxId: this.rfxId,
              rfxBiddingItemIds
            })
            .then(() => {
              this.$toast({ content: this.$t('操作成功！'), type: 'success' })
              this.refresh()
            })
        }
      })
    },
    // ag - 监听select选择框
    onRowSelected(e) {
      if (!e.data.isFirstLine) return
      let isSelected = e.node.selected
      let customGroupKey = e.data.customGroupKey
      //如果勾选的是阶梯第一行数据,同步其他行
      this.agGrid.api.forEachNode((node) => {
        if (node.data.customGroupKey === customGroupKey && !node.data.isFirstLine) {
          node.setSelected(isSelected)
        }
      })
    },
    // ag - 编辑框监听
    cellValueChanged(params) {
      if (params.oldValue === params.newValue) return

      // 特殊处理付款条件字段
      if (params.column.colId === 'paymentCondition') {
        // 确保值被正确设置到数据模型中
        params.data.paymentCondition = params.newValue
        if (params.data.itemExtMap) {
          params.data.itemExtMap.paymentCondition = params.newValue
        }
      }

      this.syncOtherStepLine(params) //同步阶梯行
    },
    // 编辑框监听 - 阶梯同步其他行数据
    syncOtherStepLine(params) {
      const field = params.column.colId
      const customGroupKey = params.data.customGroupKey
      const isFirstLine = params.data.isFirstLine
      if (!isFirstLine || stepNotMergeFieldNoPrefix.includes(field) || !customGroupKey) return
      // 查询同组阶梯数据
      this.agGrid.api.forEachNode((node) => {
        if (!node.data.isFirstLine && node.data.customGroupKey === customGroupKey) {
          node.setDataValue(field, params.value)
        }
      })
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    // btn - 查询
    search(params) {
      this.initTableData(params)
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    async init() {
      await this.initDictItems()
      this.initTab()
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // 初始化 - 获取策略信息
    async getStrategyData() {
      await this.$API.strategyConfig
        .findByRfxId({
          sourcingMode: this.$route.query.source,
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          // 配额分配范围
          this.allocationRange = res.data.strategyConfigList[0].allocationRange
        })
    },
    // 初始化 - tab页签
    initTab() {
      let _tabSource = [
        { title: this.$t('报价明细'), moduleType: 0 },
        { title: this.$t('配额分配'), moduleType: 1 },
        { title: this.$t('历史报价'), moduleType: 2 }
      ]
      if (this.allocationRange !== 1) {
        //非议价状态没有议价tab页
        _tabSource.splice(1, 1)
      }
      this.tabSource = _tabSource
    },
    // 初始化 - 查询区域
    initSearchConfig() {
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'categoryCode',
          label: this.$t('品类编码'),
          hide: this.moduleType === 1
        },
        {
          field: 'temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'supplierCode',
          label: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          label: this.$t('供应商名称')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar() {
      let _toolbar = []
      if (this.moduleType === 2) {
        // 历史报价 不显示
        this.toolbar = []
        return
      }
      let status = this.$route.query?.status
      let transferStatus = this.detailInfo.transferStatus // 20:已提交定点 100 已完成
      // toolbar 控制
      _toolbar = [
        {
          id: 'Save',
          icon: 'icon_solid_Save',
          title: this.$t('保存'),
          hide: [8, -1].includes(status) || [20, 100].includes(transferStatus)
        },
        {
          id: 'SubmitExamine',
          icon: 'icon_solid_Submit',
          title: this.$t('提交审批'),
          hide: [8, -1].includes(status) || [20, 100].includes(transferStatus)
        },
        {
          id: 'ExamineProgres',
          icon: 'icon_solid_Submit',
          title: this.$t('OA审批进度'),
          hide: status === -1
        },
        {
          id: 'RejectBargain',
          icon: 'icon_solid_Submit',
          title: this.$t('驳回议价'),
          hide: [20, 100].includes(transferStatus)
        },
        {
          id: 'StartNewRound',
          icon: 'icon_solid_Submit',
          title: this.$t('发起新一轮'),
          hide: [20, 100].includes(transferStatus)
        },
        // {
        //   id: 'CreateMoldBuildTable',
        //   icon: 'icon_solid_edit',
        //   title: this.$t('生成模具制造审批表'), // 暂未接入TODO:
        //   hide: transferStatus !== 100
        // },
        {
          id: 'Assistant',
          icon: 'icon_solid_edit',
          title: this.$t('比价助手'),
          hide:
            [8, -1].includes(status) ||
            [20, 100].includes(transferStatus) ||
            this.moduleType === 1 ||
            ['sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            )
        },
        {
          id: 'BatchVmi',
          icon: 'icon_solid_edit',
          title: this.$t('批量VMI'),
          hide:
            [8, -1].includes(status) ||
            [20, 100].includes(transferStatus) ||
            this.moduleType === 1 ||
            ['sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            )
        },
        {
          id: 'Import',
          icon: 'icon_solid_Import',
          title: this.$t('导入'),
          hide:
            [8, -1].includes(status) || [20, 100].includes(transferStatus) || this.moduleType !== 1
        },
        {
          id: 'PricingExport', // 明细导出导出
          icon: 'icon_solid_export',
          title: this.$t('导出'),
          hide: this.moduleType !== 0 ? true : false
        },
        {
          id: 'QuotaExport', // 配额导出
          icon: 'icon_solid_export',
          title: this.$t('导出'),
          hide: this.moduleType !== 1 ? true : false
        }
      ]
      this.toolbar = _toolbar
    },
    // 初始化 - 表头
    async initGridColumns() {
      // 添加表头固定字段
      let _columnData = this.setFixdColumns(cloneDeep(this.fieldDefines))
      let _quotacolumnData = generalQuotaColumnData(this) // 配额列信息
      let _columns = this.moduleType === 1 ? cloneDeep(_quotacolumnData) : cloneDeep(_columnData)
      // 设置表头属性
      _columns = this.defineGridColumns(_columns)
      // 物流表头特殊设置
      if (this.detailInfo.sourcingScenarios === 'sea_transport_annual') {
        _columns = await this.setAnnualLogisticsItemConfig(_columns) // 物流海运字段较多，需要设置为双表头结构
      }
      // 物流干线表头特殊设置
      if (this.detailInfo.sourcingScenarios === 'trunk_transport_annual') {
        _columns = await this.setLogisticsDynamicConfig(_columns) // 物流海运字段较多，需要设置为双表头结构
      }
      this.columns = cloneDeep(_columns)
    },
    // 表头 - 字段序列化 （TODO:父子结构）
    defineGridColumns(fieldDefines) {
      let _gridEditAble = this.getGridEditAble()
      let haveTempItemCode = fieldDefines.some((e) => e.fieldCode === 'temporaryItemCode')
      let itemCodeEditable = false
      // 存在临时物料，则物料选择框，支持操作 (添加固定字段隐藏，否则setDataValue报错)
      let fixFields = [
        'itemId',
        'itemName',
        'spec',
        'material',
        'unitName',
        'categoryId',
        'categoryCode',
        'categoryName',
        'priceUnitName'
      ]
      let _columnData = []
      const tableColumnMap = {
        rfx_item: rfxItem({
          currencyList: this.currencyList,
          taxList: this.taxList,
          purUnitList: this.purUnitList,
          isFc: this?.detailInfo?.rfxGeneralType === 2
        }),
        rfx_item_logistics: itemLogistics({
          // 物流信息
          prefix: 'itemLogisticsResponse.',
          dictItems: this.dictItems
        }),
        rfx_bidding_item: rfxItem({
          // 采方直接去外层数据，不取bddingItemDTO
          currencyList: this.currencyList,
          taxList: this.taxList,
          purUnitList: this.purUnitList,
          isTx: this.txCompanyList.includes(this?.detailInfo?.companyCode),
          companyCode: this?.detailInfo?.companyCode,
          rfxGeneralType: this?.detailInfo?.rfxGeneralType
        }),
        rfx_bidding_item_logistics: biddingItemLogistics({
          prefix: 'biddingItemLogisticsResponse.',
          dictItems: this.dictItems
        }),
        rfx_item_die: itemDie({
          // 模具信息
          prefix: 'itemDieResponse.'
        }),
        rfx_item_ext: itemExt({
          prefix: 'itemExtMap.'
        }),
        mt_rfx_annual_logistics_sea_bidding_item: itemLogisticsSea({
          prefix: 'rfxAnnualLogisticsSeaDTO.',
          dictItems: this.dictItems
        }),
        logistics_railway_item: itemLogisticsRailway({
          prefix: 'annualLogisticsRailwayItemDTO.',
          dictItems: this.dictItems
        }),
        bidding_annual_railway: itemLogisticsRailway({
          prefix: 'annualLogisticsRailwayItemDTO.',
          dictItems: this.dictItems
        }),
        annual_logistics_transport: itemLogisticsTrunk({
          prefix: 'annualLogisticsTrunkItem.',
          dictItems: this.dictItems
        })
      }
      fieldDefines.forEach((item) => {
        let tableName = item.tableName || 'rfx_item'
        let name = quoTableNameMap[tableName]?.key
        let field = name ? `${name}.${item.fieldCode}` : `${item.fieldCode}`
        let headerName = item.headerName || item.fieldName
        let editable = item?.editable || false
        // 2.与配置文件中字段进行匹配
        let column = tableColumnMap[tableName]?.find((e) => e.field === field) || {}
        // 3.如果配置文件未配置，则手动添加
        // column = this.addEditAttribute({ ...column, fieldCode: item.fieldCode })
        if (
          item.fieldCode === 'actualContainerQty' ||
          (item.fieldCode === 'allocationRatio' && this.allocationRange !== 1)
        ) {
          // 物流实际分配柜量可编辑 && 配额分配可编辑
          editable = true
        }
        let _one = {
          width: 130, //默认值130
          editable: _gridEditAble && editable,
          ...column,
          fieldCode: item.fieldCode,
          field,
          headerName,
          tableName,
          cellClass: () => {
            return _gridEditAble && editable ? 'cellEditable' : ''
          }
        }
        // 特殊处理 - 物料编码itemCode
        // 1.存在临时物料编码temporaryItemCode，且单据状态为'定标中' 可以编辑正式物料
        // 2.可编辑情况下添加固定字段，避免编辑报错
        if (
          item.fieldCode === 'itemCode' &&
          haveTempItemCode &&
          [34].includes(this?.detailInfo?.transferStatus)
        ) {
          _one = Object.assign({}, _one, {
            editable: true,
            editConfig: {
              type: 'selectSearch', //带弹框的下拉框
              props: {
                rfxId: this.$route.query.rfxId,
                sourcingType: this.detailInfo.sourcingType,
                source: this.$route.query.source
              }
            },
            cellClass: 'cellEditable'
          })
          itemCodeEditable = true
        }
        // 特殊处理 - 成本分析
        if (['costAnalysis'].includes(item.fieldCode)) {
          _one.cellRenderer = 'cellLink'
          _one.cellRendererParams = {
            handleable: true, //"可操作"（不可编辑），取editable避免从历史报价进入成本分析页面
            rfxId: this.$route.query.rfxId,
            type: 'pur'
          }
        }
        // 特殊处理 - 附件查看
        if (['drawing', 'supplierDrawing'].includes(item.fieldCode)) {
          _one.cellRenderer = 'cellFile'
        }
        // 特殊处理 - VMI
        if (['vmi'].includes(item.fieldCode)) {
          _one.cellRenderer = 'checkbox'
        }
        // 特殊处理 - 柜量
        if (['cabinetQuantity'].includes(item.fieldCode)) {
          _one.cellRenderer = 'cellDialog'
          _one.editable = false
          _one.cellRendererParams = {
            position: 'rfxAnnualLogisticsSeaDTO',
            handleable: true, //"可操作"（不可编辑）（针对于附件查看类字段）
            rfxId: this.$route.query.rfxId
          }
        }
        _columnData.push(_one)
      })

      if (itemCodeEditable) {
        // 如果配置了固定字段，则删除fixFields
        _columnData.forEach((item) => {
          if (fixFields.indexOf(item.fieldCode) > -1)
            fixFields.splice(fixFields.indexOf(item.fieldCode), 1)
        })
        // 特殊处理，添加隐藏项
        fixFields.map((item) => {
          _columnData.push({
            field: item,
            hide: true,
            width: 0
          })
        })
      }
      // 添加勾选框
      if (this.isSelectControl()) {
        _columnData.unshift({ option: 'checkboxSelection', width: 55 })
      }

      return _columnData
      // return combination.defineGridColumnsAfter(this, arr, isChild, editInstance)
    },
    setFixdColumns(columnData) {
      let _preColumns = [
        {
          fieldCode: 'rowIndex',
          fieldName: this.$t('序号')
        }
      ]
      let _columnData = _preColumns.concat(columnData)
      _columnData.push(
        {
          fieldCode: 'bidTimes',
          fieldName: this.$t('次数')
        },
        {
          fieldCode: 'roundNo',
          fieldName: this.$t('轮次')
        }
      )
      return _columnData
    },
    // 表头 - 物流双表头字段处理
    async setAnnualLogisticsItemConfig(columns) {
      let res = await this.$API.rfxQuotationDetails.getLogisticsConfig({
        rfxId: this.$route.query.rfxId,
        moduleType: 20
      })
      if (res.code === 200) {
        this.annualLogisticsItemConfig = [...res.data]
      }
      let _columns = this.groupColumns(columns, this.annualLogisticsItemConfig)
      return _columns
    },
    // 物流 - 获取阶梯列配置
    async setLogisticsDynamicConfig(_columnData) {
      let dynamicConfig = []
      const res = await this.$API.rfxDetail.getLogisticsDynamicConfig({
        id: this.$route.query.rfxId
      })
      if (res.code === 200) {
        const _data = { ...res.data } || {}
        for (let i in _data) {
          _data[i].forEach((item) => {
            dynamicConfig.push({
              headerName: item.stepName,
              children: [
                {
                  headerName: this.$t('数量'),
                  width: 80,
                  field: `${item.stepCode}_fieldData`,
                  fieldCode: `${item.stepCode}_fieldData`
                },
                {
                  headerName: this.$t('未税单价'),
                  width: 120,
                  field: `${item.stepCode}_untaxedUnitPrice`,
                  fieldCode: `${item.stepCode}_untaxedUnitPrice`,
                  editable: true,
                  option: 'customEdit',
                  editConfig: {
                    type: 'number',
                    props: {
                      min: 0,
                      precision: '0'
                    }
                  }
                }
              ]
              // tableName: logisticsTableNameMap[this.quotedPriceData.sourcingScenarios]['tableName']
            })
          })
        }
      }
      // 找到字段三的索引
      const index = _columnData.findIndex((column) => column.fieldCode === 'totalQty')
      // 将 stepList 插入到字段三之前
      if (index !== -1) {
        _columnData.splice(index, 0, ...dynamicConfig)
      }
      // 将数据插入
      return _columnData
    },
    groupColumns(columns, obj) {
      const result = []
      const groupMap = new Map()

      obj.forEach((group) => {
        groupMap.set(group.fieldGroup, group.fields)
      })
      const addedFieldCodes = new Set()
      columns.forEach((column) => {
        const groupEntry = Array.from(groupMap.entries()).find(([_, fields]) =>
          fields.some((field) => field.fieldCode === column.fieldCode)
        )
        if (groupEntry) {
          const [groupName] = groupEntry
          let groupItem = result.find((item) => item.fieldName === groupName)
          if (!groupItem) {
            groupItem = { headerName: groupName, fieldName: groupName, children: [] }
            result.push(groupItem)
          }
          groupItem.children.push({ ...column })
          addedFieldCodes.add(column.fieldCode)
        } else {
          result.push({ ...column })
        }
      })

      // 过滤掉已添加到分组中的字段
      return result.filter((item) => !addedFieldCodes.has(item.fieldCode))
    },
    // 表头 - 工具 - 判断是否需要选择框
    isSelectControl() {
      if (this.moduleType === 2 || [20, 100].includes(this.detailInfo.transferStatus)) return false
      return true
    },
    // 表头 - 获取表头是否可编辑
    getGridEditAble() {
      if (this.moduleType === 2) return false
      // 报价中|| 商务投标中 采购明细可以编辑
      if (![20, 100].includes(this.detailInfo.transferStatus)) return true
      return false
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      let url = ''
      let field = ''
      switch (this.moduleType) {
        case 0:
          url = this.$API.rfxPricing.getRfxPricingList // 报价明细
          field = 'rfx_item.rfxHeaderId'
          break
        case 1:
          url = this.$API.rfxPricing.getRfxItemQuotaList // 配额分配
          field = 'rfxId'
          break
        case 2:
          url = this.$API.rfxPricing.getRfxPricingLists // 历史报价
          field = 'rfx_item.rfxHeaderId'
          break
      }
      this.$store.commit('startLoading')
      let params = this.mergeParams(rules, field)
      this.tableData = []
      const res = await this.$API.rfxPricing
        .getRfxPricingItemList(url, { ...params })
        .catch(() => {})
      if (res.code === 200) {
        let records = res.data?.records || []
        let list = await this.serializeGridList(records)
        // 通讯判断是否为多种付款条件
        if (this.txCompanyList.includes(this.detailInfo.companyCode)) {
          list = await this.isPaymentCondition(list)
        }
        this.tableData = cloneDeep(list)
        // list分组
        const groupData = list.reduce((result, current) => {
          if (!result[current.itemCode]) {
            result[current.itemCode] = []
          }
          result[current.itemCode].push(current)
          return result
        }, {})
        this.groupedTableData = Object.keys(groupData)
      }
      this.$store.commit('endLoading')
    },
    isPaymentCondition(list) {
      return new Promise(async (resolve) => {
        try {
          const promises = list.map(async (item) => {
            if (!item.supplierCode) return item

            const res = await this.$API.rfxPricing.checkPaymentCondition({
              orgCode: this.detailInfo.companyCode,
              partnerCode: item.supplierCode
            })

            if (res.code === 200) {
              item.editable =
                [1, '1'].includes(res.data?.businessPartnerAccountingDTO?.multiplePayCondition) ||
                false
            }
            return item
          })

          const result = await Promise.all(promises)
          resolve(result)
        } catch (error) {
          console.error('Check payment condition error:', error)
          resolve(list)
        }
      })
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules, field) {
      let params = {
        defaultRules: [
          {
            label: '',
            field: field,
            type: 'number',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        page: { current: 1, size: 10000 }
      }
      if (rules) params = Object.assign({}, params, rules)
      return params
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  工具方法  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 工具 - 生成GroupKey(区分阶梯数据)
    generateGroupKey(row) {
      let id = row.supplierId || '0'
      let code = row.supplierCode || 'a'
      return row.itemGroupId + '__' + id + '__' + code
    },
    // 初始化字典数据
    async initDictItems() {
      let codeList = [{ code: 'LOGISTICS_DEMAND_TYPE', type: 'string' }]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    },
    async initTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 币种编码
    async initCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency().catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 采购单位
    async initPurUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.purUnitList = res?.data?.records || [] // FIXME 采购单位和基本单位的区别
      })
    },
    async initSiteList() {
      const res = await this.$API.masterData.permissionSiteList({
        buOrgId: sessionStorage.getItem('selectOrgId'),
        companyId: sessionStorage.getItem('selectCompanyId')
      })
      if (res) {
        this.siteNameList = res?.data || []
      }
    },

    // 配额列表字段根据招标询报价区分
    processQuotaData() {
      // 如果是询价大厅 隐藏名次字段
      if (this.$route?.query?.source === 'rfq') {
        const _columns = quotaColumnData(this).filter((item) => item.field !== 'ranking')
        return _columns
      }
      return quotaColumnData(this)
    },
    serializeGridList(list) {
      const resKey = logisticsTableNameMap[this.detailInfo.sourcingScenarios]?.resKey
      list.forEach((v, index) => {
        v.rowIndex = index + 1
        if (v[resKey]?.dynamicFields) {
          for (let i in v[resKey]?.dynamicFields) {
            v[resKey]?.dynamicFields[i].forEach((item) => {
              const _fieldData = `${item.fieldCode}_fieldData`
              const _fieldUntaxedUnitPrice = `${item.fieldCode}_untaxedUnitPrice`
              v[_fieldData] = item.fieldData
              v[_fieldUntaxedUnitPrice] = item.untaxedUnitPrice
            })
          }
        }
        v.customType = 'pur' // 自定义type,区分采、供
        this.defineUnuseRateValue(v)
        v.allocationRatio = (v.allocationRatio * 100).toFixed(0)
        v.declinePercent = v.declinePercent ? (v.declinePercent * 100).toFixed(2) : ''
        v.drawing = v.fileList ? JSON.stringify(v.fileList) : null //单独处理附件字段
        v.supplierDrawing = v.supplierFileList ? JSON.stringify(v.supplierFileList) : null //单独处理"供应商附件"字段
        v.stepQuoteName =
          v.itemStageList && v.itemStageList.length > 0 ? JSON.stringify(v.itemStageList) : null //单独处理阶梯报价
        v.stepNum = v.stepValue

        // 付款条件处理 - 确保字段存在且不为undefined
        // 优先从外层获取，如果为空则从itemExtMap获取
        if (!v.paymentCondition && v.itemExtMap?.paymentCondition) {
          v.paymentCondition = v.itemExtMap.paymentCondition
        }
        if (v.paymentCondition === undefined || v.paymentCondition === null) {
          v.paymentCondition = '' // 设置为空字符串而不是undefined/null
        }

        // 工厂id处理
        let _findSite = this.siteNameList.find((e) => e.orgCode === v.siteCode)
        v.organizationId = _findSite ? _findSite?.id : ''
        // 生成key
        v.customGroupKey = this.generateGroupKey(v)
        // 添加rfxid
        v.rfxId = this.$route.query.rfxId
        let _subItems = v['childItems']
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })

      let arr = []
      list.forEach((x) => {
        arr.push(
          JSON.stringify({
            itemCode: x.itemCode,
            siteCode: x.siteCode
          })
        )
      })
      arr = [...new Set(arr)]
      arr = arr.filter((item, index) => index % 2 !== 0)
      list.forEach((x) => {
        arr.forEach((v) => {
          if (x.itemCode == JSON.parse(v).itemCode && x.siteCode == JSON.parse(v).siteCode) {
            x.isGray = true
          }
        })
      })
      list = addRowSpan(list)
      return list
    },

    // 列表中，税率名称、税率编码，值无效
    defineUnuseRateValue(v) {
      if (v?.bidTaxRateCode && /^\d{19}$/.test(v.bidTaxRateCode)) {
        let _find = this.taxList.find((e) => e.id === v.bidTaxRateCode)
        if (_find && _find.id) {
          v.bidTaxRateCode = _find.taxItemCode
          v.bidTaxRateName = _find.taxItemName
        }
      }
    },

    actionComplete(e) {
      if (e.requestType == 'beginEdit') {
        let _row = e.rowData
        if (!_row?.siteCode) {
          sessionStorage.removeItem('siteParam')
          sessionStorage.removeItem('temp_rfxId')
          sessionStorage.removeItem('temp_rfxItemId')
          return
        }
        let _findSite = this.siteNameList.find((e) => e.orgCode === _row.siteCode)
        if (_findSite) {
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: _findSite['id'],
              siteCode: _row.siteCode
            })
          )
        }
        this.minSplitQuantityCache = e.rowData.minSplitQuantity
        if (!_row?.itemCode) {
          sessionStorage.setItem(
            'dieItemParam',
            JSON.stringify({
              itemCode: _row?.itemCode
            })
          )
        } else {
          sessionStorage.removeItem('dieItemParam')
        }
        sessionStorage.setItem('temp_rfxId', this.$route.query.rfxId)
        sessionStorage.setItem('temp_rfxItemId', _row['rfxItemId'])
      }
      if (e.requestType == 'save') {
        this.$set(this.$refs.templateRef.getCurrentTabRef().grid.dataSource, e.rowIndex, e.data)
        if (e.data.minSplitQuantity != this.minSplitQuantityCache) {
          const grid = this.$refs.templateRef.getCurrentTabRef().grid
          let dataSource = grid.$options.propsData.dataSource
          let itemCode = e.data.itemCode
          if (itemCode) {
            dataSource.forEach((item) => {
              if (item.itemCode == itemCode) {
                item.minSplitQuantity = e.data.minSplitQuantity
              }
            })
            grid.refresh() //刷新表格
          }
        }
        if (this.isStepGrid) {
          //一行同步多行
          this.syncStepRownData(e.data)
        }
      }
    },

    // 生成模具制造审批表
    handleCreateMoldBuildTable(ids) {
      if (ids.length <= 0) {
        this.$toast({ content: this.$t('请选择模具明细数据'), type: 'warning' })
        return
      }
      const query = {
        rfxBiddingItemIds: ids,
        rfxId: this.$route.query.rfxId
      }
      this.$store.commit('startLoading')
      this.$API.rfxPricing
        .createMoldBuildTable(query)
        .then(() => {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleSaveAPI(dataSource, tabIndex) {
      if (!dataSource?.length) return
      let _quotaDTOList = []
      let _dataSource = cloneDeep(dataSource)
      _dataSource.forEach((e, index) => {
        // 配额为0也可以提交。
        e.allocationRatio = e.allocationRatio > 0 ? (e.allocationRatio / 100).toFixed(2) : 0
        if (e?.allocationRatio < 0) {
          this.$toast({
            content: this.$t(`第${Number(index + 1)}行的'配额'字段有误`),
            type: 'error'
          })
          throw new Error(this.$t(`第${Number(index + 1)}行的'配额'字段有误`))
        }
        if (tabIndex == 0) {
          // 报价明细
          const paymentConditionValue = e?.paymentCondition || e?.itemExtMap?.paymentCondition || ''
          const quotaItem = {
            rfxBiddingItemId: e.id,
            allocationQuantity: this.allocationRange === 1 ? undefined : e.allocationQuantity, // 所有有效配额不传
            allocationRatio: this.allocationRange === 1 ? undefined : e.allocationRatio,
            priceClassification: e.priceClassification,
            minSplitQuantity: e.minSplitQuantity,
            dieFormalCode: e?.itemDieResponse?.dieFormalCode,
            basicDieCode: e?.itemDieResponse?.basicDieCode,
            vmi: e.vmi,
            actualContainerQty: e?.biddingItemLogisticsResponse?.actualContainerQty || 0,
            paymentCondition: paymentConditionValue,
            quoteEffectiveStartDate: e?.quoteEffectiveStartDate
              ? new Date(e.quoteEffectiveStartDate).getTime()
              : null,
            quoteEffectiveEndDate: e?.quoteEffectiveEndDate
              ? new Date(e.quoteEffectiveEndDate).getTime()
              : null
          }
          _quotaDTOList.push(quotaItem)
        } else if (tabIndex == 1) {
          //配额分配
          _quotaDTOList.push({
            allocationQuantity: e.allocationQuantity,
            id: e.id,
            allocationRatio: e.allocationRatio,
            priceClassification: e.priceClassification,
            minSplitQuantity: e.minSplitQuantity,
            minPurQuantity:
              e.minPurQuantity && e.minPurQuantity === 0
                ? Number(e.minPurQuantity)
                : e.minPurQuantity,
            itemCode: e.itemCode,
            siteCode: e.siteCode,
            stepValue: e.stepValue || 0
          })
        }
      })
      let params = {
        rfxId: this.$route.query.rfxId,
        quotaDTOList: _quotaDTOList
      }
      if (tabIndex == 0) {
        return this.$API.rfxPricing.getPricingSave(params)
      } else if (tabIndex == 1) {
        return this.$API.rfxPricing.rfxItemQuotaSaveQuota(params)
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'infoRecord') {
        if (e.data.rfxApprovalStatus === 7) {
          //调整'信息记录'操作逻辑
          if (e.data.priceStatus === 5) {
            this.$dialog({
              modal: () => import('../components/index.vue'),
              data: {
                title: this.$t('信息记录'),
                biddingItemId: e.data.id,
                priceStatus: e.data.priceStatus
              },
              success: () => {
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          } else {
            this.$toast({
              content: this.$t('定标审核未通过，不能操作'),
              type: 'warning'
            })
          }
        } else {
          this.$toast({
            content: this.$t('定标审批未完成，不能补充信息记录'),
            type: 'warning'
          })
        }
      }
    },
    serializeSaveParams(list) {
      let res = []
      list.forEach((e) => {
        let _sub = e?.childItems
        delete e.childItems
        res.push(e)
        if (Array.isArray(_sub)) {
          res = res.concat(_sub)
        }
      })
      return res
    },
    // 导入 - 配额
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'rfxPricing',
        templateUrl: 'quotaExport',
        uploadUrl: 'quotaImport',
        rfxId: this.$route.query.rfxId
      }
      this.showUploadExcel(true)
    },
    // 导出 - 报价明细 TODO:
    handleExport(type) {
      const queryBuilderRules = {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            condition: 'and',
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            value: this.rfxId
          }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.commonConfig[type === 'pricing' ? 'exportPricing' : 'exportBiddingData'](
        params
      ).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 导出 - 配额
    handleQuotaExport() {
      this.$API.rfxPricing
        .quotaExport({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          let fileName = getHeadersFileName(res)
          download({ fileName, blob: res.data })
        })
    },
    // 控制上传弹窗是否展示
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.refresh()
    }
  }
}
