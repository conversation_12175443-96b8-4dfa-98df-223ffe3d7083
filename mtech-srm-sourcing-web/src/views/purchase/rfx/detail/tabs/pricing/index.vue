c
<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :current-tab="currentTab"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @actionComplete="actionComplete"
      @handleSelectTab="handleSelectTab"
    />

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="$t('配额导入模板')"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { tabToolbar, canEdit, quotaColumnData } from './config'
import Vue from 'vue'
import { utils } from '@mtech-common/utils'
import { Query } from '@syncfusion/ej2-data'
import { columnData as logisticsColumnData } from '@/views/common/columnData/rfxItemLogistics'
import { columnData as biddingItemLogisticsColumnData } from '@/views/common/columnData/biddingItemLogistics'
import { columnData as itemDieMapColumnData } from '@/views/common/columnData/biddingItemDie'
import { columnData as itemExtMapColumnData } from '@/views/common/columnData/itemExtMap'
import { getFields as getSBIFields } from '@/views/common/columnData/supplierBiddingItem'
import { createEditInstance } from '@/utils/ej/dataGrid'
import selectedItemCode from './components/selectItemCode.vue' // 物料
import selectCkdItemCode from './components/selectCkdItemCode.vue' // ckd物料编码
import cellChanged from 'COMPONENTS/NormalEdit/cellChanged' // 单元格被改变（纯展示）
import checkbox from 'COMPONENTS/NormalEdit/checkbox' // vmi
import editCheckbox from 'COMPONENTS/NormalEdit/checkbox/edit' // vmi
import itemDieInput from 'COMPONENTS/NormalEdit/itemDieInput' //模具
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import cellFileViewSupply from 'COMPONENTS/NormalEdit/cellFileViewSupply' // 单元格上传
import * as combination from './utils/combination' // 单元格上传
import { getHeadersFileName, download } from '@/utils/utils'
import { setChildWidth } from '@/views/common/columnData/utils'
export default {
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      allocationRange: null, //allocationRange==1时展示配额分配tab
      rfxId: this.$route.query.rfxId,
      // tabConfig: {
      //   mode: 'rectangle'
      // },
      pageConfig: [
        {
          title: this.$t('报价明细'),
          toolbar: [],
          useToolTemplate: false,
          gridId: this.$md5(
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'pricing'
            ]['list'] + this.$route?.query?.rfxId
          ),
          // 注释掉的gridIds 是方便查看md5加密之前的数据
          // gridIds:
          //   this.$permission.gridId["purchase"][this.$route?.query?.source][
          //     "detail"
          //   ]["tabs"]["pricing"]["list"] + this.$route?.query?.rfxId,
          grid: {
            allowFiltering: true,
            columnData: [],
            showSelected: false,
            editSettings: {
              allowAdding: false,
              allowEditing: true,
              allowDeleting: false,
              mode: 'Normal', // 默认normal模式
              allowEditOnDblClick: true,
              showConfirmDialog: false,
              showDeleteConfirmDialog: false
            },
            recordDoubleClick: this.recordDoubleClick,
            enableVirtualization: true,
            customSelection: true,
            lineIndex: 0,
            virtualPageSize: 30,
            pageSettings: {
              currentPage: 1,
              pageSize: 1000,
              pageSizes: [20, 50, 100, 200, 1000],
              totalRecordsCount: 0
            }
          }
        },
        {
          title: this.$t('历史报价'),
          toolbar: [],
          useToolTemplate: false,
          gridId: this.$md5(
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'pricing'
            ]['history'] + this.$route?.query?.rfxId
          ),
          // 注释掉的gridIds 是方便查看md5加密之前的数据
          // gridIds:
          //   this.$permission.gridId["purchase"][this.$route?.query?.source][
          //     "detail"
          //   ]["tabs"]["pricing"]["history"] + this.$route?.query?.rfxId,
          grid: {
            lineIndex: 0,
            allowFiltering: true,
            columnData: [],
            dataSource: []
          }
        }
      ],
      dictItems: [],
      taxList: [], // 税率编码
      currencyList: [], // 货币名称
      purUnitList: [], // 采购单位
      priceClassificationList: [
        { text: this.$t('暂估价格'), value: 'predict_price' },
        { text: this.$t('SRM价格'), value: 'srm_price' },
        { text: this.$t('执行价格'), value: 'execute_price' },
        { text: this.$t('基价'), value: 'basic_price' }
      ],
      oldList: [], //接口数据与编辑后数据进行比较
      siteNameList: [], //工厂列表
      childItemList: [], //子集List
      currentEditData: null, //当前编辑的数据
      currentTabId: 0,
      currentTab: -1,
      requestUrls: {},
      downTemplateParams: {
        rfxId: this.$route.query.rfxId
      },
      txCompanyList: ['2M01', '2S06']
    }
  },
  watch: {
    detailInfo: {
      handler() {
        this.initToolbar()
      },
      deep: true
    }
  },
  computed: {
    allowEditing() {
      return canEdit(this.detailInfo?.transferStatus)
    }
  },
  activated() {
    this.currentTab = 0
  },
  deactivated() {
    this.currentTab = -1
  },
  async mounted() {
    await this.getTxCompanyList()
    combination.mounted(this)
    this.initToolbar()
    sessionStorage.removeItem('siteParam')
    sessionStorage.removeItem('dieItemParam')
    sessionStorage.removeItem('temp_rfxId')
    sessionStorage.removeItem('temp_rfxItemId')
    await this.initDictItems()
    await this.initTaxList()
    await this.initCurrencyList()
    await this.initPurUnitList()
    await this.initSiteList()
    let _quotaColumns = this.processQuotaData()
    this.$API.strategyConfig
      .findByRfxId({
        sourcingMode: this.$route.query.source,
        rfxId: this.$route.query.rfxId
      })
      .then((res) => {
        this.allocationRange = res.data.strategyConfigList[0].allocationRange
        if (this.allocationRange == 1) {
          // 插入 “配额分配” tab
          this.pageConfig.splice(1, 0, {
            title: this.$t('配额分配'),
            toolbar: tabToolbar(this.$route.query.status),
            useToolTemplate: false,
            grid: {
              customSelection: true,
              showSelected: false,
              virtualPageSize: 30,
              enableVirtualization: true,
              lineIndex: true,
              pageSettings: {
                currentPage: 1,
                pageSize: 1000,
                pageSizes: [20, 50, 100, 200, 1000],
                totalRecordsCount: 0
              },
              allowFiltering: true,
              columnData: _quotaColumns,
              dataSource: [],
              rowDataBound: (args) => {
                if (args.data.isGray == 1) {
                  args.row.classList.add('backgroundRed')
                }
              },
              editSettings: {
                allowAdding: false,
                allowEditing: true,
                allowDeleting: false,
                mode: 'Normal', // 默认normal模式
                allowEditOnDblClick: true,
                showConfirmDialog: false,
                showDeleteConfirmDialog: false
              }
            }
          })
          // 配额明细Tab，按钮状态，和报价明细Tab保持一致。
          this.pageConfig[1].toolbar = tabToolbar(
            this.$route.query.status,
            this.detailInfo.transferStatus,
            'quota'
          )
        }
        this.queryRfxConfig()
      })
    const statusList = [20, 32, 33, 34, 37, 100]
    if (
      this.$route?.query?.source !== 'rfq' &&
      statusList.includes(this?.detailInfo?.transferStatus)
    ) {
      this.pageConfig[0].toolbar.push({
        id: 'signPreview',
        icon: 'icon_table_filter',
        title: this.$t('开标一览表')
      })
    }
    let _this = this
    this.$bus.$on('refreshPricingTab', () => {
      _this.$refs.templateRef.refreshCurrentGridData()
    })
    this.currentTab = 0
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    handleSelectTab(e) {
      this.currentEditData = null
      this.currentTabId = e
    },
    //单元格icons点击事件
    handleClickCellTool() {},
    show() {
      this.$dialog({
        modal: () => import('../../../components/comparativePriceDialog.vue'),
        data: {
          rfxId: this.$route.query.rfxId,
          detailInfo: this.detailInfo
        }
      })
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
    initToolbar() {
      // 判断是否报价明细
      // if (Number(this.allocationRange) !== 1) { // detailInfo改变后需要重置按钮状态
      this.pageConfig[0].toolbar = tabToolbar(
        this.$route.query.status,
        this.detailInfo.transferStatus
      )
      // 新需求--招标和竞价 隐藏此按钮:startNewRound
      if (
        this.$route?.query?.source == 'invite_bids' ||
        this.$route?.query?.source == 'bidding_price'
      ) {
        let toolbar = this.pageConfig[0].toolbar
        toolbar.forEach((item, index) => {
          if (item.id == 'startNewRound') {
            this.pageConfig[0].toolbar.splice(index, 1)
          }
        })
        // 招标竞价添加导出功能
        toolbar.push({
          id: 'export',
          icon: 'icon_solid_pushorder',
          title: this.$t('导出')
        })
      }
      // }
    },
    // 初始化字典数据
    async initDictItems() {
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    async initTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 币种编码
    async initCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency().catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 采购单位
    async initPurUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.purUnitList = res?.data?.records || [] // FIXME 采购单位和基本单位的区别
      })
    },
    async initSiteList() {
      const res = await this.$API.masterData.permissionSiteList({
        buOrgId: sessionStorage.getItem('selectOrgId'),
        companyId: sessionStorage.getItem('selectCompanyId')
      })
      if (res) {
        this.siteNameList = res?.data || []
      }
    },
    queryRfxConfig() {
      if (this.allocationRange == 1) {
        this.initConfig(0, this.$API.rfxPricing.getRfxPricingList)
        this.initConfig(1, this.$API.rfxPricing.getRfxItemQuotaList, 'rfxId', false) //配额分配Tab下，只渲染父级结构
        this.initConfig(2, this.$API.rfxPricing.getRfxPricingLists)
      } else {
        this.initConfig(0, this.$API.rfxPricing.getRfxPricingList)
        this.initConfig(1, this.$API.rfxPricing.getRfxPricingLists)
      }
    },
    initConfig(index, url, field = 'rfx_item.rfxHeaderId', haveChild = 'true') {
      let parentColumns = null
      if (Array.isArray(this.fieldDefines) && this.fieldDefines.length) {
        let arr = this.defineGridColumns(utils.cloneDeep(this.fieldDefines))
        if (this.detailInfo.rfxGeneralType === 2) {
          const priceUnitField = ['priceUnitName', 'biddingItemDTO.priceUnitName']
          const priceUnitCol = arr.find((item) => priceUnitField.includes(item.field))
          if (priceUnitCol) {
            priceUnitCol.valueAccessor = (field, data) => {
              // 非采
              const purUnitNameDataSource = [
                { text: this.$t('元'), value: '1' },
                { text: this.$t('万元'), value: '0.0001' }
              ]
              const item = purUnitNameDataSource.find((el) => el.value === data.priceUnitName)
              return item ? item.text : ''
            }
          }
        }
        if (this.allocationRange == 1 && index === 1) {
          // 配额分配范围 allocationRange 下拉框 当前寻源单：0 所有有效配额：1
          this.pageConfig[index].grid.columnData = this.processQuotaData()
        } else if (index == 0) {
          this.pageConfig[index].grid.columnData = arr
        } else {
          // 历史报价Tab页，移除‘信息记录’字段
          let _arr = utils.cloneDeep(arr)
          let infoRecordIndex = _arr.findIndex((e) => e.field == 'infoRecord')
          if (infoRecordIndex > -1) _arr.splice(+infoRecordIndex, 1)
          this.pageConfig[index].grid.columnData = _arr
        }
        // 添加isPrimaryKey
        arr.push({
          field: 'id',
          width: 0,
          isPrimaryKey: true,
          visible: false
        })
        parentColumns = utils.cloneDeep(arr)
      }
      if (Array.isArray(this.childFields) && this.childFields.length && haveChild) {
        let arr = this.defineGridColumns(utils.cloneDeep(this.childFields), true)
        let isHierarchy = this.detailInfo.sourcingObjType === 'HIERARCHY' ? true : false
        !isHierarchy && arr.shift() //部品模具，子集数据允许勾选 其他子级数据 不允许勾选
        let _this = this
        let allowEditing = index == 0 //只有报价明细Tab 支持编辑
        let _w = setChildWidth(arr, 200, parentColumns)
        this.$set(this.pageConfig[index].grid, 'detailTemplate', function () {
          return {
            template: Vue.component('detailTemplate', {
              template: `<div style="padding:10px 0;width:${_w};">
                          <mt-template-page
                            ref="childRef"
                            class="childTemplate "
                            :template-config="childTable"
                             @actionComplete="subActionComplete"
                             @rowSelected="rowSelected"
                             @rowDeselected="rowDeselected"
                          ></mt-template-page>
                        </div>`,
              data: function () {
                return {
                  data: {},
                  childTable: [
                    {
                      grid: {
                        height: 'auto',
                        lineIndex: true,
                        allowPaging: false,
                        dataSource: [],
                        columnData: arr,
                        allowEditing,
                        editSettings: {
                          allowAdding: false,
                          allowEditing: true,
                          allowDeleting: false,
                          mode: 'Normal', // 默认normal模式
                          allowEditOnDblClick: true,
                          showConfirmDialog: false,
                          showDeleteConfirmDialog: false
                        },
                        recordDoubleClick: this.subDoubleClick,
                        class: 'pe-edit-grid custom-toolbar-grid'
                      }
                    }
                  ]
                }
              },
              mounted() {
                let data = this.data
                console.log('detail-table', data)
                setTimeout(() => {
                  this.childTable[0].grid.dataSource = data?.childItems
                }, 0)
                this.$bus.$on(`subPricingGridEndEdit-${data.id}`, () => {
                  let _childRef = this?.$refs?.childRef
                  let _current = _childRef?.getCurrentTabRef()
                  if (_current?.grid) {
                    _current?.grid.endEdit()
                  }
                })
              },
              beforeDestroy() {
                this.$bus.$off(`subPricingGridEndEdit-${this.data.id}`)
              },
              methods: {
                subActionComplete(e) {
                  if (e.requestType == 'save') {
                    this.$set(
                      this.$refs.childRef.getCurrentTabRef().grid.dataSource,
                      e.rowIndex,
                      e.data
                    )
                    let _parent = _this.$refs.templateRef.getCurrentTabRef().grid.dataSource
                    let _findIndex = _parent.findIndex((e) => e.id === this.data.id)
                    _parent[_findIndex]['childItems'][e.rowIndex] = e.data
                    // if (
                    //   e.previousData?.itemDieResponse?.dieFormalCode !==
                    //   e.data?.itemDieResponse?.dieFormalCode
                    // ) {
                    //   //如果更新了数据dieFormalCode， 需要同步其他对应的子级数据也更新dieFormalCode
                    //   //举例，如果更新了第3行的dieFormalCode，则其他子级的第3行。也更新dieFormalCode
                    //   _parent.forEach((_p) => {
                    //     if (Array.isArray(_p.childItems) && _p.childItems.length > e.rowIndex) {
                    //       _p.childItems[e.rowIndex]['itemDieResponse']['dieFormalCode'] =
                    //         e.data?.itemDieResponse?.dieFormalCode
                    //     }
                    //   })
                    // }
                    _this.$set(
                      _this.$refs.templateRef.getCurrentTabRef().grid,
                      'dataSource',
                      _parent
                    )
                  }
                },
                subDoubleClick() {
                  _this.handleParentEndEdit(this.data.id)
                },
                rowSelected(args) {
                  let _data = args.isHeaderCheckboxClicked ? args.data : [args.data]
                  _data.forEach((item) => {
                    _this.childItemList.push(item)
                  })
                },
                rowDeselected(args) {
                  let _selectGridRecords = this.$refs.childRef
                    .getCurrentTabRef()
                    .grid.getCurrentViewRecords()
                  let _data = args.isHeaderCheckboxClicked ? _selectGridRecords : [args.data]

                  let ids = _data.map((item) => {
                    return item.id
                  })
                  _this.childItemList = _this.childItemList.filter((item) => {
                    let flag = !ids.includes(item.id)
                    return flag
                  })
                }
              }
            })
          }
        })
      }
      this.$set(this.pageConfig[index].grid, 'asyncConfig', {
        url,
        defaultRules: [
          {
            label: '',
            field: field,
            type: 'number',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        afterAsyncData: (res) => {
          this.oldList = utils.cloneDeep(res.data.records)
        },
        serializeList: (list) => {
          this.serializeGridList(list)
          return list
        }
      })
    },
    // 配额列表字段根据招标询报价区分
    processQuotaData() {
      // 如果是询价大厅 隐藏名次字段
      if (this.$route?.query?.source === 'rfq') {
        const _columns = quotaColumnData(this).filter((item) => item.field !== 'ranking')
        return _columns
      }
      return quotaColumnData(this)
    },
    serializeGridList(list) {
      const { currentPage, pageSize } =
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.pageSettings
      list.forEach((v, index) => {
        v.index = (currentPage - 1) * pageSize + index
        this.defineUnuseRateValue(v)
        v.allocationRatio = (v.allocationRatio * 100).toFixed(0)
        v.declinePercent = v.declinePercent ? (v.declinePercent * 100).toFixed(2) : ''
        v.drawing = v.fileList ? JSON.stringify(v.fileList) : null //单独处理附件字段
        v.supplierDrawing = v.supplierFileList ? JSON.stringify(v.supplierFileList) : null //单独处理"供应商附件"字段
        v.stepQuoteName =
          v.itemStageList && v.itemStageList.length > 0 ? JSON.stringify(v.itemStageList) : null //单独处理阶梯报价
        v.stepNum = v.stepValue
        let _subItems = v['childItems']
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })
      let arr = []
      list.forEach((x) => {
        arr.push(
          JSON.stringify({
            itemCode: x.itemCode,
            siteCode: x.siteCode
          })
        )
      })
      arr = [...new Set(arr)]
      arr = arr.filter((item, index) => index % 2 !== 0)
      list.forEach((x) => {
        arr.forEach((v) => {
          if (x.itemCode == JSON.parse(v).itemCode && x.siteCode == JSON.parse(v).siteCode) {
            x.isGray = true
          }
        })
        // 添加成本分析操作列
        if (x.costModelName && x.costModelQuote === 1) {
          x.costAnalysis = this.$t('成本分析')
        }
      })
      return list
    },

    // 列表中，税率名称、税率编码，值无效
    defineUnuseRateValue(v) {
      if (v?.bidTaxRateCode && /^\d{19}$/.test(v.bidTaxRateCode)) {
        let _find = this.taxList.find((e) => e.id === v.bidTaxRateCode)
        if (_find && _find.id) {
          v.bidTaxRateCode = _find.taxItemCode
          v.bidTaxRateName = _find.taxItemName
        }
      }
    },
    defineGridColumns(fieldDefines, isChild = false) {
      let arr = []
      if (this.$route.query.source == 'rfq' && isChild) {
        arr = [
          {
            width: '50',
            type: 'checkbox',
            allowEditing: false
          }
        ]
      }
      const editInstance = createEditInstance()
        .component('checkbox', editCheckbox)
        .component('itemDieInput', itemDieInput)
      const rfxItemLogisticsColumn = logisticsColumnData({
        prefix: 'itemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const biddingItemLogisticsColumn = biddingItemLogisticsColumnData({
        prefix: 'biddingItemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const sBIFields = getSBIFields({
        currencyList: this.currencyList,
        taxList: this.taxList,
        purUnitList: this.purUnitList
      })

      if (sBIFields.bidPurUnitName?.editConfig) {
        const dataSource = this.purUnitList
        sBIFields.bidPurUnitName.editConfig = {
          type: 'select',
          valueConvert: (val, { options, column }) => {
            const dataSource = options.dataSource || []
            const row = dataSource.find((e) => e.id === val)
            const purUnitName = row?.unitName || ''
            editInstance.setValueByField(column.field, purUnitName)
            return purUnitName
          },
          props: {
            ...sBIFields.bidPurUnitName.editConfig.props,
            fields: { value: 'id', text: '__text' },
            created: () => {
              let row
              const bidPurUnitCode = editInstance.getValueByField('biddingItemDTO.bidPurUnitCode')
              const bidPurUnitName = editInstance.getValueByField('biddingItemDTO.bidPurUnitName')
              if (bidPurUnitCode) {
                row = dataSource.find((e) => e.unitCode === bidPurUnitCode)
              } else {
                row = dataSource.find((e) => e.unitName === bidPurUnitName)
              }
              if (row) {
                setTimeout(() => {
                  editInstance.setValueByField('biddingItemDTO.bidPurUnitName', row.id)
                }, 0)
              }
            }
          }
        }
      }
      const itemDieMapColumn = itemDieMapColumnData({
        prefix: 'itemDieResponse.',
        handleSelectChange: () => {}
      })
      const itemExtMapColumn = itemExtMapColumnData({
        prefix: 'itemExtMap.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      fieldDefines.forEach((e2) => {
        let name = ''
        if (e2.tableName == 'rfx_item_ext') {
          name = 'itemExtMap'
          const field = `${name}.${e2.fieldCode}`
          // 通用信息显示
          const defColumn = itemExtMapColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: '120',
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter,
            required: e2.required
          })
        } else if (e2.tableName == 'rfx_item_die') {
          name = 'itemDieResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = itemDieMapColumn.find((e) => e.field === field)
          if (
            field !== 'itemDieResponse.dieFormalCode' &&
            field !== 'itemDieResponse.basicDieCode'
          ) {
            arr.push({
              field,
              headerText: this.$t(e2.fieldName),
              allowEditing: false,
              width: '200',
              required: e2.required,
              editConfig: defColumn?.editConfig,
              formatter: defColumn?.formatter,
              edit: defColumn
                ? editInstance.create({
                    getEditConfig: () => ({
                      type: defColumn.editConfig.type,
                      ...defColumn.editConfig,
                      disabled: true,
                      readonly: true
                    })
                  })
                : undefined
            })
          } else if (field === 'itemDieResponse.basicDieCode') {
            arr.push({
              field,
              headerText: this.$t(e2.fieldName),
              allowEditing: true,
              required: e2.required,
              width: '200'
            })
          } else {
            arr.push({
              field,
              headerText: this.$t(e2.fieldName),
              allowEditing: true,
              width: '200',
              // editConfig: defColumn?.editConfig,
              required: e2.required,
              formatter: defColumn?.formatter,
              edit: editInstance.create({
                onChange: () => {
                  this.handleSaveEvent()
                },
                getEditConfig: () => ({
                  type: 'itemDieInput',
                  field: 'itemDieResponse.dieFormalCode'
                })
              })
            })
          }
          // arr.push({
          //   field: `${name}.${e2.fieldCode}`,
          //   headerText: this.$t(e2.fieldName),
          //   allowEditing: false,
          //   width: "200",
          // });
        } else if (e2.tableName == 'rfx_item_logistics') {
          name = 'itemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = rfxItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: '200',
            required: e2.required,
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName === 'rfx_bidding_item_logistics') {
          name = 'biddingItemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = biddingItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: '200',
            required: e2.required,
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName === 'rfx_bidding_item') {
          name = ''
          const row = {
            formatter: sBIFields?.[e2.fieldCode]?.formatter,
            editConfig: sBIFields?.[e2.fieldCode]?.editConfig,
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            allowEditing: [
              'allocationQuantity',
              'quoteEffectiveStartDate',
              'quoteEffectiveEndDate'
            ].includes(e2.fieldCode)
              ? true
              : false, // 配额数量、报价有效期从、报价有效期至可编辑
            width: '200',
            required: e2.required
          }
          const readonly = !row.allowEditing
          row.edit = sBIFields?.[e2.fieldCode]
            ? editInstance.create({
                // 针对日期字段添加专门的验证逻辑
                onInput: (ctx, { value }) => {
                  if (e2.fieldCode === 'quoteEffectiveStartDate' && value) {
                    // 设置结束日期的最小值为开始日期
                    ctx.setOptions('quoteEffectiveEndDate', {
                      min: new Date(Math.max(new Date(value).getTime(), Date.now()))
                    })
                  } else if (e2.fieldCode === 'quoteEffectiveEndDate' && value) {
                    // 设置开始日期的最大值为结束日期
                    ctx.setOptions('quoteEffectiveStartDate', {
                      max: new Date(new Date(value).getTime())
                    })
                  }

                  // 自动带出报价有效期至的逻辑
                  if (e2.fieldCode === 'quoteEffectiveStartDate' && value) {
                    const companyCode = this.detailInfo?.companyCode

                    // 1503、0602公司的自动设置逻辑
                    if (['1503', '0602'].includes(companyCode)) {
                      var date = new Date()
                      var year = date.getFullYear()
                      var yearTwo = date.getFullYear() + 1
                      let datae = year + '-' + '6' + '-' + '30'
                      let dateTwo = year + '-' + '7' + '-' + '1'
                      if (new Date(value).getTime() >= new Date(dateTwo).getTime()) {
                        ctx.setValueByField(
                          'quoteEffectiveEndDate',
                          yearTwo + '-' + '12' + '-' + '31'
                        )
                      } else if (new Date(value).getTime() <= new Date(datae).getTime()) {
                        ctx.setValueByField('quoteEffectiveEndDate', year + '-' + '12' + '-' + '31')
                      }
                    }

                    // 2M01、2S06公司的自动设置逻辑
                    if (this.txCompanyList.includes(companyCode)) {
                      const isNonProcurement = this.detailInfo?.rfxGeneralType === 2
                      if (isNonProcurement) {
                        // 非采：报价有效期至为报价有效期从的半年后日期
                        const startDate = new Date(value)
                        const endDate = new Date(startDate)
                        endDate.setMonth(startDate.getMonth() + 6)
                        const formattedEndDate =
                          endDate.getFullYear() +
                          '-' +
                          String(endDate.getMonth() + 1).padStart(2, '0') +
                          '-' +
                          String(endDate.getDate()).padStart(2, '0')
                        ctx.setValueByField('quoteEffectiveEndDate', formattedEndDate)
                      } else {
                        // 通采：按原有逻辑设置为2099-12-31
                        ctx.setValueByField('quoteEffectiveEndDate', '2099-12-31')
                      }
                    }

                    // 其他公司的默认逻辑：如果没有特殊处理，设置为2099-12-31
                    if (!['1503', '0602', ...this.txCompanyList].includes(companyCode)) {
                      ctx.setValueByField('quoteEffectiveEndDate', '2099-12-31')
                    }
                  }
                },
                getEditConfig: (context) => {
                  const baseConfig = {
                    type: sBIFields?.[e2.fieldCode]?.editConfig.type,
                    ...sBIFields?.[e2.fieldCode]?.editConfig.props,
                    readonly,
                    enabled: readonly
                  }

                  // 如果原字段有 getEditConfig，则合并其配置
                  if (sBIFields[e2.fieldCode].edit?.getEditConfig) {
                    const originalConfig = sBIFields[e2.fieldCode].edit.getEditConfig(context)
                    return {
                      ...baseConfig,
                      ...originalConfig,
                      readonly,
                      enabled: readonly
                    }
                  }

                  return baseConfig
                }
              })
            : undefined
          arr.push(row)
        } else if (e2.fieldCode === 'infoRecord') {
          arr.push({
            field: 'infoRecord',
            headerText: this.$t('信息记录'),
            cssClass: 'field-content',
            allowEditing: false,
            required: e2.required,
            valueConverter: {
              type: 'placeholder',
              placeholder: this.$t('信息记录')
            }
          })
        } else if (e2.tableName === 'rfx_file') {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            required: e2.required,
            template: function () {
              return {
                template: e2.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
              }
            },
            editTemplate: function () {
              return {
                template: e2.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
              }
            },
            width: 150,
            allowEditing: false
          })
        } else {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: e2.fieldCode === 'lineNo' ? '80' : '200',
            required: e2.required
          })
        }
      })
      let that = this
      arr.forEach((e) => {
        if (e.required) {
          e.headerTemplate = () => {
            return {
              template: Vue.component('requiredCell', {
                template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                data() {
                  return {
                    data: {},
                    fieldName: ''
                  }
                },
                mounted() {
                  this.fieldName = e.headerText
                }
              })
            }
          }
        }

        if (e.field === 'allocationRatio') {
          if (this.allocationRange === 1) {
            e.allowEditing = false
          } else {
            e.allowEditing = true
            e.editType = 'number'
            e.edit = {
              params: {
                min: 0,
                max: 100,
                precision: '0'
              }
            }
          }
          e.formatter = ({ field }, item) => {
            let val = item[field] ? Number(item[field]) : 0
            return val + '%'
          }
        } else if (e.field === 'priceClassification') {
          e.allowEditing = true
          e.editType = 'dropdownedit'
          e.formatter = ({ field }, item) => {
            const cellVal = item[field]
            return this.priceClassificationList.find((e) => e.value === cellVal)?.text
          }
          e.edit = {
            params: {
              dataSource: this.priceClassificationList,
              fields: { value: 'value', text: 'text' },
              query: new Query()
            }
          }
        } else if (['distributionQuantity', 'allocationQuantity', 'freightFee'].includes(e.field)) {
          e.allowEditing = true
          e.editType = 'numericedit'
          e.edit = {
            params: {
              min: 0,
              validateDecimalOnType: true
            }
          }
        }
        // else if (e.field === "itemDieResponse.dieFormalCode") {
        //   e.allowEditing = true;
        //   // e.editTemplate = () => {
        //   //   return {
        //   //     template: selectDieItemCode,
        //   //   };
        //   // };
        // }
        else if (
          ['costModelName', 'costModelId', 'costModelCode', 'costModelVersionCode'].includes(
            e.field
          )
        ) {
          e.width = 0
          // e.cellTools = [
          //   {
          //     id: 'editCost',
          //     title: this.$t('报价'),
          //     visibleCondition: (data) => {
          //       return data['costModelName']
          //     }
          //   }
          // ]
        } else if (e.field == 'costModelQuote') {
          e.valueConverter = {
            type: 'map',
            map: { 0: this.$t('否'), 1: this.$t('是') }
          }
          e.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'select',
              readonly: true,
              disabled: true,
              dataSource: [
                { text: this.$t('否'), value: 0 },
                { text: this.$t('是'), value: 1 }
              ],
              fields: { value: 'value', text: 'text' },
              editInstance
            })
          })
        } else if (e.field === 'stepQuote') {
          e.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 1 ? that.$t('是') : that.$t('否')
          }
          e.allowEditing = false
          e.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('否'),
              1: this.$t('是')
            }
          }
        } else if (e.field === 'stepQuoteType') {
          e.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 0
              ? this.$t('数量累计阶梯')
              : cellVal === 3
              ? this.$t('数量逐层阶梯')
              : ''
          }
          e.allowEditing = false
          e.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('数量累计阶梯'),
              3: this.$t('数量逐层阶梯')
            }
          }
        } else if (e.field === 'vmi') {
          e.allowEditing = true
          e.template = () => {
            return {
              template: checkbox
            }
          }
          e.edit = editInstance.create({
            getEditConfig: () => ({
              type: 'checkbox',
              editInstance
            })
          })
        } else if (e.field === 'costAnalysis') {
          e.cssClass = 'field-content'
        } else if (e.field === 'ckdMaterialCode') {
          e.allowEditing = true
          e.editTemplate = () => {
            return {
              template: selectCkdItemCode
            }
          }
        } else if (e.field === 'ckdPrice') {
          e.allowEditing = false
          e.editTemplate = () => {
            return {
              template: cellChanged
            }
          }
        }
      })
      // 存在临时物料，则物料选择框，支持操作
      let haveTempItemCode = arr.some((e) => e.field === 'temporaryItemCode')
      if (haveTempItemCode && [34].includes(this?.detailInfo?.transferStatus)) {
        // 存在临时物料编码temporaryItemCode，且单据状态为‘定标中’ 可以编辑正式物料
        arr.forEach((e) => {
          if (e.field === 'itemCode') {
            e.allowEditing = true
            e.sourcingType = this.detailInfo.sourcingType
            e.editTemplate = () => {
              return {
                template: selectedItemCode
              }
            }
          }
          if (e.field === 'itemName') {
            e.allowEditing = false
            e.editTemplate = () => {
              return {
                template: cellChanged
              }
            }
          }
        })
      }
      // // 存在临时模具编码，则正式模具编码选择框，支持操作
      // let haveTempDieItemCode = arr.some(
      //   (e) => e.field === "itemDieResponse.dieTempCode"
      // );
      // if (haveTempDieItemCode) {
      //   arr.forEach((e) => {
      //     if (e.field === "itemDieResponse.dieFormalCode") {
      //       e.allowEditing = true;
      //       e.editTemplate = () => {
      //         return {
      //           template: selectDieItemCode,
      //         };
      //       };
      //     }
      //   });
      // }
      arr.push(
        {
          field: 'bidTimes',
          headerText: this.$t('次数'),
          width: '90'
        },
        {
          field: 'roundNo',
          headerText: this.$t('轮次'),
          width: '90'
        }
      )
      return combination.defineGridColumnsAfter(this, arr, isChild, editInstance)
    },
    actionComplete(e) {
      if (e.requestType == 'beginEdit') {
        let _row = e.rowData
        if (!_row?.siteCode) {
          sessionStorage.removeItem('siteParam')
          sessionStorage.removeItem('temp_rfxId')
          sessionStorage.removeItem('temp_rfxItemId')
          return
        }
        let _findSite = this.siteNameList.find((e) => e.orgCode === _row.siteCode)
        if (_findSite) {
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: _findSite['id'],
              siteCode: _row.siteCode
            })
          )
        }
        this.minSplitQuantityCache = e.rowData.minSplitQuantity
        if (!_row?.itemCode) {
          sessionStorage.setItem(
            'dieItemParam',
            JSON.stringify({
              itemCode: _row?.itemCode
            })
          )
        } else {
          sessionStorage.removeItem('dieItemParam')
        }
        sessionStorage.setItem('temp_rfxId', this.$route.query.rfxId)
        sessionStorage.setItem('temp_rfxItemId', _row['rfxItemId'])
      }
      if (e.requestType == 'save') {
        this.$set(this.$refs.templateRef.getCurrentTabRef().grid.dataSource, e.rowIndex, e.data)
        if (e.data.minSplitQuantity != this.minSplitQuantityCache) {
          const grid = this.$refs.templateRef.getCurrentTabRef().grid
          let dataSource = grid.$options.propsData.dataSource
          let itemCode = e.data.itemCode
          if (itemCode) {
            dataSource.forEach((item) => {
              if (item.itemCode == itemCode) {
                item.minSplitQuantity = e.data.minSplitQuantity
              }
            })
            grid.refresh() //刷新表格
          }
        }
      }
    },
    handleClickToolBar(e) {
      let _selectGridRecords = []
      e.gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          _selectGridRecords.push(item)
        }
      })
      let ids = []
      if (_selectGridRecords.length) {
        ids = _selectGridRecords.map((item) => {
          return item.id
        })
      }
      if (e.toolbar.id == 'rejectBargain') {
        this.rejectBargain()
      } else if (e.toolbar.id == 'signPreview') {
        // 开标一览表 不需要勾选数据行
        this.$emit('preview')
        return
      } else if (e.toolbar.id == 'submit') {
        this.handleSubmitOA(e)
        return
      } else if (e.toolbar.id == 'redirectOA') {
        this.handleRedirectOaLink()
        return
      } else if (e.toolbar.id == 'startNewRound') {
        this.handleStartNewRound()
      } else if (e.toolbar.id == 'Save') {
        this.handleSave(e)
      } else if (e.toolbar.id == 'relative') {
        this.show()
      } else if (e.toolbar.id == 'batchVmi') {
        this.batchVmi(ids)
      } else if (e.toolbar.id == 'export') {
        this.handleExcelExport()
      } else if (e.toolbar.id === 'exportPricing') {
        this.handleExcelExport('pricing')
      } else if (e.toolbar.id === 'createMoldBuildTable') {
        if (this.detailInfo.sourcingObjType === 'HIERARCHY') {
          let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
          let _flag = true
          this.childItemList?.map((item) => {
            if ([1, 2].includes(item.itemDieResponse.dieType)) {
              let _find = dataSource.find((x) => x.rfxItemKey === item.parentRfxItemKey)
              if (_find && [3, 4].includes(_find.dieType)) _flag = false
            }
          })
          if (!_flag) {
            this.$toast({ content: this.$t('改模情况不允许勾选基础模/复制模'), type: 'warning' })
            return
          }

          ids = this.childItemList.map((item) => {
            return item.id
          })
        }
        this.handleCreateMoldBuildTable(ids)
      } else if (e.toolbar.id === 'upload') {
        this.requestUrls = {
          templateUrlPre: 'rfxPricing',
          templateUrl: 'quotaExport',
          uploadUrl: 'quotaImport',
          rfxId: this.$route.query.rfxId
        }
        this.showUploadExcel(true)
      } else if (e.toolbar.id === 'download') {
        this.handleQuotaExport()
      }
    },
    // 批量VMI
    batchVmi(ids) {
      if (!ids?.length) return
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行“批量VMI”操作？')
        },
        success: () => {
          this.$API.comparativePrice
            .batchSetVmi({
              rfxId: this.rfxId,
              rfxBiddingItemIds: ids
            })
            .then(() => {
              this.$toast({ content: this.$t('操作成功！'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    },
    //驳回议价
    rejectBargain() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'驳回议价'操作？")
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.rfxPricing
            .toEvaluationBid(query)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$router.go(0)
              // this.$emit("goToTab", 9); //跳转到评标tab
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    //开启下一轮
    handleStartNewRound() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'发起新一轮'操作？")
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.comparativePrice.startNewRound(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.$router.go(0)
            // this.$parent.getDetail();
            // this.$refs.templateRef.refreshCurrentGridData();
          })
        }
      })
    },
    // 生成模具制造审批表
    handleCreateMoldBuildTable(ids) {
      if (ids.length <= 0) {
        this.$toast({ content: this.$t('请选择模具明细数据'), type: 'warning' })
        return
      }
      const query = {
        rfxBiddingItemIds: ids,
        rfxId: this.$route.query.rfxId
      }
      this.$store.commit('startLoading')
      this.$API.rfxPricing
        .createMoldBuildTable(query)
        .then(() => {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleRedirectOaLink() {
      //ApproveType  暂时处理：定标/定价页面
      //业务类型 0招标竞价定标 1询报价定价 2商务标晋级 3提交立项
      let approveType = 0
      if (this.$route?.query?.source == 'rfq') {
        approveType = 1
      }
      let params = {
        approveType,
        rfxId: this.$route.query.rfxId
      }
      console.log('跳转OA-link', params)
      this.$API.rfxExt.getOaLink(params).then((res) => {
        console.log('OA-link  返回数据', res)
        if (res?.data && res.data.indexOf('.com')) {
          window.open(res.data)
        }
      })
    },
    // 保存操作
    handleSave(e) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行保存操作？`)
        },
        success: () => {
          this.handleSaveEvent(e)
        }
      })
    },
    handleSaveEvent() {
      this.endEdit()
      let e = this.$refs.templateRef.getCurrentTabRef()
      let dataSource = e.grid.dataSource
      const _list = this.serializeSaveParams(utils.cloneDeep(dataSource))
      this.handleSaveAPI(_list, e.tabIndex)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('操作成功.'),
              type: 'success'
            })
          }
        })
        .finally(() => {
          this.$refs.templateRef.refreshCurrentGridData()
        })
    },
    // 弹框展示
    async showDialog(message, cssClass) {
      return new Promise((resolve) => {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t(message),
            cssClass
          },
          success: resolve
        })
      })
    },
    // 提交OA
    async handleSubmitOA(e) {
      let dataSource = e.grid.dataSource
      let expand = this.detailInfo.rfxHeaderExpandResponseList
      let flag_expand = expand && expand?.length // 存在拓展工厂
      let flag_table = JSON.stringify(dataSource) === JSON.stringify(this.oldList) // 接口数据与本地数据一致
      let flag_price = false // true:涨价 false：未涨价
      const res = await this.$API.rfxPricing.submitPricingValid({
        rfxId: this.$route.query.rfxId
      })
      if (res.code === 200 && res.data) flag_price = true
      if (flag_expand && flag_table && !flag_price) {
        // 直接调用保存接口
        this.handleSubmitFn(e)
        return
      }
      if (!flag_expand && this.detailInfo.rfxGeneralType !== 2)
        await this.showDialog('工厂扩展为空，请确认！') // 不存在拓展工厂提示(非采不用校验)
      if (!flag_table) await this.showDialog('当前表格内容未保存，是否继续？') // 表格是否保存提示
      if (flag_price) await this.showDialog(res.msg, 'rise-price') // 涨价
      this.handleSubmitFn(e)
    },
    // 提交
    handleSubmitFn(e) {
      this.endEdit()
      let dataSource = e.grid.dataSource
      const _list = this.serializeSaveParams(utils.cloneDeep(dataSource))
      this.handleSaveAPI(_list, e.tabIndex).then((r) => {
        if (r.code == 200) {
          let params = {
            rfxId: this.$route.query.rfxId
          }
          this.$API.rfxPricing
            .getRfxPricingSubmit(params)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .finally(() => {
              this.$emit('reGetDetail')
            })
        }
      })
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      _current?.grid.hideSpinner()
      this.handleChildEndEdit()
    },
    handleParentEndEdit(id = null) {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
      this.handleChildEndEdit(id)
    },
    recordDoubleClick() {
      this.handleChildEndEdit()
    },
    handleChildEndEdit(id = null) {
      this.oldList.forEach((data) => {
        if (data.id !== id) {
          this.$bus.$emit(`subPricingGridEndEdit-${data.id}`)
        }
      })
    },
    handleSaveAPI(dataSource, tabIndex) {
      if (dataSource.length) {
        let quotaDTOList = []
        dataSource.forEach((e, index) => {
          console.log('allocationRatio1', e.allocationRatio)
          // 配额字段=0，也可以提交。
          if (e.allocationRatio > 0) {
            e.allocationRatio = (e.allocationRatio / 100).toFixed(2)
          }
          console.log('allocationRatio2', e.allocationRatio)
          if (e?.allocationRatio >= 0) {
            if (tabIndex == 0) {
              // if (!e?.itemDieResponse?.dieFormalCode) {
              //   this.$toast({
              //     content: this.$t(`正式模具编码不能为空`),
              //     type: "error",
              //   });
              //   throw new Error(this.$t(`正式模具编码不能为空`));
              // }
              quotaDTOList.push({
                allocationQuantity: e.allocationQuantity,
                rfxBiddingItemId: e.id,
                allocationRatio: e.allocationRatio,
                priceClassification: e.priceClassification,
                minSplitQuantity: e.minSplitQuantity,
                dieFormalCode: e?.itemDieResponse?.dieFormalCode,
                basicDieCode: e?.itemDieResponse?.basicDieCode,
                domesticPrice: e?.domesticPrice,
                ckdMaterialCode: e?.ckdMaterialCode,
                ckdPrice: e?.ckdPrice,
                freightFee: e?.freightFee,
                quoteEffectiveStartDate: e?.quoteEffectiveStartDate
                  ? new Date(e.quoteEffectiveStartDate).getTime()
                  : null,
                quoteEffectiveEndDate: e?.quoteEffectiveEndDate
                  ? new Date(e.quoteEffectiveEndDate).getTime()
                  : null
              })
            } else if (tabIndex == 1) {
              quotaDTOList.push({
                allocationQuantity: e.allocationQuantity,
                id: e.id,
                allocationRatio: e.allocationRatio,
                priceClassification: e.priceClassification,
                minSplitQuantity: e.minSplitQuantity,
                minPurQuantity: e.minPurQuantity,
                itemCode: e.itemCode,
                siteCode: e.siteCode,
                stepValue: e?.stepValue || 0
              })
            }
          } else {
            this.$toast({
              content: this.$t(`第${Number(index + 1)}行的'配额'字段有误`),
              type: 'error'
            })
            throw new Error(this.$t(`第${Number(index + 1)}行的'配额'字段有误`))
          }
        })
        if (this.allocationRange == 1 && (tabIndex == 0 || tabIndex == 2)) {
          for (let item of quotaDTOList) {
            item.allocationQuantity = undefined
            item.allocationRatio = undefined
          }
        }
        let params = {
          rfxId: this.$route.query.rfxId,
          quotaDTOList
        }
        if (tabIndex == 0) {
          return this.$API.rfxPricing.getPricingSave(params)
        } else if (tabIndex == 1) {
          return this.$API.rfxPricing.rfxItemQuotaSaveQuota(params)
        }
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'infoRecord') {
        if (e.data.rfxApprovalStatus === 7) {
          //调整‘信息记录’操作逻辑
          if (e.data.priceStatus === 5) {
            this.$dialog({
              modal: () => import('./components/index.vue'),
              data: {
                title: this.$t('信息记录'),
                biddingItemId: e.data.id,
                priceStatus: e.data.priceStatus
              },
              success: () => {
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          } else {
            this.$toast({
              content: this.$t('定标审核未通过，不能操作'),
              type: 'warning'
            })
          }
        } else {
          this.$toast({
            content: this.$t('定标审批未完成，不能补充信息记录'),
            type: 'warning'
          })
        }
      } else if (e.field === 'costAnalysis') {
        const { id, costModelId } = e.data
        this.$router.push({
          name: 'purchase-cost',
          query: {
            rfxId: this.$route.query.rfxId,
            biddingItemId: id,
            costModelId,
            refreshId: Date.now() // 每次进入页面进行数据更新
          }
        })
      }
    },
    serializeSaveParams(list) {
      let res = []
      list?.forEach((e) => {
        let _sub = e?.childItems
        delete e.childItems
        res.push(e)
        if (Array.isArray(_sub)) {
          res = res.concat(_sub)
        }
      })
      return res
    },
    // 导出
    handleExcelExport(type) {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            condition: 'and',
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            value: this.rfxId
          }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.commonConfig[type === 'pricing' ? 'exportPricing' : 'exportBiddingData'](
        params
      ).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 配额分配-导出
    handleQuotaExport() {
      this.$API.rfxPricing
        .quotaExport({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          let fileName = getHeadersFileName(res)
          download({ fileName, blob: res.data })
        })
    },
    // 控制上传弹窗是否展示
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    }
  },
  beforeDestroy() {
    combination.beforeDestroy(this)
    sessionStorage.removeItem('siteParam')
    sessionStorage.removeItem('dieItemParam')
    sessionStorage.removeItem('temp_rfxId')
    sessionStorage.removeItem('temp_rfxItemId')
  }
}
</script>
<style lang="scss">
.childTemplate .grid-container .mt-data-grid .e-grid .e-content {
  min-height: 60px !important;
}
.price-dialog {
  width: 80% !important;
  height: 100% !important;
}
.rise-price .dialog-content {
  white-space: pre-line;
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.mt-tabs {
    background: #fafafa;
  }
  /deep/ .tab-container {
    background: #fafafa !important;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
/deep/ .e-rowcell input[readonly] {
  border-bottom: none !important;
  cursor: no-drop !important;
  color: rgba(0, 0, 0, 0.42) !important;
}
.e-dlg-container > .right-wrapper {
  height: 1000px !important;
}
</style>
