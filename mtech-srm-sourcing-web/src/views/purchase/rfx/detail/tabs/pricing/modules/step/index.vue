<template>
  <div class="flex-full-height">
    <mt-tabs
      class="custom-tab"
      :tab-id="$utils.randomString()"
      :e-tab="false"
      :data-source="tabSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 议价、当前报价、历史报价公用 -->
    <CustomAgGrid
      ref="CustomAgGrid"
      v-clickoutside="clickoutside"
      :search-config="searchConfig"
      :toolbar="toolbar"
      :columns="columns"
      :suppress-row-transform="true"
      :row-data="tableData"
      :animate-rows="false"
      :is-stop-editing-on-blur="isStopEditingOnBlur"
      :get-row-id="getRowId"
      :context="context"
      @cell-value-changed="cellValueChanged"
      @handleClickToolbar="handleClickToolbar"
      @onGridReady="onGridReady"
      @row-selected="onRowSelected"
      @refresh="refresh"
      @search="search"
    >
    </CustomAgGrid>

    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="$t('配额导入模板')"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { quotaColumnData, agQuotaColumnData } from '../../config'
import Vue from 'vue'
import CustomAgGrid from '@/components/CustomAgGrid'
import cellFile from '@/components/AgCellComponents/cellFile'
import checkbox from '@/components/AgCellComponents/checkbox'
import cellLink from '@/components/AgCellComponents/cellLink'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item' // 引用别名，避免变量歧义
import { Query } from '@syncfusion/ej2-data'
import { columnData as logisticsColumnData } from '@/views/common/columnData/rfxItemLogistics'
import { columnData as biddingItemLogisticsColumnData } from '@/views/common/columnData/biddingItemLogistics'
import { columnData as itemDieMapColumnData } from '@/views/common/columnData/biddingItemDie'
import { columnData as itemExtMapColumnData } from '@/views/common/columnData/itemExtMap'
import { getFields as getSBIFields } from '@/views/common/columnData/supplierBiddingItem'
import { createEditInstance } from '@/utils/ej/dataGrid'
// import selectDieItemCode from "./components/selectDieItemCode.vue"; // 模具
import editCheckbox from 'COMPONENTS/NormalEdit/checkbox/edit' // vmi
import itemDieInput from 'COMPONENTS/NormalEdit/itemDieInput' //模具
import cellFileView from 'COMPONENTS/NormalEdit/cellFileView' // 单元格附件查看
import cellFileViewSupply from 'COMPONENTS/NormalEdit/cellFileViewSupply' // 单元格上传
import * as combination from '../../utils/combination' // 单元格上传
import { getHeadersFileName, download } from '@/utils/utils'
import { stepNotMergeFieldNoPrefix } from '@/views/common/columnData/constant'
import {
  setStepField,
  addRowSpan,
  rowSpan,
  cellClass,
  cellClassNoBg
} from '@/views/common/columnData/utils'
import cloneDeep from 'lodash/cloneDeep'

export default {
  inject: ['reload'],
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    // eslint-disable-next-line
    cellFile,
    // eslint-disable-next-line
    checkbox,
    // eslint-disable-next-line
    cellLink // 单元格点击跳转
  },
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tabSource: [],
      moduleType: 0,
      agGrid: null,
      toolbar: [],
      searchConfig: [],
      tableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false,
      allocationRange: null, //allocationRange==1时展示配额分配tab
      rfxId: this.$route.query.rfxId,
      context: null,
      // tabConfig: {
      //   mode: 'rectangle'
      // },
      dictItems: [],
      taxList: [], // 税率编码
      currencyList: [], // 货币名称
      purUnitList: [], // 采购单位
      priceClassificationList: [
        { text: this.$t('暂估价格'), value: 'predict_price' },
        { text: this.$t('SRM价格'), value: 'srm_price' },
        { text: this.$t('执行价格'), value: 'execute_price' },
        { text: this.$t('基价'), value: 'basic_price' }
      ],
      oldList: [], //接口数据与编辑后数据进行比较
      siteNameList: [], //工厂列表
      childItemList: [], //子集List
      isStepGrid: false, // 是否是阶梯格式的数据，通过columns中有没有stepNum字段来判断
      itemGroupIdList: [], // 阶梯groupId
      currentEditData: null, //当前编辑的数据
      currentTabId: 0,
      currentTab: -1,
      requestUrls: {},
      downTemplateParams: {
        rfxId: this.$route.query.rfxId
      },
      txCompanyList: ['2M01', '2S06']
    }
  },
  watch: {},
  computed: {
    // allowEditing() {
    //   return canEdit(this.detailInfo?.transferStatus)
    // }
  },
  beforeDestroy() {
    this.$bus.$off('refreshPricingTab')
  },
  beforeMount() {
    this.context = { componentParent: this }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.$bus.$on('refreshPricingTab', () => {
      this.refresh()
    })
    await this.getStrategyData()
    await this.initSiteList()
    this.init()
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<< 事件操作 >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // tab - 切换页签
    handleSelectTab(idx) {
      this.moduleType = this.tabSource[idx].moduleType
      // 初始数据
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.id
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Save': // 保存
          this.handleSave()
          break
        case 'SubmitExamine': // 提交审批
          this.handleSubmitExamine()
          break
        case 'ExamineProgres': // OA审批进度
          this.handleExamineProgres()
          break
        case 'RejectBargain': // 驳回议价
          this.handleRejectBargain()
          break
        case 'StartNewRound': // 发起新一轮
          this.handleStartNewRound()
          break
        case 'Assistant': // 比价助手
          this.handleAssistant()
          break
        case 'BatchVmi': // 批量VMI
          this.handleBatchVmi()
          break
        default:
          break
      }
    },
    // toolbar点击监听 - 保存
    handleSave() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行保存操作？`)
        },
        success: () => {
          let _dataSource = this.serializeSaveParams(cloneDeep(this.tableData))
          this.handleSaveAPI(_dataSource, this.moduleType)
            .then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('操作成功.'),
                  type: 'success'
                })
              }
            })
            .finally(() => {
              this.refresh()
              // this.$refs.templateRef.refreshCurrentGridData()
            })
        }
      })
    },
    // toolbar点击监听 - 提交审批
    async handleSubmitExamine() {
      // 获取涨价提示
      const res = await this.$API.rfxPricing.submitPricingValid({ rfxId: this.$route.query.rfxId })
      if (res.code === 200 && res.data) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: res.msg,
            cssClass: 'rise-price'
          },
          success: () => {
            this.submitFn()
          }
        })
      } else {
        this.submitFn()
      }
    },
    // toolbar点击监听 - 提交审批 - 提交
    submitFn() {
      let _dataSource = this.serializeSaveParams(cloneDeep(this.tableData))
      this.$store.commit('startLoading')
      this.handleSaveAPI(_dataSource, this.moduleType).then((r) => {
        if (r.code == 200) {
          let params = {
            rfxId: this.$route.query.rfxId
          }
          this.$API.rfxPricing
            .getRfxPricingSubmit(params)
            .then((res) => {
              if (res.code == 200) {
                this.$store.commit('endLoading')
                this.$toast({
                  content: res.msg,
                  type: 'success'
                })
                this.reload()
              }
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
            .finally(() => {
              // this.$emit('reGetDetail')
            })
        }
      })
    },
    // toolbar点击监听 - OA审批进度
    handleExamineProgres() {
      //ApproveType  暂时处理：定标/定价页面
      //业务类型 0招标竞价定标 1询报价定价 2商务标晋级 3提交立项
      let params = {
        approveType: this.$route?.query?.source == 'rfq' ? 1 : 0,
        rfxId: this.$route.query.rfxId
      }
      this.$API.rfxExt.getOaLink(params).then((res) => {
        if (res?.data && res.data.indexOf('.com')) {
          window.open(res.data)
        }
      })
    },
    // toolbar点击监听 - 驳回议价
    handleRejectBargain() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'驳回议价'操作？")
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.rfxPricing
            .toEvaluationBid(query)
            .then(() => {
              this.$store.commit('endLoading')
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.reload()
            })
            .catch(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    // toolbar点击监听 - 发起新一轮
    handleStartNewRound() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t("确认执行'发起新一轮'操作？")
        },
        success: () => {
          const query = { rfxId: this.$route.query.rfxId }
          this.$store.commit('startLoading')
          this.$API.comparativePrice.startNewRound(query).then(() => {
            this.$store.commit('endLoading')
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.reload()
          })
        }
      })
    },
    // toolbar点击监听 - 比价助手
    handleAssistant() {
      this.$dialog({
        modal: () => import('../../../../../components/comparativePriceDialog.vue'),
        data: {
          rfxId: this.$route.query.rfxId,
          detailInfo: this.detailInfo
        }
      })
    },
    // toolbar点击监听 - 批量VMI
    handleBatchVmi() {
      let _selectedRows = this.agGrid.api.getSelectedRows()
      if (!_selectedRows?.length) return
      let rfxBiddingItemIds = _selectedRows.map((item) => item.id)
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认执行“批量VMI”操作？')
        },
        success: () => {
          this.$API.comparativePrice
            .batchSetVmi({
              rfxId: this.rfxId,
              rfxBiddingItemIds
            })
            .then(() => {
              this.$toast({ content: this.$t('操作成功！'), type: 'success' })
              this.refresh()
            })
        }
      })
    },
    // ag - 监听select选择框
    onRowSelected(e) {
      if (!e.data.isFirstLine) return
      let isSelected = e.node.selected
      let customGroupKey = e.data.customGroupKey
      //如果勾选的是阶梯第一行数据,同步其他行
      this.agGrid.api.forEachNode((node) => {
        if (node.data.customGroupKey === customGroupKey && !node.data.isFirstLine) {
          node.setSelected(isSelected)
        }
      })
    },
    // ag - 编辑框监听
    cellValueChanged(params) {
      if (params.oldValue === params.newValue) return
      this.syncOtherStepLine(params) //同步阶梯行
    },
    // 编辑框监听 - 阶梯同步其他行数据
    syncOtherStepLine(params) {
      const field = params.column.colId
      const customGroupKey = params.data.customGroupKey
      const isFirstLine = params.data.isFirstLine
      if (!isFirstLine || stepNotMergeFieldNoPrefix.includes(field) || !customGroupKey) return
      // 查询同组阶梯数据
      this.agGrid.api.forEachNode((node) => {
        if (!node.data.isFirstLine && node.data.customGroupKey === customGroupKey) {
          node.setDataValue(field, params.value)
        }
      })
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    // btn - 查询
    search(params) {
      this.initTableData(params)
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    init() {
      this.initTab()
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // 初始化 - 获取策略信息
    async getStrategyData() {
      await this.$API.strategyConfig
        .findByRfxId({
          sourcingMode: this.$route.query.source,
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          // 配额分配范围
          this.allocationRange = res.data.strategyConfigList[0].allocationRange
        })
    },
    // 初始化 - tab页签
    initTab() {
      let _tabSource = [
        { title: this.$t('报价明细'), moduleType: 0 },
        { title: this.$t('配额分配'), moduleType: 1 },
        { title: this.$t('历史报价'), moduleType: 2 }
      ]
      if (this.allocationRange !== 1) {
        //非议价状态没有议价tab页
        _tabSource.splice(1, 1)
      }
      this.tabSource = _tabSource
    },
    // 初始化 - 查询区域
    initSearchConfig() {
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'categoryCode',
          label: this.$t('品类编码'),
          hide: this.moduleType === 1
        },
        {
          field: 'temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'supplierCode',
          label: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          label: this.$t('供应商名称')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar() {
      let _toolbar = []
      if (this.moduleType === 2) {
        // 历史报价 不显示
        this.toolbar = []
        return
      }
      let status = this.$route.query?.status
      let transferStatus = this.detailInfo.transferStatus // 20:已提交定点 100 已完成
      // toolbar 控制
      _toolbar = [
        {
          id: 'Save',
          icon: 'icon_solid_Save',
          title: this.$t('保存'),
          hide: [8, -1].includes(status) || [20, 100].includes(transferStatus)
        },
        {
          id: 'SubmitExamine',
          icon: 'icon_solid_Submit',
          title: this.$t('提交审批'),
          hide: [8, -1].includes(status) || [20, 100].includes(transferStatus)
        },
        {
          id: 'ExamineProgres',
          icon: 'icon_solid_Submit',
          title: this.$t('OA审批进度'),
          hide: status === -1
        },
        {
          id: 'RejectBargain',
          icon: 'icon_solid_Submit',
          title: this.$t('驳回议价'),
          hide: [20, 100].includes(transferStatus)
        },
        {
          id: 'StartNewRound',
          icon: 'icon_solid_Submit',
          title: this.$t('发起新一轮'),
          hide: [20, 100].includes(transferStatus)
        },
        {
          id: 'CreateMoldBuildTable',
          icon: 'icon_solid_edit',
          title: this.$t('生成模具制造审批表'), // 暂未接入
          hide: transferStatus !== 100
        },
        {
          id: 'Assistant',
          icon: 'icon_solid_edit',
          title: this.$t('比价助手')
        },
        {
          id: 'BatchVmi',
          icon: 'icon_solid_edit',
          title: this.$t('批量VMI'),
          hide:
            [8, -1].includes(status) || [20, 100].includes(transferStatus) || this.moduleType === 1
        },
        {
          id: 'Import',
          icon: 'icon_solid_Import',
          title: this.$t('导入'),
          hide:
            [8, -1].includes(status) || [20, 100].includes(transferStatus) || this.moduleType !== 1
        },
        {
          id: 'Export',
          icon: 'icon_solid_export',
          title: this.$t('导出'),
          hide: this.moduleType !== 1 ? true : false
        }
      ]
      this.toolbar = _toolbar
    },
    // 初始化 - 表头
    initGridColumns() {
      // this.fieldDefines 采购明细主页面已作处理，沿用之前逻辑，保留field,headerText,allowEdit,eidtConfig此处阶梯根据this.fieldDefines配置ag相关属性
      let _itemColumns =
        this.moduleType === 1 ? 1 : this.defineGridColumns(cloneDeep(this.fieldDefines)) // 报价明细列信息
      let _quotaColumns = agQuotaColumnData(this) // 配额列信息
      let _columnData = this.moduleType === 1 ? cloneDeep(_quotaColumns) : cloneDeep(_itemColumns)
      // 阶梯相关字段顺序调整
      _columnData = setStepField(_columnData, stepNotMergeFieldNoPrefix)
      // 设置表头属性
      _columnData = this.setColumnsAttribute(_columnData)
      this.columns = cloneDeep(_columnData)
    },
    // 表头 - 字段序列化 (TODO 逻辑待整理)
    defineGridColumns(fieldDefines, isChild = false) {
      let arr = []
      const editInstance = createEditInstance()
        .component('checkbox', editCheckbox)
        .component('itemDieInput', itemDieInput)
      const rfxItemLogisticsColumn = logisticsColumnData({
        prefix: 'itemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const biddingItemLogisticsColumn = biddingItemLogisticsColumnData({
        prefix: 'biddingItemLogisticsResponse.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      const sBIFields = getSBIFields({
        currencyList: this.currencyList,
        taxList: this.taxList,
        purUnitList: this.purUnitList
      })

      if (sBIFields.bidPurUnitName?.editConfig) {
        sBIFields.bidPurUnitName.editConfig = {
          type: 'select',
          props: {
            ...sBIFields.bidPurUnitName.editConfig.props,
            fields: { value: 'id', text: '__text' }
          }
        }
      }
      const itemDieMapColumn = itemDieMapColumnData({
        prefix: 'itemDieResponse.',
        handleSelectChange: () => {}
      })
      const itemExtMapColumn = itemExtMapColumnData({
        prefix: 'itemExtMap.',
        dictItems: this.dictItems,
        handleSelectChange: () => {}
      })
      fieldDefines.forEach((e2) => {
        let name = ''
        if (e2.tableName == 'rfx_item_ext') {
          name = 'itemExtMap'
          const field = `${name}.${e2.fieldCode}`
          // 通用信息显示
          const defColumn = itemExtMapColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: 120,
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter,
            required: e2.required
          })
        } else if (e2.tableName == 'rfx_item_die') {
          name = 'itemDieResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = itemDieMapColumn.find((e) => e.field === field)
          if (
            field !== 'itemDieResponse.dieFormalCode' &&
            field !== 'itemDieResponse.basicDieCode'
          ) {
            arr.push({
              field,
              headerText: this.$t(e2.fieldName),
              allowEditing: false,
              width: 200,
              required: e2.required,
              editConfig: defColumn?.editConfig,
              formatter: defColumn?.formatter,
              edit: defColumn
                ? editInstance.create({
                    getEditConfig: () => ({
                      type: defColumn.editConfig.type,
                      ...defColumn.editConfig,
                      disabled: true,
                      readonly: true
                    })
                  })
                : undefined
            })
          } else if (field === 'itemDieResponse.basicDieCode') {
            arr.push({
              field,
              headerText: this.$t(e2.fieldName),
              allowEditing: true,
              required: e2.required,
              width: 200
            })
          } else {
            arr.push({
              field,
              headerText: this.$t(e2.fieldName),
              allowEditing: true,
              width: 200,
              // editConfig: defColumn?.editConfig,
              required: e2.required,
              formatter: defColumn?.formatter,
              edit: editInstance.create({
                getEditConfig: () => ({
                  type: 'itemDieInput',
                  field: 'itemDieResponse.dieFormalCode'
                })
              })
            })
          }
          // arr.push({
          //   field: `${name}.${e2.fieldCode}`,
          //   headerText: this.$t(e2.fieldName),
          //   allowEditing: false,
          //   width: "200",
          // });
        } else if (e2.tableName == 'rfx_item_logistics') {
          name = 'itemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = rfxItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: 200,
            required: e2.required,
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName === 'rfx_bidding_item_logistics') {
          name = 'biddingItemLogisticsResponse'
          const field = `${name}.${e2.fieldCode}`
          // 物流信息显示
          const defColumn = biddingItemLogisticsColumn.find((e) => e.field === field)
          arr.push({
            field,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: 200,
            required: e2.required,
            editConfig: defColumn?.editConfig,
            formatter: defColumn?.formatter
          })
        } else if (e2.tableName === 'rfx_bidding_item') {
          name = ''
          const row = {
            formatter: sBIFields?.[e2.fieldCode]?.formatter,
            editConfig: sBIFields?.[e2.fieldCode]?.editConfig,
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            allowEditing: [
              'allocationQuantity',
              'quoteEffectiveStartDate',
              'quoteEffectiveEndDate'
            ].includes(e2.fieldCode)
              ? true
              : false, // 配额数量、报价有效期从、报价有效期至可编辑
            width: '200',
            required: e2.required
          }
          const readonly = !row.allowEditing
          row.edit = sBIFields?.[e2.fieldCode]
            ? editInstance.create({
                // 针对日期字段添加专门的验证逻辑
                onInput: (ctx, { value }) => {
                  if (e2.fieldCode === 'quoteEffectiveStartDate' && value) {
                    // 设置结束日期的最小值为开始日期
                    ctx.setOptions('quoteEffectiveEndDate', {
                      min: new Date(Math.max(new Date(value).getTime(), Date.now()))
                    })
                  } else if (e2.fieldCode === 'quoteEffectiveEndDate' && value) {
                    // 设置开始日期的最大值为结束日期
                    ctx.setOptions('quoteEffectiveStartDate', {
                      max: new Date(new Date(value).getTime())
                    })
                  }

                  // 自动带出报价有效期至的逻辑
                  if (e2.fieldCode === 'quoteEffectiveStartDate' && value) {
                    const companyCode = this.detailInfo?.companyCode

                    // 1503、0602公司的自动设置逻辑
                    if (['1503', '0602'].includes(companyCode)) {
                      var date = new Date()
                      var year = date.getFullYear()
                      var yearTwo = date.getFullYear() + 1
                      let datae = year + '-' + '6' + '-' + '30'
                      let dateTwo = year + '-' + '7' + '-' + '1'
                      if (new Date(value).getTime() >= new Date(dateTwo).getTime()) {
                        ctx.setValueByField(
                          'quoteEffectiveEndDate',
                          yearTwo + '-' + '12' + '-' + '31'
                        )
                      } else if (new Date(value).getTime() <= new Date(datae).getTime()) {
                        ctx.setValueByField('quoteEffectiveEndDate', year + '-' + '12' + '-' + '31')
                      }
                    }

                    // 2M01、2S06公司的自动设置逻辑
                    if (this.txCompanyList.includes(companyCode)) {
                      const isNonProcurement = this.detailInfo?.rfxGeneralType === 2
                      if (isNonProcurement) {
                        // 非采：报价有效期至为报价有效期从的半年后日期
                        const startDate = new Date(value)
                        const endDate = new Date(startDate)
                        endDate.setMonth(startDate.getMonth() + 6)
                        const formattedEndDate =
                          endDate.getFullYear() +
                          '-' +
                          String(endDate.getMonth() + 1).padStart(2, '0') +
                          '-' +
                          String(endDate.getDate()).padStart(2, '0')
                        ctx.setValueByField('quoteEffectiveEndDate', formattedEndDate)
                      } else {
                        // 通采：按原有逻辑设置为2099-12-31
                        ctx.setValueByField('quoteEffectiveEndDate', '2099-12-31')
                      }
                    }

                    // 其他公司的默认逻辑：如果没有特殊处理，设置为2099-12-31
                    if (!['1503', '0602', ...this.txCompanyList].includes(companyCode)) {
                      ctx.setValueByField('quoteEffectiveEndDate', '2099-12-31')
                    }
                  }
                },
                getEditConfig: (context) => {
                  const baseConfig = {
                    type: sBIFields?.[e2.fieldCode]?.editConfig.type,
                    ...sBIFields?.[e2.fieldCode]?.editConfig.props,
                    readonly,
                    enabled: readonly
                  }

                  // 如果原字段有 getEditConfig，则合并其配置
                  if (sBIFields[e2.fieldCode].edit?.getEditConfig) {
                    const originalConfig = sBIFields[e2.fieldCode].edit.getEditConfig(context)
                    return {
                      ...baseConfig,
                      ...originalConfig,
                      readonly,
                      enabled: readonly
                    }
                  }

                  return baseConfig
                }
              })
            : undefined
          arr.push(row)
        } else if (e2.fieldCode === 'infoRecord') {
          arr.push({
            field: 'infoRecord',
            headerText: this.$t('信息记录'),
            cssClass: 'field-content',
            allowEditing: false,
            required: e2.required,
            valueConverter: {
              type: 'placeholder',
              placeholder: this.$t('信息记录')
            }
          })
        } else if (e2.tableName === 'rfx_file') {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            required: e2.required,
            template: function () {
              return {
                template: e2.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
              }
            },
            editTemplate: function () {
              return {
                template: e2.fieldCode === 'supplierDrawing' ? cellFileViewSupply : cellFileView
              }
            },
            width: 150,
            allowEditing: false
          })
        } else {
          arr.push({
            field: e2.fieldCode,
            headerText: this.$t(e2.fieldName),
            allowEditing: false,
            width: 200,
            required: e2.required
          })
        }
      })
      let that = this
      arr.forEach((e) => {
        if (e.required) {
          e.headerTemplate = () => {
            return {
              template: Vue.component('requiredCell', {
                template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                data() {
                  return {
                    data: {},
                    fieldName: ''
                  }
                },
                mounted() {
                  this.fieldName = e.headerText
                }
              })
            }
          }
        }
        if (e.field === 'priceClassification') {
          e.allowEditing = true
          e.editType = 'dropdownedit'
          e.formatter = ({ field }, item) => {
            const cellVal = item[field]
            return this.priceClassificationList.find((e) => e.value === cellVal)?.text
          }
          e.edit = {
            params: {
              dataSource: this.priceClassificationList,
              fields: { value: 'value', text: 'text' },
              query: new Query()
            }
          }
        } else if (e.field === 'distributionQuantity' || e.field === 'allocationQuantity') {
          e.allowEditing = true
          e.editType = 'numericedit'
          e.edit = {
            params: {
              min: 0,
              validateDecimalOnType: true
            }
          }
        }
        // else if (e.field === "itemDieResponse.dieFormalCode") {
        //   e.allowEditing = true;
        //   // e.editTemplate = () => {
        //   //   return {
        //   //     template: selectDieItemCode,
        //   //   };
        //   // };
        // }
        else if (e.field == 'costModelName') {
          e.cellTools = [
            {
              id: 'editCost',
              title: this.$t('报价'),
              visibleCondition: (data) => {
                return data['costModelName']
              }
            }
          ]
        } else if (e.field == 'costModelQuote') {
          e.valueConverter = {
            type: 'map',
            map: { 0: this.$t('否'), 1: this.$t('是') }
          }
        } else if (e.field === 'stepQuote') {
          e.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 1 ? that.$t('是') : that.$t('否')
          }
          e.allowEditing = false
          e.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('否'),
              1: this.$t('是')
            }
          }
        } else if (e.field === 'stepQuoteType') {
          e.formatter = function ({ field }, item) {
            const cellVal = item[field]
            return cellVal === 0
              ? this.$t('数量累计阶梯')
              : cellVal === 3
              ? this.$t('数量逐层阶梯')
              : ''
          }
          e.allowEditing = false
          e.valueConverter = {
            type: 'map',
            map: {
              0: this.$t('数量累计阶梯'),
              3: this.$t('数量逐层阶梯')
            }
          }
        } else if (e.field === 'vmi') {
          e.allowEditing = true
        }
      })
      // 存在临时物料，则物料选择框，支持操作 (添加固定字段隐藏，否则setDataValue报错)

      let fixFields = [
        'itemId',
        'itemName',
        'spec',
        'material',
        'unitName',
        'categoryId',
        'categoryCode',
        'categoryName',
        'priceUnitName'
      ]
      let haveTempItemCode = arr.some((e) => e.field === 'temporaryItemCode')
      if (haveTempItemCode && [34].includes(this?.detailInfo?.transferStatus)) {
        // 存在临时物料编码temporaryItemCode，且单据状态为‘定标中’ 可以编辑正式物料
        arr.forEach((e) => {
          if (fixFields.indexOf(e.field) > -1) fixFields.splice(fixFields.indexOf(e.field), 1)
          if (e.field === 'itemCode') {
            e.allowEditing = true
          }
        })
        // 特殊处理，添加隐藏项
        fixFields.map((item) => {
          arr.push({
            field: item,
            hide: true,
            width: 0
          })
        })
      }
      // // 存在临时模具编码，则正式模具编码选择框，支持操作
      // let haveTempDieItemCode = arr.some(
      //   (e) => e.field === "itemDieResponse.dieTempCode"
      // );
      // if (haveTempDieItemCode) {
      //   arr.forEach((e) => {
      //     if (e.field === "itemDieResponse.dieFormalCode") {
      //       e.allowEditing = true;
      //       e.editTemplate = () => {
      //         return {
      //           template: selectDieItemCode,
      //         };
      //       };
      //     }
      //   });
      // }
      arr.push(
        {
          field: 'bidTimes',
          headerText: this.$t('次数'),
          width: '90'
        },
        {
          field: 'roundNo',
          headerText: this.$t('轮次'),
          width: '90'
        }
      )
      // 添加勾选框
      if (this.isSelectControl()) {
        arr.unshift({ option: 'checkboxSelection', width: 55 })
      }
      return combination.defineGridColumnsAfter(this, arr, isChild, editInstance)
    },
    // 表头 - 工具 - 判断是否需要选择框
    isSelectControl() {
      if (this.moduleType === 2 || [20, 100].includes(this.detailInfo.transferStatus)) return false
      return true
    },
    // 表头 - 设置ag表头配置相关属性
    setColumnsAttribute(columns) {
      let _editable = this.getGridEditAble()
      let _base = rfxItem()

      let _columns = columns.map((item) => {
        // checkBox 特殊处理
        if (item.option === 'checkboxSelection') {
          return {
            ...item,
            rowSpan: rowSpan,
            cellClass: _editable ? cellClass : cellClassNoBg
          }
        }
        let _attrObj = {
          required: item.required,
          field: item.field || '',
          headerName: item.headerText,
          editable: _editable && item.allowEditing,
          editConfig: item.editConfig,
          width: item.width,
          hide: item.hide,
          rowSpan: rowSpan,
          cellClass: _editable ? cellClass : cellClassNoBg
        }
        // 特殊处理 - 物料编码
        if (['itemCode'].includes(item.field)) {
          _attrObj.editConfig = {
            type: 'selectSearch', //带弹框的下拉框
            props: {
              rfxId: this.$route.query.rfxId,
              sourcingType: this.detailInfo.sourcingType,
              source: this.$route.query.source
            }
          }
        }
        // 特殊处理 - 成本分析
        if (['costAnalysis'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellLink'
          _attrObj.cellRendererParams = {
            handleable: true, //“可操作”（不可编辑），取editable避免从历史报价进入成本分析页面
            rfxId: this.$route.query.rfxId,
            type: 'pur'
          }
        }
        // 特殊处理 - 附件查看
        if (['drawing', 'supplierDrawing'].includes(item.field)) {
          _attrObj.cellRenderer = 'cellFile'
        }
        if (['vmi'].includes(item.field)) {
          _attrObj.cellRenderer = 'checkbox'
        }
        const _find = _base.find((x) => x.field === item.field)
        if (_find) {
          // 特殊处理 (非编辑页签editable控制)
          if ('editable' in _find) {
            _find.editable = _editable && _find.editable
          }

          _attrObj.editConfig = _find?.editConfig
            ? { ..._attrObj.editConfig, ..._find?.editConfig }
            : { ..._attrObj.editConfig }
          delete _find.editConfig
          _attrObj = Object.assign({}, _attrObj, _find)
        }
        return _attrObj
      })
      return _columns
    },
    // 表头 - 获取表头是否可编辑
    getGridEditAble() {
      if (this.moduleType === 2) return false
      // 报价中|| 商务投标中 采购明细可以编辑
      if (![20, 100].includes(this.detailInfo.transferStatus)) return true
      return false
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      let url = ''
      let field = ''
      switch (this.moduleType) {
        case 0:
          url = this.$API.rfxPricing.getRfxPricingList // 报价明细
          field = 'rfx_item.rfxHeaderId'
          break
        case 1:
          url = this.$API.rfxPricing.getRfxItemQuotaList // 配额分配
          field = 'rfxId'
          break
        case 2:
          url = this.$API.rfxPricing.getRfxPricingLists // 历史报价
          field = 'rfx_item.rfxHeaderId'
          break
      }
      this.$store.commit('startLoading')
      let params = this.mergeParams(rules, field)
      this.tableData = []
      const res = await this.$API.rfxPricing
        .getRfxPricingItemList(url, { ...params })
        .catch(() => {})
      if (res.code === 200) {
        let records = res.data?.records || []
        let list = await this.serializeGridList(records)
        this.tableData = cloneDeep(list)
      }
      this.$store.commit('endLoading')
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules, field) {
      let params = {
        defaultRules: [
          {
            label: '',
            field: field,
            type: 'number',
            operator: 'equal',
            value: this.$route.query.rfxId
          }
        ],
        page: { current: 1, size: 10000 }
      }
      if (rules) params = Object.assign({}, params, rules)
      return params
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  工具方法  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 工具 - 生成GroupKey(区分阶梯数据)
    generateGroupKey(row) {
      let id = row.supplierId || '0'
      let code = row.supplierCode || 'a'
      return row.itemGroupId + '__' + id + '__' + code
    },
    // 初始化字典数据
    async initDictItems() {
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    async initTaxList() {
      const res = await this.$API.masterData.queryAllTaxItem().catch(() => {})
      if (res) {
        this.taxList = res.data
      }
    },
    // 币种编码
    async initCurrencyList() {
      const res = await this.$API.masterData.queryAllCurrency().catch(() => {})
      if (res) {
        this.currencyList = res.data
      }
    },
    // 采购单位
    async initPurUnitList() {
      this.$API.masterData.pagedQueryUnit().then((res) => {
        this.purUnitList = res?.data?.records || [] // FIXME 采购单位和基本单位的区别
      })
    },
    async initSiteList() {
      const res = await this.$API.masterData.permissionSiteList({
        buOrgId: sessionStorage.getItem('selectOrgId'),
        companyId: sessionStorage.getItem('selectCompanyId')
      })
      if (res) {
        this.siteNameList = res?.data || []
      }
    },

    // 配额列表字段根据招标询报价区分
    processQuotaData() {
      // 如果是询价大厅 隐藏名次字段
      if (this.$route?.query?.source === 'rfq') {
        const _columns = quotaColumnData(this).filter((item) => item.field !== 'ranking')
        return _columns
      }
      return quotaColumnData(this)
    },
    serializeGridList(list) {
      list.forEach((v) => {
        v.customType = 'pur' // 自定义type,区分采、供
        this.defineUnuseRateValue(v)
        v.allocationRatio = (v.allocationRatio * 100).toFixed(0)
        v.declinePercent = v.declinePercent ? (v.declinePercent * 100).toFixed(2) : ''
        v.drawing = v.fileList ? JSON.stringify(v.fileList) : null //单独处理附件字段
        v.supplierDrawing = v.supplierFileList ? JSON.stringify(v.supplierFileList) : null //单独处理"供应商附件"字段
        v.stepQuoteName =
          v.itemStageList && v.itemStageList.length > 0 ? JSON.stringify(v.itemStageList) : null //单独处理阶梯报价
        v.stepNum = v.stepValue
        // 工厂id处理
        let _findSite = this.siteNameList.find((e) => e.orgCode === v.siteCode)
        v.organizationId = _findSite ? _findSite?.id : ''
        // 生成key
        v.customGroupKey = this.generateGroupKey(v)
        // 添加rfxid
        v.rfxId = this.$route.query.rfxId
        let _subItems = v['childItems']
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })

      let arr = []
      list.forEach((x) => {
        arr.push(
          JSON.stringify({
            itemCode: x.itemCode,
            siteCode: x.siteCode
          })
        )
      })
      arr = [...new Set(arr)]
      arr = arr.filter((item, index) => index % 2 !== 0)
      list.forEach((x) => {
        arr.forEach((v) => {
          if (x.itemCode == JSON.parse(v).itemCode && x.siteCode == JSON.parse(v).siteCode) {
            x.isGray = true
          }
        })
      })
      list = addRowSpan(list)
      return list
    },

    // 列表中，税率名称、税率编码，值无效
    defineUnuseRateValue(v) {
      if (v?.bidTaxRateCode && /^\d{19}$/.test(v.bidTaxRateCode)) {
        let _find = this.taxList.find((e) => e.id === v.bidTaxRateCode)
        if (_find && _find.id) {
          v.bidTaxRateCode = _find.taxItemCode
          v.bidTaxRateName = _find.taxItemName
        }
      }
    },

    actionComplete(e) {
      if (e.requestType == 'beginEdit') {
        let _row = e.rowData
        if (!_row?.siteCode) {
          sessionStorage.removeItem('siteParam')
          sessionStorage.removeItem('temp_rfxId')
          sessionStorage.removeItem('temp_rfxItemId')
          return
        }
        let _findSite = this.siteNameList.find((e) => e.orgCode === _row.siteCode)
        if (_findSite) {
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: _findSite['id'],
              siteCode: _row.siteCode
            })
          )
        }
        this.minSplitQuantityCache = e.rowData.minSplitQuantity
        if (!_row?.itemCode) {
          sessionStorage.setItem(
            'dieItemParam',
            JSON.stringify({
              itemCode: _row?.itemCode
            })
          )
        } else {
          sessionStorage.removeItem('dieItemParam')
        }
        sessionStorage.setItem('temp_rfxId', this.$route.query.rfxId)
        sessionStorage.setItem('temp_rfxItemId', _row['rfxItemId'])
      }
      if (e.requestType == 'save') {
        this.$set(this.$refs.templateRef.getCurrentTabRef().grid.dataSource, e.rowIndex, e.data)
        if (e.data.minSplitQuantity != this.minSplitQuantityCache) {
          const grid = this.$refs.templateRef.getCurrentTabRef().grid
          let dataSource = grid.$options.propsData.dataSource
          let itemCode = e.data.itemCode
          if (itemCode) {
            dataSource.forEach((item) => {
              if (item.itemCode == itemCode) {
                item.minSplitQuantity = e.data.minSplitQuantity
              }
            })
            grid.refresh() //刷新表格
          }
        }
        if (this.isStepGrid) {
          //一行同步多行
          this.syncStepRownData(e.data)
        }
      }
    },

    // 生成模具制造审批表
    handleCreateMoldBuildTable(ids) {
      if (ids.length <= 0) {
        this.$toast({ content: this.$t('请选择模具明细数据'), type: 'warning' })
        return
      }
      const query = {
        rfxBiddingItemIds: ids,
        rfxId: this.$route.query.rfxId
      }
      this.$store.commit('startLoading')
      this.$API.rfxPricing
        .createMoldBuildTable(query)
        .then(() => {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleSaveAPI(dataSource, tabIndex) {
      if (!dataSource?.length) return
      let _quotaDTOList = []
      let _dataSource = cloneDeep(dataSource)
      _dataSource.forEach((e, index) => {
        // 配额为0也可以提交。
        e.allocationRatio = e.allocationRatio > 0 ? (e.allocationRatio / 100).toFixed(2) : 0
        if (e?.allocationRatio < 0) {
          this.$toast({
            content: this.$t(`第${Number(index + 1)}行的'配额'字段有误`),
            type: 'error'
          })
          throw new Error(this.$t(`第${Number(index + 1)}行的'配额'字段有误`))
        }
        if (tabIndex == 0) {
          // 报价明细
          _quotaDTOList.push({
            rfxBiddingItemId: e.id,
            allocationQuantity: this.allocationRange === 1 ? undefined : e.allocationQuantity, // 所有有效配额不传
            allocationRatio: this.allocationRange === 1 ? undefined : e.allocationRatio,
            priceClassification: e.priceClassification,
            minSplitQuantity: e.minSplitQuantity,
            dieFormalCode: e?.itemDieResponse?.dieFormalCode,
            basicDieCode: e?.itemDieResponse?.basicDieCode,
            vmi: e.vmi,
            quoteEffectiveStartDate: e?.quoteEffectiveStartDate
              ? new Date(e.quoteEffectiveStartDate).getTime()
              : null,
            quoteEffectiveEndDate: e?.quoteEffectiveEndDate
              ? new Date(e.quoteEffectiveEndDate).getTime()
              : null
          })
        } else if (tabIndex == 1) {
          //配额分配
          _quotaDTOList.push({
            allocationQuantity: e.allocationQuantity,
            id: e.id,
            allocationRatio: e.allocationRatio,
            priceClassification: e.priceClassification,
            minSplitQuantity: e.minSplitQuantity,
            minPurQuantity:
              e.minPurQuantity && e.minPurQuantity === 0
                ? Number(e.minPurQuantity)
                : e.minPurQuantity,
            itemCode: e.itemCode,
            siteCode: e.siteCode,
            stepValue: e.stepValue || 0
          })
        }
      })
      let params = {
        rfxId: this.$route.query.rfxId,
        quotaDTOList: _quotaDTOList
      }
      if (tabIndex == 0) {
        return this.$API.rfxPricing.getPricingSave(params)
      } else if (tabIndex == 1) {
        return this.$API.rfxPricing.rfxItemQuotaSaveQuota(params)
      }
    },
    handleClickCellTitle(e) {
      if (e.field == 'infoRecord') {
        if (e.data.rfxApprovalStatus === 7) {
          //调整‘信息记录’操作逻辑
          if (e.data.priceStatus === 5) {
            this.$dialog({
              modal: () => import('../../components/index.vue'),
              data: {
                title: this.$t('信息记录'),
                biddingItemId: e.data.id,
                priceStatus: e.data.priceStatus
              },
              success: () => {
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          } else {
            this.$toast({
              content: this.$t('定标审核未通过，不能操作'),
              type: 'warning'
            })
          }
        } else {
          this.$toast({
            content: this.$t('定标审批未完成，不能补充信息记录'),
            type: 'warning'
          })
        }
      }
    },
    serializeSaveParams(list) {
      let res = []
      list.forEach((e) => {
        let _sub = e?.childItems
        delete e.childItems
        res.push(e)
        if (Array.isArray(_sub)) {
          res = res.concat(_sub)
        }
      })
      return res
    },
    // 导出
    handleExcelExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules,
        defaultRules: [
          {
            condition: 'and',
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            value: this.rfxId
          }
        ].concat(queryBuilderRules.rules || [])
      } // 筛选条件
      this.$API.commonConfig.exportBiddingData(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 配额分配-导出
    handleQuotaExport() {
      this.$API.rfxPricing
        .quotaExport({
          rfxId: this.$route.query.rfxId
        })
        .then((res) => {
          let fileName = getHeadersFileName(res)
          download({ fileName, blob: res.data })
        })
    },
    // 控制上传弹窗是否展示
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.refresh()
    }
  }
}
</script>
<style lang="scss">
.price-dialog {
  width: 80% !important;
  height: 100% !important;
}
.rise-price .dialog-content {
  white-space: pre-line;
}
</style>
<style lang="scss" scoped>
.custom-tab /deep/.mt-tabs-container {
  background: #ffffff;
}
</style>
