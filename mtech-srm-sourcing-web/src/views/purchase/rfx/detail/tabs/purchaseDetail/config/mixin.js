import { v4 as uuidv4 } from 'uuid'
import { sliceArray } from '@/utils/arr'
import { cloneDeep, isEmpty } from 'lodash'
import Decimal from 'decimal.js'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
import { download, getHeadersFileName } from '@/utils/utils'
import { getValueByPath } from '@/utils/obj'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item'
import { columnData as itemExt } from '@/views/common/columnData/ag/itemExt'
import { columnData as itemLogistics } from '@/views/common/columnData/ag/itemLogistics'
// import { columnData as biddingItem } from '@/views/common/columnData/ag/biddingItem'
// import { columnData as biddingItemLogistics } from '@/views/common/columnData/ag/biddingItemLogistics'
import { columnData as itemDie } from '@/views/common/columnData/ag/itemDie'
import { columnData as itemLogisticsSea } from '@/views/common/columnData/ag/itemLogisticsSea'
import { columnData as itemLogisticsRailway } from '@/views/common/columnData/ag/itemLogisticsRailway'
import { columnData as itemLogisticsTrunk } from '@/views/common/columnData/ag/itemLogisticsTrunk'

import { addArrTextField } from '@/views/common/columnData/utils'
import {
  tableNameMap,
  logisticsTableNameMap,
  notAllowEditFields,
  ktNotAllowEditFields,
  costFactorNotAllowEditFields,
  generalNumberEditFields,
  fiveDecimalNumberEditFields,
  integerNumberEditFields,
  priceNumberEditFields,
  dateEditFields,
  booleanEditField
} from '@/views/common/columnData/ag/purConstant'
import {
  inputUnit,
  inputCurrency,
  inputSite,
  inputPurGroup,
  inputCost,
  inputTaxRate,
  inputQuantity,
  inputPrice,
  inputItemDieQuantity,
  inputLogisticsMethod
} from '@/views/purchase/rfx/utils/aggridInputHandlers.js'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import cellStep from '@/components/AgCellComponents/cellStep'
import cellDialog from '@/components/AgCellComponents/cellDialog'
import clickoutside from '@/directive/clickoutside'
export default {
  components: {
    cellFile,
    cellLink,
    cellStep,
    cellDialog
  },
  directives: { clickoutside: clickoutside },
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    moduleType: {
      type: Number,
      default: () => {}
    },
    ktFlag: {
      type: Number,
      default: () => {}
    }
  },

  data() {
    return {
      agGrid: null,
      searchConfig: [],
      toolbar: [],
      tableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false,
      dictItems: {}, //字典集合接口
      siteNameList: [], // 工厂集合
      rowSiteParams: {}, //缓存工厂信息
      rowItemParams: {}, //缓存物料工厂信息
      templateData: {}, //初始化生成默认模板数据（避免回填不了的情况）
      currencyNameData: null, //币种信息
      supplierNameData: null, //供应商信息
      skuNameList: null, //sku信息
      taxRateNameList: null, //taxRate信息
      receiveUserIdData: null, //人员信息
      unitNameList: null, //单位列表
      purUnitNameList: null, // 订单单位列表
      companyNameList: null, //公司列表
      purGroupList: null, //采购组列表
      deptNameList: null, //部门列表
      defaultStepData: null, // 默认阶梯报价数据
      isSingleClickEdit: false,
      requiredCols: [], //必填字段，全局变量
      massTrialFlagData: [
        { text: i18n.t('试产'), value: i18n.t('试产') },
        { text: i18n.t('量产'), value: i18n.t('量产') }
      ], // 试产/量产标识
      txCompanyList: ['2M01', '2S06']
    }
  },
  computed: {},
  watch: {
    treeGridDataSource: {
      handler() {
        this.initCurrentFieldDataGrid()
      },
      deep: true
      // immediate: true,
    },
    $route(to, from) {
      if (from.name === 'calculation-purchase-cost') {
        // 由成本分析页面返回，则刷新当前页面
        this.refresh()
      }
    }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.init()
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  事件操作:start  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 点击事件监听
    clickoutside() {
      this.agGrid.api.stopEditing()
    },
    // ag - 编辑框监听
    cellValueChanged(params) {
      if (params.oldValue === params.newValue) return
      inputUnit(params) // 单位联动
      inputCurrency(params) // 币种联动
      inputSite(params) // 工厂联动
      inputPurGroup(params) // 采购组织联动
      inputTaxRate(params) // 税率联动
      inputQuantity(params) // 建议最小采购量、建议最小包装量
      inputPrice(params) // 预算价格计算
      inputItemDieQuantity(params) //模具相关数量
      inputLogisticsMethod(params) // 物流方式
      this.syncOtherLogisticsLine(params) // 同步其他物流行行
      inputCost(
        params,
        this.detailInfo,
        this.$route.query.rfxId,
        this.$API.costModel.queryRelCostModel
      ) //成本模型联动
    },

    // 编辑框监听 - 干线竞价一行同步多行
    syncOtherLogisticsLine(params) {
      const field = params.column.colId
      if (field !== 'taxRateName') return
      // 同步其它明细行
      this.agGrid.api.forEachNode((node) => {
        node.setDataValue(field, params.value)
      })
    },
    async processDataFromClipboard(data) {
      this.$store.commit('startLoading')
      let { rowIndex } = this.agGrid.api.getFocusedCell()
      //  如果复制的行数多余表格的行数，则新添（从当前行定位）
      let totalCount = this.agGrid.api.getDisplayedRowCount()
      let lines = totalCount - rowIndex
      let lens = data?.length || 0
      let diff = lens - lines
      if (diff > 0) {
        for (let i = 0; i < diff; i++) {
          this.handleAdd()
        }
      }
      await this.delay(500)
      // 接口物料重复校验
      const _data = data.map((item) => item[0])
      await this.validItemRepeat(_data, rowIndex, this.loopDataChange)
    },
    // 数据联动
    loopDataChange(data, rowIndex) {
      let _this = this
      this.agGrid.api.forEachNode(async (node, index) => {
        if (index - rowIndex >= 0) {
          await _this.dataChange(node, { ...node.data, itemCode: data[index - rowIndex] })
        }
        if (index - rowIndex === data.length - 1) {
          this.tableData = this.getRowData()
          this.$store.commit('endLoading')
        }
      })
    },

    // 基础数据联动
    async dataChange(node, data) {
      const _itemCode = data ? data['itemCode'] : ''
      // 获取物料数据
      const itemData = await this.getItemData(data)
      if (!itemData) return
      // 数据校验
      let valid = this.validData(itemData)
      if (!valid) return
      // 物料编码及联动
      let fieldMap = {
        itemId: 'id',
        itemName: 'itemName',
        spec: 'itemDescription', // 规格描述
        material: 'materialTypeName', // 材质
        unitName: 'baseMeasureUnitName', // 单位
        unitCode: 'baseMeasureUnitCode',
        priceUnitCode: 'purchaseUnitCode'
      }
      // 如果页面配置了价格单位，则不用根据物料编码联动，由页面逻辑自动带默认值
      const priceUnitName = this.agGrid.columnApi.getColumn('priceUnitName')
      if (!priceUnitName) {
        fieldMap.priceUnitName = 'purchaseUnitName'
      }
      let basicData = {}
      // 基础信息设置
      Object.entries(fieldMap).map(([field, key]) => {
        basicData[field] = itemData ? getValueByPath(itemData, key) : ''
      })

      // 品类信息设置
      let categoryResponse = {
        categoryName: '',
        categoryCode: '',
        categoryId: ''
      }
      // 优先取categoryResponse
      if ('categoryResponse' in itemData && itemData.categoryResponse !== null) {
        categoryResponse = {
          categoryName: itemData.categoryResponse.categoryName,
          categoryCode: itemData.categoryResponse.categoryCode,
          categoryId: itemData.categoryResponse.id
        }
      } else if (itemData.categoryCode) {
        categoryResponse = {
          categoryName: itemData.categoryName,
          categoryCode: itemData.categoryCode,
          categoryId: itemData.id
        }
      }
      // 采购信息设置
      let purGroupData = await this.getPurGroup({
        itemCode: _itemCode,
        siteCode: data?.siteCode
      })
      // smt pcb版号同步
      let itemExtMap = data.itemExtMap || {}
      if (this.detailInfo.sourcingObjType === 'smt') {
        let res = await this.getComponentInfo({
          companyCode: this.detailInfo.companyCode,
          componentCode: data?.itemCode
        })
        itemExtMap.pcbCode = res.pcbCode
      }
      // 更新行数据
      const paramsData = Object.assign({}, data, { itemExtMap })
      const rowData = Object.assign(
        {},
        paramsData,
        { itemCode: _itemCode },
        basicData,
        categoryResponse,
        purGroupData
      )
      node.setData(rowData)
    },
    // 获取pcb版本号
    async getComponentInfo(data) {
      let params = {
        companyCode: data.companyCode,
        componentCode: data.componentCode
      }
      const res = await this.$API.smtComponentItem.querySmtComponent(params)
      return {
        pcbCode: res.data?.itemCode
      }
    },
    // 物料重复校验
    async validItemRepeat(data, rowIndex, fn) {
      let params = {
        rfxId: this.$route.query.rfxId,
        itemCodeList: [...data]
      }
      // 接口物料重复校验
      await this.$API.masterData.checkItemCode(params).then((res) => {
        if (res?.data?.length) {
          let rfxCodes = []
          let itemCodes = []
          res.data.forEach((item) => {
            rfxCodes.push(item.rfxCode)
            itemCodes.push(item.itemCode)
          })
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t(
                `进行中的单据：${rfxCodes.join(',')} ，已存在物料编码：${itemCodes.join(
                  ','
                )},是否继续添加?`
              )
            },
            success: () => {
              fn(data, rowIndex)
            }
          })
        } else {
          fn(data, rowIndex)
        }
      })
    },
    // 获取物料信息
    async getItemData(data) {
      let res = await this.$API.comparativePrice.queryItemsData({
        page: { current: 1, size: 20 },
        organizationId: data.siteId || this.detailInfo.siteId,
        siteCode: data?.siteCode,
        rfxId: this.$route.query.rfxId,
        defaultRules: [
          {
            label: '物料编码',
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value: data.itemCode
          }
        ]
      })
      return res?.data?.records?.length > 0 ? res.data.records[0] : null
    },
    // 数据校验
    validData(data) {
      // 1.二次、已有：如果不存在价格记录报错
      if (
        ['second_inquiry', 'exist'].includes(this.detailInfo.sourcingType) &&
        data.isPriceRecord === 0
      ) {
        this.$toast({
          content: this.$t('请选择有价格记录的物料'),
          type: 'warning'
        })
        return
      }
      /**
       * 3.新品：
       * 0:没有价格类型：校验价格记录
       * 1.暂估价：校验暂估价 和 执行价
       * 2.srm价格：校验srm价格
       * 3.执行价：校验执行价
       * 4.基价：校验基价
       *  */
      const priceMap = {
        '': {
          validField: ['isPriceRecord'],
          validText: this.$t('请选择没有价格记录的物料')
        },
        predict_price: {
          validField: ['predictPrice', 'executePrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        },
        srm_price: {
          validField: ['srmPrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        },
        execute_price: {
          validField: ['executePrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        },
        basic_price: {
          validField: ['basicPrice'],
          validText: this.$t('请选择没有执行价和暂估价记录的物料')
        }
      }
      if (this.detailInfo.sourcingType === 'new_products') {
        let priceClassification = this.detailInfo.priceClassification || ''
        for (let i = 0; i < priceMap[priceClassification].length; i++) {
          if (data[priceMap[priceClassification][i]] === 1) {
            this.$toast({
              content: priceMap['validText'],
              type: 'warning'
            })
            return
          }
        }
      }
      // 4.物物料重复校验
      // if (this.hasItemCode(data.itemCode)) return
      return true
    },
    // 获取采购组信息
    async getPurGroup(data) {
      let params = {
        itemCode: data.itemCode,
        organizationCode: data.siteCode
      }
      const res = await this.$API.masterData.getPurGroup(params)
      return {
        purGroupName: res.data?.purchaseGroupName,
        purGroupCode: res.data?.purchaseGroupCode
      }
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Add':
          this.handleAdd() // 新增
          break
        case 'Delete':
          this.handleDelete() // 删除
          break
        case 'Save':
          this.handleSave() // 保存
          break
        case 'Import':
          this.handleImport() // 导入
          break
        case 'Export':
          this.handleExport() // 导出
          break
        case 'BatchAdd':
          this.handleBatchAdd() // 批量添加
          break
        case 'BatchCopy':
          this.handleBatchCopy() // 批量复制行
          break
      }
    },
    // toolbar点击监听 - 批量添加
    handleBatchAdd() {
      if (!this.detailInfo.siteCode) {
        this.$toast({
          content: this.$t('单据未配置工厂'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('@/components/NormalEdit/checkSelectItemCode/components/selectGrid'),
        data: {
          title: this.$t('选择物料'),
          rfxId: this.$route.query.rfxId,
          siteParam: {
            organizationId: this.detailInfo.siteId,
            siteCode: this.detailInfo.siteCode
          },
          sourcingType: this.detailInfo.sourcingType,
          multiple: true,
          source: this.$route?.query?.source,
          sourcingObjType: this.detailInfo.sourcingObjType,
          priceClassification: this.detailInfo.priceClassification
        },
        success: (data) => {
          let dataSource = this.getRowData()
          let existItemCodeArr = []
          for (let item of dataSource) {
            if (item.itemCode) {
              existItemCodeArr.push(item.itemCode)
            }
          }
          for (let item of data) {
            // 校验itemCode存在
            if (existItemCodeArr.indexOf(item.itemCode) != -1) {
              this.$toast({
                content: this.$t('物料"' + item.itemName + '"在当前单据已存在,不能重复添加'),
                type: 'warning'
              })
              continue
            }
            let row = this.getAddTemplateData()
            Object.assign(row, {
              isBatch: true,
              itemId: item.id,
              itemCode: item.itemCode,
              itemName: item.itemName,
              spec: item.itemDescription,
              material: item.materialTypeName,
              unitId: item.baseMeasureUnitId,
              unitCode: item.baseMeasureUnitCode,
              unitName: item.baseMeasureUnitName,
              categoryId: item.categoryId,
              categoryCode: item.categoryCode,
              categoryName: item.categoryName,
              // priceUnitName: item.purchaseUnitName,
              purUnitId: item.purUnitId,
              purUnitCode: item.purUnitCode,
              purUnitName: item.purUnitName
            })
            // 如果页面配置了价格单位，则不用根据物料编码联动，由页面逻辑自动带默认值
            const priceUnitName = this.agGrid.columnApi.getColumn('priceUnitName')
            if (!priceUnitName) {
              row.priceUnitName = item.purUnitName
            }
            this.tableData.push(cloneDeep(row))
          }
          return
        }
      })
    },
    // toolbar点击监听 - 批量复制行
    handleBatchCopy() {
      const rowSelections = this.agGrid.api.getSelectedRows()
      if (!rowSelections?.length) {
        this.$toast({
          content: this.$t('请先勾选数据'),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        modal: () => import('@/views/purchase/rfx/detail/components/timesDialog.vue'),
        data: {
          title: this.$t('次数')
        },
        success: (times) => {
          for (let i = 0; i < times; i++) {
            rowSelections.forEach((item) => {
              const _item = {
                ...item,
                id: undefined,
                rowIndex: null,
                lineNo: null,
                addId: uuidv4(),
                rfxItemKey: uuidv4()
              }
              this.tableData.push(_item)
            })
          }
          this.handleSave()
        }
      })
    },
    // toolbar点击监听 - 新增
    handleAdd() {
      let _templateData = this.getAddTemplateData()
      this.tableData.push(cloneDeep(_templateData))
      console.log(this.tableData)
    },
    // 新增 - 新增数据模板
    getAddTemplateData() {
      let _detailInfo = cloneDeep(this.detailInfo) // ?提交会全量传递detailInfo的数据
      let _templateData = {
        ...this.templateData,
        ..._detailInfo,
        addId: uuidv4(),
        rfxItemKey: uuidv4(),
        id: undefined
        // rowIndex: this.tableData?.length ? this.tableData.length + 1 : 1
      }
      this.mergeDetailInfoCurrency(_templateData) // 合并币种新增
      this.setDefaultData(_templateData)
      return _templateData
    },
    // 新增 - 新增数据模板 - 合并单据币种
    mergeDetailInfoCurrency(data) {
      if (!data.currencyCode) return
      const currency = this.currencyNameData.find(
        ({ currencyCode }) => currencyCode === this.detailInfo.currencyCode
      )
      if (currency) {
        data.currencyCode = currency.currencyCode
        data.currencyName = currency.currencyName
      }
    },
    // 新增 - 设置默认数据(不同场景默认数据设置不同)
    setDefaultData(data) {
      // 直送地默认，1503公司默认惠州
      let deliveryPlace = ''
      if (data.companyCode === '1503' || this.detailInfo.sourcingScenarios === 'odmin')
        deliveryPlace = [this.$t('惠州')]
      /**
       *价格单位
       *  1.通采（空调、模具、海外通用询报价默认单位为1、其他为1000
       *  2.非采（如果配了则默认值为1）
       **/
      let hasUnit = this.columns.find((item) => item.fieldCode === 'priceUnitName')
      let priceUnitName = this.txCompanyList.includes(data.companyCode) // 当 companyCode 为 2M01 或 2S06 时,默认单位为 1
        ? '1'
        : data.rfxGeneralType === 1
        ? this.ktFlag == 1 ||
          ['single_module', 'COMMON_OVERSEA_CKD', 'odmin'].includes(this.detailInfo.sourcingObjType) //kt 、模具、海外通用询报价默认单位为1
          ? '1'
          : '1000'
        : hasUnit // 非采分为配置了价格单位跟没有配置价格单位两种情况
        ? ''
        : '1'
      // 阶梯默认值设置
      let itemStages = []
      // 结构件&美工件
      if (['structure_component', 'artist_component'].includes(this.detailInfo.sourcingObjType)) {
        itemStages = [
          // 此处应该配置字典中 *
          { remark: '', startValue: '1' },
          { remark: '', startValue: '500' },
          { remark: '', startValue: '2000' },
          { remark: '', startValue: '3000' }
        ]
        data['stepQuote'] = 1
        data['stepQuoteType'] = 3
      }
      if (['power_panel', 'module_out_going'].includes(this.detailInfo.sourcingObjType)) {
        itemStages = cloneDeep(this.defaultStepData)
        data['stepQuote'] = 0
        data['stepQuoteType'] = null
      }
      data['stepQuoteName'] = JSON.stringify(itemStages)
      data['itemStageList'] = cloneDeep(itemStages)
      Object.assign(data, {
        itemLogisticsResponse: {
          transportType: data.sourcingObjType
        },
        // itemExtMap: {
        //   referChannel: 0, // 区分父子
        //   deliveryPlace: deliveryPlace
        // },
        itemDieResponse: {
          addUp: 1, //累计至本次  默认'是'
          shareFlag: 0 //是否分摊量 默认'否'
        },
        priceUnitName
      })
      if (isEmpty(data.itemExtMap)) {
        data.itemExtMap = {}
      }
      data.itemExtMap.referChannel = 0
      data.itemExtMap.deliveryPlace = deliveryPlace
    },
    // // 新增 - 新增数据模板 - 合并工厂信息
    // mergeDetailInfoSite(data) {
    //   if (!data.siteCode) return
    // },
    // toolbar点击监听 - 删除
    async handleDelete() {
      const rowSelections = this.agGrid.api.getSelectedRows()
      const newIdList = [] // 新增
      const oldIdList = [] // 已有
      rowSelections.map((item) => {
        // 处理数据
        item.id && oldIdList.push(item.id)
        !item.id && newIdList.push(item.addId)
      })
      // 1.如果只有新增数据的情况直接删除
      if (newIdList.length && !oldIdList.length) {
        // this.agGrid.api.updateRowData({ remove: rowSelections })
        this.tableData = this.tableData.filter((item) => !newIdList.includes(item.addId))
      }
      // 2.如果存在oldIdList,则直接调用保存接口
      if (oldIdList.length) {
        this.tableData = this.tableData.filter((item) => !oldIdList.includes(item.addId))
        await this.delay(500)
        this.handleSave(oldIdList)
      }
    },
    // toolbar点击监听 - 保存
    async handleSave(ids) {
      await this.delay(500)
      let rowData = this.getRowData() //不用this.tableData数据
      // 父子结构暂未整理 TODO
      let { flag, submitTableData } = this.serializeSaveParams(
        cloneDeep(rowData),
        this.fieldDefines
      )
      // 数据校验
      if (flag) return
      let _params = {
        deleteIdList: { idList: ids || [] },
        rfxId: this.$route.query.rfxId,
        rfxItems: submitTableData
      }
      let res = await this.$API.rfxRequireDetail.saveRFXItem(_params).catch(() => {
        this.ids = []
      })
      if (res?.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.refresh()
        this.ids = []
      }
      return res
    },
    // toolbar点击监听 - 保存 - 序列化数据 TODO:逻辑细节待整理
    serializeSaveParams(data, fields) {
      let flag = false
      data.forEach((item, index) => {
        let _extSaveRequest = {} //扩展表字段存储  rfx_item_ext
        let _dieSaveRequest = {} //模具信息  字段存储  rfx_item_die
        let _logisticsSaveRequest = {} //物流信息 字段存储 rfx_item_logistics
        let _extStageSaveRequests = []
        // 参考物料信息处理
        if (getValueByPath(item, 'itemExtMap.referItemSupplierId')) {
          // 写入_extSaveRequest
          _extSaveRequest.referItemSupplierId = getValueByPath(
            item,
            'itemExtMap.referItemSupplierId'
          )
          // 供应商数据转化
          if (!item.supplierId) {
            item.supplierId = getValueByPath(item, 'itemExtMap.referItemSupplierId')
            item.supplierCode = getValueByPath(item, 'itemExtMap.referItemSupplierCode')
            item.supplierName = getValueByPath(item, 'itemExtMap.referItemSupplierName')
          }
        }
        // 文件数据转换
        if (item['drawing'] && typeof item['drawing'] == 'string') {
          item['drawing'] = JSON.parse(item['drawing'])
          if (item['drawing'].length) {
            item['drawing'].forEach((f) => {
              if (!f?.sysFileId) {
                f.sysFileId = f.id
                delete f.id
              }
            })
          }
        } else if (!item['drawing']) {
          item['drawing'] = []
        }
        item['fileList'] = item['drawing']
        item['drawing'] = ''
        // 阶梯数据转换
        if (item['stepQuoteName'] && typeof item['stepQuoteName'] == 'string') {
          item['stepQuoteName'] = JSON.parse(item['stepQuoteName'])
        } else if (!item['stepQuoteName']) {
          item['stepQuoteName'] = []
        }
        item['itemStageList'] = item['stepQuoteName']
        item['itemStageList'].forEach((x) => {
          x.stepType = 2
        })
        item['stepQuoteName'] = ''

        // 数据赋值
        item['rfxHeaderId'] = this.detailInfo.id
        item['rfxHeaderCode'] = this.detailInfo.rfxCode
        item['rfxHeaderName'] = this.detailInfo.rfxName
        item['businessTypeId'] = this.detailInfo.businessTypeId
        item['businessTypeCode'] = this.detailInfo.businessTypeCode
        item['businessTypeName'] = this.detailInfo.businessTypeName
        // 日期处理 _extSaveRequest _dieSaveRequest _logisticsSaveRequest
        for (let i in fields) {
          let _temp = fields[i]
          if (dateEditFields.indexOf(_temp.fieldCode) > -1) {
            //日期类型
            if (item[_temp['fieldCode']]) {
              item[_temp['fieldCode']] = this.$utils.formatTime(item[_temp['fieldCode']])
              console.log('日期类型序列化', item[_temp['fieldCode']])
            }
          }

          if (_temp['tableName'] === 'rfx_item_ext') {
            if (item['itemExtMap'] != undefined) {
              if (
                _temp['fieldCode'] === 'salesVouchers' &&
                item['itemExtMap'][_temp['fieldCode']] === 'empty'
              ) {
                _extSaveRequest[_temp['fieldCode']] = '' // 销售凭证空值转换
              } else {
                _extSaveRequest[_temp['fieldCode']] = item['itemExtMap'][_temp['fieldCode']]
              }
              // delete item['itemExtMap'][_temp['fieldCode']]
            }
          } else if (_temp['tableName'] === 'rfx_item_logistics') {
            if (item['itemLogisticsResponse'] != undefined) {
              _logisticsSaveRequest[_temp['fieldCode']] =
                item['itemLogisticsResponse'][_temp['fieldCode']]
              delete item['itemLogisticsResponse'][_temp['fieldCode']]
            }
          } else if (_temp['tableName'] === 'rfx_item_die') {
            if (item['itemDieResponse'] != undefined) {
              _dieSaveRequest[_temp['fieldCode']] =
                item['itemDieResponse'][_temp['fieldCode']] ?? ''
              delete item['itemDieResponse'][_temp['fieldCode']]
            }
          }
        }
        // extSaveRequest 时间格式处理
        if (_extSaveRequest?.requireDate) {
          _extSaveRequest.requireDate = Date.parse(new Date(_extSaveRequest.requireDate))
        }
        if (_extSaveRequest?.requireEndDate) {
          _extSaveRequest.requireEndDate = Date.parse(new Date(_extSaveRequest.requireEndDate))
        }
        // extSaveRequest直送地转换
        let _deliveryPlace = _extSaveRequest?.deliveryPlace
        item['deliveryPlaceList'] =
          _deliveryPlace && !Array.isArray(_deliveryPlace)
            ? _deliveryPlace.split(',')
            : _deliveryPlace
        item['deliveryPlaceList'] = item['deliveryPlaceList'] || []
        _extSaveRequest?.deliveryPlace && (_extSaveRequest.deliveryPlace = '') // 置空直送地（后端解析单个直送地报错）
        if (
          isEmpty(item['deliveryPlaceList']) &&
          this.requiredCols.includes('itemExtMap.deliveryPlace')
        ) {
          this.$toast({
            content: this.$t(`请填写第${Number(index + 1)}行的直送地`),
            type: 'error'
          })
          flag = true
        }
        // extSaveRequest 最小竞价（报价）幅度、最小竞价（报价）幅度类型转换
        let minQuoteRangeType = _extSaveRequest?.minQuoteRangeType
        if (minQuoteRangeType === 1) {
          _extSaveRequest.minQuoteRange = new Decimal(_extSaveRequest.minQuoteRange)
            .div(new Decimal(100))
            .toNumber()
        } else if (minQuoteRangeType === 0) {
          if (
            _extSaveRequest.maxQuotePercent > 0 &&
            _extSaveRequest.minQuotePercent > 0 &&
            _extSaveRequest.minQuoteRange >
              _extSaveRequest.maxQuotePercent - _extSaveRequest.minQuotePercent
          ) {
            // 如果价格上限、下限，都有值，且>0，校验'最小竞价幅度'
            this.$toast({
              content: this.$t(`第${Number(index + 1)}行最小竞价幅度应小于价格上限减去价格下限`),
              type: 'error'
            })
            flag = true
          }
        }
        // extSaveRequest 最小采购量、最小包装量 校验
        if (_extSaveRequest?.adviseMinPurQuantity && _extSaveRequest?.adviseMinPackageQuantity) {
          let adviseMinPurQuantity = _extSaveRequest.adviseMinPurQuantity
          let adviseMinPackageQuantity = _extSaveRequest.adviseMinPackageQuantity
          let number = adviseMinPurQuantity / adviseMinPackageQuantity
          if (
            Math.floor(number) !== number &&
            !this.txCompanyList.includes(this.detailInfo?.companyCode)
          ) {
            this.$toast({
              content: this.$t(`第${index + 1}行最小采购量需是最小包装数量的整数倍`),
              type: 'warning'
            })
            flag = true
          }
        }
        // 参考物料信息附带信息
        let extStageSaveRequests = data?.extStageSaveRequests || []
        extStageSaveRequests.forEach((extStage) => {
          _extStageSaveRequests.push({
            ...item.extSaveRequest,
            ...extStage
          })
        })

        delete item['itemExtMap']
        delete item['itemLogisticsResponse']
        delete item['itemDieResponse']
        item['extSaveRequest'] = _extSaveRequest
        item['dieSaveRequest'] = _dieSaveRequest
        item['logisticsSaveRequest'] = _logisticsSaveRequest
        item['extStageSaveRequests'] = _extStageSaveRequests
      })
      return { submitTableData: data, flag }
    },
    // toolbar点击监听 - 导入
    handleImport() {
      if (this.detailInfo.sourcingMode == 'rfq') {
        this.requestUrls = {
          templateUrlPre: 'rfxRequireDetail',
          templateUrl: 'exportRfxItem',
          uploadUrl: 'importRfxItem',
          rfxId: this.$route.query.rfxId
        }
      } else {
        this.requestUrls = {
          templateUrlPre: 'rfxRequireDetail',
          templateUrl: 'exportBiddingAndTenderRfxItem',
          uploadUrl: 'importBiddingAndTenderRfxItem',
          rfxId: this.$route.query.rfxId
        }
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.refresh()
    },
    // toolbar点击监听 - 导出
    handleExport() {
      let id = this.$route.query?.id || this.detailInfo.id
      let params = {
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            type: 'string',
            value: id
          }
        ]
      }
      this.$API.rfxRequireDetail.exportBuilder(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // toolbar点击监听 - 刷新
    refresh() {
      this.initTableData()
    },
    // toolbar点击监听 - 查询
    search(params) {
      this.initTableData(params)
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  事件操作:end  >>>>>>>>>>>>>>>>>>>>>>>>>>>

    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化:start  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    async init() {
      this.initSearchConfig()
      this.initToolbar()
      await this.initDictItems()
      await this.getDropDownData()
      // 如果是电源板外发、整机外发请求接口
      if (
        [
          'power_panel', // 电源板外发
          'module_out_going' // 整机外发
        ].includes(this.detailInfo.sourcingObjType)
      ) {
        this.initStepDefaultData()
      }
      this.initGridColumns()
      this.initTableData()
    },

    // 初始化 - 查询区域（组件同名被覆盖）
    initSearchConfig() {
      // if (this.pageType && `init${this.pageType}SearchConfig` in this) {
      //   //不同结构类型初始化toolbar不同
      //   this[`init${this.pageType}SearchConfig`]()
      //   return
      // }
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'categoryCode',
          label: this.$t('品类编码')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar （组件同名被覆盖）
    initToolbar() {
      let editable = this.getGridEditAble()
      let _toolbar = [
        {
          id: 'Add',
          icon: 'icon_solid_Createorder',
          title: this.$t('新增'),
          hide:
            !editable ||
            ['trunk_transport_annual', 'sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            ) ||
            [3, '3'].includes(this.detailInfo?.docSource)
        },
        {
          id: 'Delete',
          icon: 'icon_solid_Delete1',
          title: this.$t('删除'),
          hide:
            !editable ||
            ['trunk_transport_annual', 'sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            ) ||
            [3, '3'].includes(this.detailInfo?.docSource)
        },
        {
          id: 'Save',
          icon: 'icon_solid_Createproject',
          title: this.$t('保存'),
          hide: !editable
        },
        {
          id: 'Import',
          icon: 'icon_solid_Createproject',
          title: this.$t('导入'),
          hide:
            ['trunk_transport_annual', 'sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            ) || [3, '3'].includes(this.detailInfo?.docSource)
        },
        {
          id: 'Export',
          icon: 'icon_solid_Download',
          title: this.$t('导出')
        },
        {
          id: 'BatchAdd',
          icon: 'icon_solid_Createorder',
          title: this.$t('批量添加'),
          hide:
            !editable ||
            ['trunk_transport_annual', 'sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            ) ||
            [3, '3'].includes(this.detailInfo?.docSource)
        },
        {
          id: 'BatchCopy',
          icon: 'icon_solid_Createorder',
          title: this.$t('批量复制行'),
          hide:
            !editable ||
            this.detailInfo.rfxGeneralType === 1 ||
            this.detailInfo.sourcingMode !== 'rfq' ||
            ['trunk_transport_annual', 'sea_transport_annual', 'railway_transport_annual'].includes(
              this.detailInfo.sourcingScenarios
            ) ||
            [3, '3'].includes(this.detailInfo?.docSource)
        }
      ]
      this.toolbar = _toolbar
    },
    // 初始化 - 表头
    async initGridColumns() {
      // 设置表头属性
      let fieldDefines = [...this.fieldDefines]
      if (this.detailInfo.sourcingScenarios === 'trunk_transport_annual') {
        const dynamicConfig = await this.setLogisticsDynamicConfig(fieldDefines)
        fieldDefines = [...dynamicConfig]
      }

      let _columnData = this.defineGridColumns(cloneDeep(fieldDefines))
      this.columns = cloneDeep(_columnData)
    },
    // 物流 - 获取阶梯列配置
    async setLogisticsDynamicConfig(_columnData) {
      let dynamicConfig = []
      const res = await this.$API.rfxDetail.getLogisticsDynamicConfig({
        id: this.$route.query.rfxId
      })
      if (res.code === 200) {
        const _data = { ...res.data } || {}
        for (let i in _data) {
          _data[i].forEach((item) => {
            dynamicConfig.push({
              fieldCode: item.stepCode,
              fieldName: item.stepName,
              width: 140,
              tableName: logisticsTableNameMap[this.detailInfo.sourcingScenarios]['tableName']
            })
          })
        }
      }
      // 找到字段三的索引
      const index = _columnData.findIndex((column) => column.fieldCode === 'stepTrunkQty')
      // 将 stepList 插入到字段三之前
      if (index !== -1) {
        _columnData.splice(index, 0, ...dynamicConfig)
      }
      return _columnData
    },
    // 表头 - 列字段定义
    defineGridColumns(fieldDefines) {
      let _gridEditable = this.getGridEditAble()
      let topInfoFormData = this.$parent.$refs.topInfoRef.formData
      // 1.添加序号
      let _columnData = []
      let _preColumns = [
        {
          fieldCode: 'rowIndex',
          fieldName: this.$t('序号')
        }
      ]
      let _columns = _preColumns.concat(cloneDeep(fieldDefines))
      if (!Array.isArray(_columns) || _columns?.length < 1) return []
      let rfxItemParams = {
        currencyNameData: addArrTextField(this.currencyNameData, 'currencyCode', 'currencyName'),
        purUnitNameList: addArrTextField(this.purUnitNameList, 'unitCode', 'unitName'), // 采购单位
        taxRateNameList: addArrTextField(this.taxRateNameList, 'taxItemCode', 'taxItemName'),
        purGroupList: addArrTextField(this.purGroupList, 'groupCode', 'groupName'),
        unitNameList: addArrTextField(this.unitNameList, 'unitCode', 'unitName'),
        supplierNameData: this.supplierNameData,
        siteNameList: this.siteNameList,
        skuNameList: this.skuNameList,
        shippingMethodNameData: this.shippingMethodNameData,
        tradeClauseNameData: this.tradeClauseNameData,
        receiveUserIdData: this.receiveUserIdData,
        companyNameList: this.companyNameList,
        deptNameList: this.deptNameList,
        detailInfo: this.detailInfo,
        getCategoryList: this.$API.masterData.getCategoryList,
        categoryPagedQuery: this.$API.masterData.categoryPagedQuery,
        queryCompanyAndRel: this.$API.costModel.queryByCompanyAndRel,
        queryRelCostModel: this.$API.costModel.queryRelCostModel,
        pagedQueryUnit: this.$API.masterData.pagedQueryUnit,
        getUserDetail: this.$API.iamService.getUserDetail,
        rfxId: this.$route.query.rfxId,
        sourcingType: this.detailInfo.sourcingType,
        source: this.$route?.query?.source,
        sourcingObjType: this.detailInfo.sourcingObjType,
        companyCode: this.detailInfo.companyCode,
        priceClassification: this.detailInfo.priceClassification,
        getPurGroup: this.$API.masterData.getPurGroup,
        rfxGeneralType: this.detailInfo.rfxGeneralType,
        dictItems: this.dictItems,
        isFc: this.detailInfo.rfxGeneralType === 2,
        massTrialFlagData: this.massTrialFlagData
      }
      const tableColumnMap = {
        rfx_item: rfxItem(rfxItemParams),
        rfx_item_logistics: itemLogistics({
          // 物流信息
          prefix: 'itemLogisticsResponse.',
          dictItems: this.dictItems
        }),
        mt_supplier_rfx_item_ext: itemExt({
          // ?.采方是否可以查询供方表数据
          prefix: 'itemExtMap.',
          isPur: true
        }),
        rfx_item_ext: itemExt({
          prefix: 'itemExtMap.',
          isPur: true,
          dictItems: this.dictItems,
          rfxId: this.$route.query.rfxId,
          source: this.$route?.query?.source,
          sourcingType: this.detailInfo.sourcingType,
          sourcingObjType: this.detailInfo.sourcingObjType
        }),
        rfx_item_die: itemDie({
          // 模具信息
          prefix: 'itemDieResponse.'
        }),
        mt_rfx_annual_logistics_sea_item: itemLogisticsSea({ prefix: 'seaItemResponse.' }),
        logistics_railway_item: itemLogisticsRailway({
          prefix: 'annualLogisticsRailwayItemDTO.'
        }),
        annual_logistics_transport: itemLogisticsTrunk({
          prefix: 'annualLogisticsTrunkItem.',
          dictItems: this.dictItems
        })
      }
      _columns.forEach((item) => {
        let tableName = item.tableName || 'rfx_item'
        let name = tableNameMap[tableName]?.key
        let field = name ? `${name}.${item.fieldCode}` : `${item.fieldCode}`
        let headerName = item.headerName || item.fieldName
        headerName =
          item.fieldCode === 'itemCode' ? headerName + this.$t('（支持粘贴多个物料）') : headerName // headerName 特殊添加
        let editable = false
        // 2.与配置文件中字段进行匹配
        let column = tableColumnMap[tableName]?.find((e) => e.field === field) || {}
        // 3.如果配置文件未配置，则手动添加
        column = this.addEditAttribute({ ...column, fieldCode: item.fieldCode })
        // 4.判断是否可编辑
        /**
         * 1.禁用编辑的字段不允许编辑
         * 2.如果头上配置了该字段，则不允许编辑
         * 3.如果空调，空调禁用字段不允许编辑（价格单位）
         * 4.如果成本因子，成本因子禁用字段不允许编辑
         * 5.如果物流，则采购申请中配置的明细字段不允许编辑（此段逻辑去除）
         * 6.如果配置表里面直接配置editable，取配置表的
         * 7.如果tableName是海运、干线、物流字段，则禁止编辑
         */
        if (
          topInfoFormData[item.fieldCode] ||
          notAllowEditFields.includes(item.fieldCode) ||
          (ktNotAllowEditFields.includes(item.fieldCode) && this.ktFlag == 1) ||
          (this.detailInfo.sourcingObjType === 'cost_factor' &&
            costFactorNotAllowEditFields.includes(item.fieldCode)) ||
          [
            'annual_logistics_transport',
            'mt_rfx_annual_logistics_sea_item',
            'logistics_railway_item',
            'annual_logistics_transport'
          ].includes(tableName)
        ) {
          editable = false
        } else {
          editable = true
          this.logisticsCanEdit = true // 物流如果存在可编辑的字段，则需要显示保存按钮（特殊处理）
        }

        let _one = {
          width: 130, //默认值130
          cellClass: () => {
            return !editable ? 'singleCellDisable' : ''
          },
          editable,
          ...column,
          field,
          headerName,
          tableName,
          required: item.required,
          singleClickEdit: item.fieldCode === 'itemCode' ? false : true
        }

        // 特殊处理 - 物料编码、tconCode、screenCode
        if (['itemCode', 'tconCode', 'screenCode'].includes(item.fieldCode)) {
          _one.editConfig = {
            type: 'cellSelectSearch', //带弹框的下拉框
            props: {
              rfxId: this.$route.query.rfxId,
              source: this.$route.query.source,
              detailInfo: this.detailInfo,
              tableData: this.tableData
            }
          }
        }

        // 特殊处理 - 成本模型、成本测算、图纸协同
        if (['costEstimation', 'drawingUrl'].includes(item.fieldCode)) {
          _one.cellRenderer = 'cellLink'
          _one.cellRendererParams = {
            handleable: true, //"可操作"（不可编辑）（针对于查看类字段）
            rfxId: this.$route.query.rfxId
          }
        }
        // 特殊处理 - 阶梯报价
        if (['stepQuoteName'].includes(item.fieldCode)) {
          _one.editable = false
          _one.cellRenderer = 'cellStep'
          _one.cellRendererParams = {
            handleable: true, //"可操作"（不可编辑）（针对于查看类字段）
            rfxId: this.$route.query.rfxId,
            sourcingObjType: this.detailInfo.sourcingObjType
          }
        }
        // 特殊处理 - 附件（图纸）
        if (['drawing'].includes(item.fieldCode)) {
          _one.cellRenderer = 'cellFile'
          _one.editable = false
          _one.cellRendererParams = {
            handleable: editable, //"可操作"（不可编辑）（针对于附件查看类字段）
            rfxId: this.$route.query.rfxId
          }
        }
        // 特殊处理 - 柜量
        if (['cabinetQuantity'].includes(item.fieldCode)) {
          _one.cellRenderer = 'cellDialog'
          _one.editable = false
          _one.cellRendererParams = {
            handleable: editable, //"可操作"（不可编辑）（针对于附件查看类字段）
            rfxId: this.$route.query.rfxId
          }
        }
        // 特殊处理 - 直送地 光伏直送地为输入框手动输入
        if (this.detailInfo.buType === 'GF' && item.fieldCode === 'deliveryPlace') {
          _one.editConfig = {}
        }
        // 特殊处理 - 直送地 通讯直送地为下拉选择
        if (this.detailInfo.buType === 'TX' && item.fieldCode === 'deliveryPlace') {
          _one.editConfig = {
            type: 'select',
            props: {
              fields: { value: 'dictName', text: 'dictName' },
              'show-clear-button': true,
              dataSource: this.dictItems['DELIVERY_PLACE_TX'] || []
            }
          }
        }
        if (name) {
          if (this.templateData[name]) {
            this.templateData[name][item.fieldCode] = null
          } else {
            this.templateData[name] = {}
          }
        } else {
          this.templateData[item.fieldCode] = null
        }
        // 重置表格是否可以编辑、禁用背景色
        if (!_gridEditable) {
          _one.editable = false
          _one.cellClass = ''
        }
        if (_one.required) {
          this.requiredCols.push(_one.field)
        }
        _columnData.push(_one)
      })
      // 添加勾选框
      if (this.isSelectControl()) {
        _columnData.unshift({ option: 'checkboxSelection', width: 55 })
      }
      return _columnData
    },
    // 表头 - 列字段定义 - 列字段添加编辑属性
    addEditAttribute(column) {
      // 添加属性
      if (generalNumberEditFields.includes(column.fieldCode)) {
        // 通用数字框
        column = {
          ...column,
          option: 'customEdit',
          editConfig: {
            type: 'number',
            props: {
              min: 1
            }
          }
        }
      } else if (fiveDecimalNumberEditFields.includes(column.fieldCode)) {
        // 5位小数数字框
        column = {
          ...column,
          option: 'customEdit',
          editConfig: {
            props: {
              ...PRICE_EDIT_CONFIG,
              precision: 5
            },
            type: 'number'
          }
        }
      } else if (integerNumberEditFields.includes(column.fieldCode)) {
        // 整数数字框
        column = {
          ...column,
          option: 'customEdit',
          editConfig: {
            type: 'number',
            props: {
              min: 1,
              validateDecimalOnType: true,
              decimals: 1,
              format: 'N'
            }
          }
        }
      } else if (priceNumberEditFields.includes(column.fieldCode)) {
        // 价格数字框
        column = {
          ...column,
          option: 'customEdit',
          editConfig: {
            type: 'number',
            props: {
              ...PRICE_EDIT_CONFIG
            }
          }
        }
      } else if (dateEditFields.includes(column.fieldCode)) {
        //日期框
        //编辑状态，日期类型
        column = {
          ...column,
          option: 'customEdit',
          editConfig: {
            type: 'date'
          }
        }
      } else if (booleanEditField.indexOf(column.fieldCode) > -1) {
        //编辑状态，布尔类型
        column = {
          ...column,
          option: 'customEdit',
          editConfig: {
            type: 'select',
            props: {
              fields: { value: 'value', text: 'text' },
              dataSource: [
                { text: this.$t('是'), value: 1 },
                { text: this.$t('否'), value: 0 }
              ]
            }
          }
        }
      }
      return column
    },
    // 初始化 - 字典数据
    async initDictItems() {
      let codeList = [
        { code: 'TradeClause', type: 'string' },
        { code: 'DELIVERY_PLACE', type: 'string' },
        { code: 'DELIVERY_PLACE_TX', type: 'string' },
        { code: 'TransportMode', type: 'string' },
        { code: 'START-PORT', type: 'string' },
        { code: 'DESTINATION-PORT', type: 'string' },
        { code: 'LOGISTICS_DEMAND_TYPE', type: 'string' },
        { code: 'LOGISTICS_METHOD_CODE', type: 'string' }
      ]
      await this.$API.masterData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          this.dictItems = res.data
        }
      })
    },
    // 初始化 - 获取下拉数据
    async getDropDownData() {
      const tasks = []
      // 获取币种
      tasks.push(() =>
        this.$API.masterData.queryAllCurrency().then((res) => {
          this.currencyNameData = res.data || []
        })
      )
      //人员列表
      tasks.push(() =>
        this.$API.masterData
          .getUserPageList({
            condition: '',
            page: {
              current: 1,
              size: 10
            },
            pageFlag: false
          })
          .then((res) => {
            this.receiveUserIdData = res?.data?.records || []
          })
      )
      // 部门列表
      tasks.push(() =>
        this.$API.masterData
          .getDepartmentList({
            departmentName: ''
          })
          .then((res) => {
            this.deptNameList = res.data || []
          })
      )
      // 地点/工厂
      tasks.push(() =>
        this.$API.masterData
          .permissionSiteList({
            buOrgId: sessionStorage.getItem('selectOrgId'),
            companyId: sessionStorage.getItem('selectCompanyId')
          })
          .then((res) => {
            this.siteNameList = res?.data || []
            let siteNameListMap = {}
            for (let item of this.siteNameList) {
              item['siteName'] = item['orgName']
              item['siteCode'] = item['orgCode']
              item['organizationId'] = item['id']
              siteNameListMap[item.siteCode] = item.organizationId
            }
            this.siteNameListMap = siteNameListMap
          })
      )
      // SKU信息 skuName
      tasks.push(() =>
        this.$API.masterData.getSKUList().then((res) => {
          this.skuNameList = res?.data?.records || []
        })
      )
      //税率信息
      tasks.push(() =>
        this.$API.masterData.queryAllTaxItem().then((res) => {
          this.taxRateNameList = res?.data || []
        })
      )
      //基本单位
      tasks.push(() =>
        this.$API.masterData.pagedQueryUnit().then((res) => {
          // 特殊处理单价问题
          let _res = res?.data.records || []
          let s = []
          let o = []
          _res.forEach((item) => {
            if (item.unitCode === 'G' || item.unitCode === 'KG') {
              s.push(item)
            } else {
              o.push(item)
            }
          })
          let n = [...s, ...o]
          this.unitNameList = n || []
          this.purUnitNameList = n || [] // FIXME 采订单单位和基本单位的区别
        })
      )
      // 获取公司编码、公司名称  {companyCode、companyName、id}
      tasks.push(() =>
        this.$API.masterData.permissionCompanyList().then((res) => {
          this.companyNameList = res?.data || []
        })
      )
      // 获取采购组
      tasks.push(() =>
        this.$API.masterData
          .getbussinessGroup({
            groupTypeCode: 'BG001CG'
          })
          .then((res) => {
            this.purGroupList = res?.data || []
          })
      )
      for (const task of sliceArray(tasks, 5)) {
        await Promise.all(task.map((fn) => fn())).catch(() => {})
      }
    },
    // 初始化 - 阶梯默认数据（目前结构阶梯和美工阶梯暂未使用该接口数据）使用中 - 电源板外发、整机外发
    async initStepDefaultData() {
      const res = await this.$API.masterData.dictionaryGetList({
        dictCode: this.sourcingObjType
      })
      this.defaultStepData = []
      res.code === 200 &&
        res.data.map((item) => {
          this.defaultStepData.push({
            startValue: item.itemCode,
            remark: ''
          })
        })
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      this.$store.commit('startLoading')
      let params = this.mergeParams(rules)
      this.tableData = []
      await this.$API.rfxRequireDetail
        .getRFXItem({
          ...params
        })
        .then((res) => {
          if (res?.code === 200) {
            let _records = cloneDeep(res.data?.records || [])
            _records = this.serializeGridList(_records)
            this.tableData = cloneDeep(_records)
          }
          this.$store.commit('endLoading')
        })
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules) {
      let params = {
        defaultRules: [
          {
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            type: 'string',
            value: this.$route.query.rfxId || ''
          }
        ],
        page: { current: 1, size: 10000 }
      }
      if (rules) params = Object.assign({}, params, rules)
      return params
    },
    // 表格数据 - 数据处理
    serializeGridList(list) {
      let _list = []
      let logisticsKey = logisticsTableNameMap[this.detailInfo.sourcingScenarios]?.resKey
      list.forEach((e, index) => {
        e.rowIndex = index + 1
        e.addId = e.id
        e.groupId = uuidv4()
        e.drawing = e.fileList ? JSON.stringify(e.fileList) : null //单独处理附件字段
        e.supplierDrawing = e.supplierFileList ? JSON.stringify(e.supplierFileList) : null //单独处理"供应商附件"字段
        e.stepQuoteName =
          e.itemStageList && e.itemStageList.length > 0 ? cloneDeep(e.itemStageList) : null //单独处理阶梯报价
        if (!e?.itemExtMap) {
          // 避免没有配置扩展字段时候下面赋值直接报错
          e.itemExtMap = {}
        }
        //处理直送地
        e.itemExtMap.deliveryPlace =
          e.deliveryPlaceList && e.deliveryPlaceList?.length ? cloneDeep(e.deliveryPlaceList) : null
        e.stepNum = e.stepValue
        // 处理阶梯类型
        e.stepQuoteType === -1 && (e.stepQuoteType = null)
        // 接口没有返回税率值, 需要前端匹配
        if (e.taxRateCode && !e.taxRateValue) {
          const taxRow = this.taxRateNameList.find((tax) => tax.taxItemCode === e.taxRateCode)
          taxRow && (e.taxRateValue = taxRow.taxRate)
        }
        // 判断金额型和比例型
        if (e.itemExtMap && e.itemExtMap.minQuoteRangeType == 1) {
          e.itemExtMap.minQuoteRange = new Decimal(e.itemExtMap.minQuoteRange)
            .mul(new Decimal(100))
            .toNumber()
        }
        // 销售凭证空值转换
        if (e.itemExtMap && !e.itemExtMap.salesVouchers) {
          e.itemExtMap.salesVouchers = 'empty'
        }
        // 物流阶梯数据处理
        if (e[logisticsKey]?.dynamicFields) {
          const _dynamicFields = e[logisticsKey]?.dynamicFields
          for (let step in _dynamicFields) {
            _dynamicFields[step].forEach((i) => {
              e[logisticsKey][i['fieldCode']] = i['fieldData']
            })
          }
        }
        // 编辑时候子数据不作展开，提交时候展开(TODO 阶梯不作子数据逻辑)
        // if (Array.isArray(e?.childItems)) {
        //   e.childItems = this.serializeGridList(e.childItems)
        //   // 覆盖groupId
        //   e.childItems.forEach((item) => {
        //     item.groupId = e.gropuId
        //   })
        // }
        _list.push(e)
      })
      return _list
    },
    // 表头 - 工具 - 判断是否需要选择框 TODO:添加逻辑判断待整理
    isSelectControl() {
      if ([0].includes(this.detailInfo.transferStatus)) return true
    },
    // 表头 - 获取表头是否可编辑
    getGridEditAble() {
      if (
        this.detailInfo.transferStatus < 1 &&
        // 招投标、竞价: OA审批状态为审核中、审核通过 禁止编辑
        !(
          ['invite_bids', 'bidding_price'].includes(this.$route.query.source) &&
          [1, 2].includes(Number(this.detailInfo.approveStatus))
        )
      ) {
        return true
      }
      return false
    },
    // 表格 - 工具 - 获取表格数据
    getRowData() {
      const rowData = []
      this.agGrid.api.forEachNode(function (node) {
        rowData.push(node.data)
      })
      return rowData
    },
    // 表格 - 工具 - 延时提交
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    }
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化:end   >>>>>>>>>>>>>>>>>>>>>>>>>>>
  }
}
