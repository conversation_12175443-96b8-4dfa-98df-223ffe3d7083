<template>
  <div class="full-height">
    <mt-loading v-if="loading" />
    <mt-template-page
      ref="templateRef"
      class="frozenColumns"
      :template-config="pageConfig"
      @actionBegin="actionBegin"
      @dataBound="dataBound"
      @handleClickToolBar="handleClickToolBar($event, 'parent', {}, this)"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadInitBalanceRef"
      :down-template-name="downTemplateName"
      :request-urls="requestUrls"
      :down-template-params="downTemplateParams"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import clickoutside from '@/directive/clickoutside'
import { v1 as uuidv1, v4 as uuidv4 } from 'uuid'
import { cloneDeep, isEqual } from 'lodash'
import {
  toolbar,
  toolbarNoBatch,
  toolbarNoImport,
  noEditToolbar,
  subToolbar,
  editColumnBefore,
  childEditColumnBefore,
  editSettings,
  notAllowedEditSettings,
  editColumn,
  editFieldCacheMapGenerator,
  numberEditFields,
  priceFields,
  dateEditFields,
  booleanEditField,
  handleSelectChange
} from './config'
import { fmtSelect } from '@/utils/ej/dataGrid/formatter'
import { getValueByPath, setValueByPath } from '@/utils/obj'
import { sliceArray } from '@/utils/arr'
import { utils } from '@mtech-common/utils'
import Decimal from 'decimal.js'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
import { createEditInstance } from '@/utils/ej/dataGrid'
import * as combination from './utils/combination'
import { download, getHeadersFileName } from '@/utils/utils'
import { judgeDataIsSame } from '@/views/common/columnData/utils'
const SYMBOL_PK = 'addId'
const DEFAULTPARAM = {
  condition: '',
  page: {
    current: 1,
    size: 10
  },
  pageFlag: false
}
export default {
  name: 'PurchaseDetail',
  props: {
    fieldDefines: {
      type: Array,
      default: () => []
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    moduleType: {
      type: Number,
      default: () => {}
    },
    ktFlag: {
      type: Number,
      default: () => {}
    }
  },
  components: {
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  data() {
    return {
      ids: [],
      calculation: this.$route.query.calculation == 1 ? true : false,
      loading: false,
      siteNameListMap: {},
      pageConfig: [
        {
          useToolTemplate: false,
          activatedRefresh: false,
          toolbar: noEditToolbar,
          fieldDefines: this.fieldDefines,
          gridId: this.$md5(
            this.$permission.gridId['purchase'][this.$route?.query?.source]['detail']['tabs'][
              'item'
            ] + this.$route?.query?.rfxId
          ),
          // 注释掉的gridIds 是方便查看md5加密之前的数据
          // gridIds:
          //   this.$permission.gridId["purchase"][this.$route?.query?.source][
          //     "detail"
          //   ]["tabs"]["item"] + this.$route?.query?.rfxId,
          grid: {
            customSelection: true,
            virtualPageSize: 100,
            enableVirtualization: true,
            allowFiltering: true,
            lineIndex: 1, // 序号所处列
            columnData: editColumnBefore,
            dataSource: [],
            allowPaging: false,
            allowSorting: false,
            actionComplete: this.actionComplete,
            queryCellInfo: this.customiseCell,
            recordDoubleClick: this.handleChildEndEdit,
            class: 'pe-edit-grid custom-toolbar-grid'
          }
        }
      ],
      rowsSelectedInfo: {},
      requiredCols: [], //必填字段，全局变量
      submitTableData: [], // 新增或修改的数据，最终要提交给后台的
      currencyNameData: null, //币种信息
      supplierNameData: null, //供应商信息
      siteNameList: null, //地点/工厂 信息
      // itemNameList: null, //物料信息
      skuNameList: null, //sku信息
      taxRateNameList: null, //taxRate信息
      receiveUserIdData: null, //人员信息
      unitNameList: null, //单位列表
      purUnitNameList: null, // 订单单位列表
      companyNameList: null, //公司列表
      purGroupList: null, //采购组列表
      deptNameList: null, //部门列表
      dictItems: [], // dict-item 接口统一维护字段
      busEvent: new Set(), // 事件收集
      oldList: [], //接口数据与编辑后数据进行比较
      requestUrls: {},
      downTemplateName: this.$t('采购明细模板'),
      downTemplateParams: {
        page: {
          current: 1,
          size: 10
        },
        rules: []
      },
      newRecordsTag: null,
      localTempRecord: {},
      isEdit: false,
      isBatch: false,
      isSingleModuleFlag: false,
      newItemDieArr: null,
      currentEditGroupId: null,
      txCompanyList: ['2M01', '2S06']
    }
  },
  watch: {
    $route: {
      handler() {
        // 提交成本测算后，需要重新查询数据
        const isSubmitCalculation = localStorage.getItem('isSubmitCalculation')
        if (isSubmitCalculation) {
          this.$refs.templateRef.refreshCurrentGridData()
          localStorage.removeItem('isSubmitCalculation')
        }
      },
      deep: true,
      immediate: true
    }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.loading = true
    sessionStorage.removeItem('siteParam')
    sessionStorage.removeItem('dieItemParam')
    sessionStorage.removeItem('referItemParam')

    await this.initDictItems().catch(() => {})
    await this.getDropDownData().catch(() => {})
    this.loading = false
    this.resetAsyncConfigParams('mounted')
    this.handleUnionColumns()
    this.$bus.$on('contentDialog', () => {
      if (!this.txCompanyList.includes(this.$route.query?.companyCode)) {
        this.$toast({
          content: this.$t('最小采购量需是最小包装数量的整数倍'),
          type: 'warning'
        })
      }
    })
    // 如果是单独模具询报价 isSingleModuleFlag 设置为1
    if (this.detailInfo.sourcingObjType === 'single_module') this.isSingleModuleFlag = true
  },
  beforeDestroy() {
    sessionStorage.removeItem('siteParam')
    sessionStorage.removeItem('dieItemParam')
    sessionStorage.removeItem('referItemParam')

    this.busEvent.forEach((event) => {
      this.$bus.$off(event)
    })
  },
  computed: {
    // 贸易条款
    tradeClauseNameData: function () {
      return this.dictItems.filter((e) => e.dictCode === 'TradeClause')
    },
    // 物流方式
    shippingMethodNameData: function () {
      return this.dictItems.filter((e) => e.dictCode === 'TransportMode')
    },
    // 直送地值集
    deliveryPlaceData: function () {
      return this.dictItems.filter((e) => e.dictCode === 'DELIVERY_PLACE')
    },
    sourceType() {
      return this.$route?.query?.source //rfq | invite_bids | bidding_price
    }
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.dictionaryGetList(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    //单元格icons，点击
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      if (e.tool.id == 'editCost') {
        // this.handleFormulaDialog(e.data);
        if (e.data.id) {
          this.$router.push({
            name: 'purchase-cost',
            query: {
              rfxId: this.$route.query.rfxId,
              rfxItemId: e.data.id,
              costModelId: e.data.costModelId
            }
          })
        } else {
          this.$toast({
            content: this.$t('请保存当前数据后操作！'),
            type: 'warning'
          })
        }
      }
    },
    // 点击单元格内容
    handleClickCellTitle(e) {
      const { field, data } = e
      // 成本模型、成本测算
      if (['costModelName', 'costEstimation'].includes(field)) {
        if (data.id && data.costModelId) {
          this.$router.push({
            name: 'calculation-purchase-cost',
            query: {
              rfxId: this.$route.query.rfxId,
              rfxItemId: data.id,
              type: field === 'costModelName' ? 'costModel' : 'calculation',
              refreshId: Date.now()
            }
          })
        } else {
          this.$toast({
            content: this.$t('请保存当前数据后操作！'),
            type: 'warning'
          })
        }
      }
    },
    isRfqModding() {
      return this.$route?.query?.source === 'rfq'
    },
    // 初始化字典数据
    async initDictItems() {
      // 没有批量查询接口
      const tasks = [
        'TradeClause', // 贸易条款 tradeClauseNameData
        'DELIVERY_PLACE', // 直送地 deliveryPlaceData
        'DELIVERY_PLACE_TX', // 通讯直送地
        'TransportMode', // 物流方式 shippingMethodNameData
        'START-PORT', // 起始港
        'DESTINATION-PORT' // 目的港
      ].map((dictCode) => this.$API.masterData.dictionaryGetList({ dictCode }))
      const result = await Promise.all(tasks).catch((err) => {
        console.error(err)
      })
      if (!result) {
        return
      }
      this.dictItems = result.map((e) => e.data).flat()
    },
    // 不可修改的单元格，修改背景色
    customiseCell(args) {
      if (!args.column.allowEditing && args.column.field !== 'customChecked') {
        args.cell.classList.add('bg-grey')
      }
    },
    // 获取下拉数据
    async getDropDownData() {
      if (
        this.currencyNameData &&
        this.supplierNameData &&
        this.siteNameList &&
        this.skuNameList &&
        this.taxRateNameList &&
        this.tradeClauseNameData &&
        this.shippingMethodNameData &&
        this.receiveUserIdData &&
        this.unitNameList &&
        this.purUnitNameList &&
        this.deptNameList &&
        this.companyNameList &&
        this.purGroupList &&
        this.deliveryPlaceData
      ) {
        this.handleUnionColumns()
        return
      }

      const tasks = []

      // 获取币种
      tasks.push(() =>
        this.$API.masterData.queryAllCurrency().then((res) => {
          this.currencyNameData = res.data || []
        })
      )

      //人员列表
      tasks.push(() =>
        this.$API.masterData.getUserPageList(DEFAULTPARAM).then((res) => {
          this.receiveUserIdData = res?.data?.records || []
        })
      )

      // // 推荐供应商
      // await this.$API.masterData.getSupplierList().then((res) => {
      //   this.supplierNameData = res.data || [];
      // });
      this.supplierNameData = []

      // 部门列表
      tasks.push(() =>
        this.$API.masterData
          .getDepartmentList({
            departmentName: ''
          })
          .then((res) => {
            this.deptNameList = res.data || []
          })
      )

      // 地点/工厂
      tasks.push(() =>
        this.$API.masterData
          .permissionSiteList({
            buOrgId: sessionStorage.getItem('selectOrgId'),
            companyId: sessionStorage.getItem('selectCompanyId')
          })
          .then((res) => {
            this.siteNameList = res?.data || []
            let siteNameListMap = {}
            for (let item of this.siteNameList) {
              item['siteName'] = item['orgName']
              item['siteCode'] = item['orgCode']
              item['organizationId'] = item['id']
              siteNameListMap[item.siteCode] = item.organizationId
            }
            this.siteNameListMap = siteNameListMap
          })
      )
      // // 物料信息 itemName
      // tasks.push(() =>
      //   this.$API.masterData.getItemList().then((res) => {
      //     //逻辑需求：(新逻辑：品类是品项带出来的，不能独立输入)，选择品项，带出品类信息
      //     res.data.forEach((e) => {
      //       if (e?.categoryResponse) {
      //         e.categoryName =
      //           e?.categoryResponse?.categoryName ?? this.$t("未维护");
      //         e.categoryCode =
      //           e?.categoryResponse?.categoryCode ?? this.$t("未维护");
      //         e.categoryId = e?.categoryResponse?.id ?? 0;
      //       } else {
      //         e.categoryName = this.$t("未维护");
      //         e.categoryCode = this.$t("未维护");
      //         e.categoryId = 0;
      //       }
      //     });
      //     this.itemNameList = res?.data || [];
      //   })
      // );
      // SKU信息 skuName
      tasks.push(() =>
        this.$API.masterData.getSKUList().then((res) => {
          this.skuNameList = res?.data?.records || []
        })
      )

      //税率信息
      tasks.push(() =>
        this.$API.masterData.queryAllTaxItem().then((res) => {
          this.taxRateNameList = res?.data || []
        })
      )

      //基本单位
      tasks.push(() =>
        this.$API.masterData.pagedQueryUnit().then((res) => {
          // 特殊处理单价问题
          let _res = res?.data.records || []
          let s = []
          let o = []
          _res.forEach((item) => {
            if (item.unitCode === 'G' || item.unitCode === 'KG') {
              s.push(item)
            } else {
              o.push(item)
            }
          })
          let n = [...s, ...o]
          this.unitNameList = n || []
          this.purUnitNameList = n || [] // FIXME 采订单单位和基本单位的区别
        })
      )

      // 获取公司编码、公司名称  {companyCode、companyName、id}
      tasks.push(() =>
        this.$API.masterData.permissionCompanyList().then((res) => {
          this.companyNameList = res?.data || []
        })
      )

      // 获取采购组
      tasks.push(() =>
        this.$API.masterData
          .getbussinessGroup({
            groupTypeCode: 'BG001CG'
          })
          .then((res) => {
            this.purGroupList = res?.data || []
          })
      )

      for (const task of sliceArray(tasks, 5)) {
        await Promise.all(task.map((fn) => fn())).catch(() => {})
      }
    },
    // 组合列
    handleUnionColumns() {
      this.requiredCols = []
      let params = {
        currencyNameData: this.currencyNameData,
        supplierNameData: this.supplierNameData,
        siteNameList: this.siteNameList,
        // itemNameList: this.itemNameList,
        skuNameList: this.skuNameList,
        taxRateNameList: this.taxRateNameList,
        shippingMethodNameData: this.shippingMethodNameData,
        tradeClauseNameData: this.tradeClauseNameData,
        receiveUserIdData: this.receiveUserIdData,
        unitNameList: this.unitNameList, // 基本单位
        purUnitNameList: this.purUnitNameList, // 采购单位
        companyNameList: this.companyNameList,
        purGroupList: this.purGroupList,
        deptNameList: this.deptNameList,
        dictItems: this.dictItems,
        detailInfo: this.detailInfo,
        getCategoryList: this.$API.masterData.getCategoryList,
        categoryPagedQuery: this.$API.masterData.categoryPagedQuery,
        queryCompanyAndRel: this.$API.costModel.queryByCompanyAndRel,
        queryRelCostModel: this.$API.costModel.queryRelCostModel,
        pagedQueryUnit: this.$API.masterData.pagedQueryUnit,
        getUserDetail: this.$API.iamService.getUserDetail,
        rfxId: this.$route.query.rfxId,
        submitTableData: () => this.submitTableData,
        sourcingType: this.detailInfo.sourcingType,
        source: this.$route?.query?.source,
        self: this,
        sourcingObjType: this.detailInfo.sourcingObjType,
        companyCode: this.detailInfo.companyCode,
        priceClassification: this.detailInfo.priceClassification,
        getPurGroup: this.$API.masterData.getPurGroup,
        rfxGeneralType: this.detailInfo.rfxGeneralType
      }
      this.editColumns = editColumn(params, {
        $on: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$on(event, fn)
        },
        $emit: (event, fn) => {
          this.busEvent.add(event)
          this.$bus.$emit(event, fn)
        }
      })
      if (Array.isArray(this.fieldDefines) && this.fieldDefines.length) {
        let _columnData = this.defineGridColumns(this.fieldDefines)
        editFieldCacheMapGenerator(_columnData, editSettings)
        this.defineGridEditStatus()
        this.$set(this.pageConfig[0].grid, 'columnData', _columnData)
      }
      if (Array.isArray(this.childFields) && this.childFields.length) {
        let _columnData = this.defineGridColumns(this.childFields, 'child')
        if (combination.isCombination(this.detailInfo)) {
          _columnData = combination.defineGridColumnsAfter(_columnData)
        }
        editFieldCacheMapGenerator(_columnData, editSettings)
        let _this = this
        let allowEditing =
          this.detailInfo.transferStatus < 1 &&
          // 招投标、竞价: OA审批状态为审核中、审核通过 禁止编辑
          !(
            ['invite_bids', 'bidding_price'].includes(this.$route.query.source) &&
            [1, 2].includes(Number(this.detailInfo.approveStatus))
          )
        let _editSettings = {
          ...editSettings,
          allowEditing
        }
        let isHierarchy = this.detailInfo.sourcingObjType === 'HIERARCHY' ? true : false

        this.$set(this.pageConfig[0].grid, 'detailTemplate', function () {
          return {
            template: Vue.component('detailTemplate', {
              template: `<div :class="className" style="padding:10px 0 15px;">
                          <mt-template-page
                            ref="childRef"
                            v-clickoutside="clickoutside"
                            :template-config="childTable"
                            @actionBegin="subActionBegin"
                            @handleClickToolBar="subClickToolBar"
                          ></mt-template-page>
                        </div>`,
              directives: { clickoutside: clickoutside },
              data: function () {
                return {
                  className: isHierarchy ? 'hierarchy-template' : '',
                  data: {},
                  childTable: [
                    {
                      useToolTemplate: false,
                      useBaseConfig: false,
                      activatedRefresh: false,
                      toolbar: allowEditing ? (isHierarchy ? [] : [subToolbar]) : [],
                      grid: {
                        height: 'auto',
                        allowPaging: false,
                        page: {
                          current: 1,
                          size: 1000
                        },
                        allowEditing,
                        editSettings: _editSettings,
                        dataSource: [],
                        columnData: _columnData,
                        queryCellInfo: _this.customiseCell,
                        recordDoubleClick: this.subDoubleClick,
                        class: 'pe-edit-grid custom-toolbar-grid'
                      }
                    }
                  ]
                }
              },
              created() {
                this.childTable[0].grid.actionComplete = (arg) => _this.actionComplete(arg, this)
              },
              mounted() {
                let data = this.data
                console.log('detail-table', data)
                setTimeout(() => {
                  this.childTable[0].grid.dataSource = data?.childItems
                }, 0)
                this.$bus.$on(`subPurchaseGridEndEdit-${data.addId}`, () => {
                  let _childRef = this?.$refs?.childRef
                  let _current = _childRef?.getCurrentTabRef()
                  if (_current?.grid) {
                    _current?.grid.endEdit()
                  }
                })
                this.$bus.$on(`subClearRowSelection-${data.addId}`, () => {
                  let _childRef = this?.$refs?.childRef
                  let _current = _childRef?.getCurrentTabRef()
                  if (_current?.grid) {
                    _current?.grid.clearRowSelection()
                  }
                })
                // 设置子表格数据
                this.$bus.$on(`subDataSet-${data.groupId}`, (res) => {
                  this.childTable[0].grid.dataSource = cloneDeep(res)
                })
                // 设置当前子表格数据失焦
                this.$bus.$on(`subEndEdit-${data.groupId}`, () => {
                  this.$refs.childRef.getCurrentTabRef().grid.endEdit()
                })
                combination.detailTemplateMounted({
                  pVm: _this,
                  sVm: this,
                  SYMBOL_PK
                })
              },
              beforeDestroy() {
                combination.detailTemplateBeforeDestroy(_this)
                this.$bus.$off(`subPurchaseGridEndEdit-${this.data.addId}`)
                this.$bus.$off(`subClearRowSelection-${this.data.addId}`)
                this.$bus.$off(`subDataSet-${this.data.groupId}`)
                this.$bus.$off(`subEndEdit-${this.data.groupId}`)
              },
              methods: {
                clickoutside() {
                  this.$refs.childRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
                },
                subClickToolBar(e) {
                  _this.handleClickToolBar(e, 'subItems', this.data, this)
                  if (e.toolbar.id === 'subSave') {
                    this.$refs.childRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
                  }
                },
                subActionBegin(e) {
                  _this.actionBegin(e, 'subitem')
                },
                subDoubleClick() {
                  _this.handleParentEndEdit(this.data.addId)
                }
              }
            })
          }
        })
      }
    },
    defineGridColumns(fieldDefines, type) {
      let _columnData =
        type === 'child' ? cloneDeep(childEditColumnBefore) : cloneDeep(editColumnBefore)
      let _originColumns = cloneDeep(fieldDefines)

      // _originColumns = this.handleColumnData(_originColumns)
      let topInfoFormData = this.$parent.$refs.topInfoRef.formData
      const editInstance = createEditInstance()
      if (Array.isArray(_originColumns) && _originColumns.length) {
        _originColumns.forEach((col) => {
          let _field = { field: col.fieldCode }
          if (col.tableName == 'rfx_item_ext' && col.fieldCode != 'drawingUrl') {
            _field = {
              field: `itemExtMap.${col.fieldCode}`
            }
          } else if (col.tableName.trim() === 'mt_supplier_rfx_item_ext') {
            _field = {
              field: `itemExtMap.${col.fieldCode}`
            }
          } else if (col.tableName == 'rfx_item_die') {
            _field = {
              field: `itemDieResponse.${col.fieldCode}`
            }
          } else if (col.tableName == 'rfx_item_logistics') {
            _field = {
              field: `itemLogisticsResponse.${col.fieldCode}`
            }
          }
          var _one = {
            ...col,
            ..._field,
            // field: col.fieldCode,
            headerText: this.$t(col.fieldName),
            width: '150'
          }
          // 如果是必填 0-非必填；1-必填；2-无需配置
          if (_one.required == 1) {
            this.requiredCols.push({
              field: _one.field,
              headerText: this.$t(_one.fieldName)
            })
            //设置必填-表头
            _one.headerTemplate = () => {
              return {
                template: Vue.component('requiredCell', {
                  template: `
                        <div class="headers">
                          <span style="color: red">*</span>
                          <span class="e-headertext">{{fieldName}}</span>
                        </div>
                      `,
                  data() {
                    return {
                      data: {},
                      fieldName: ''
                    }
                  },
                  mounted() {
                    this.fieldName = this.$t(_one.fieldName)
                  }
                })
              }
            }
          }
          // 如果该列是下拉或者其他类型的复杂情况
          let editFind = this.editColumns.find((editCol) => _one.field == editCol.field)
          let _allowEditing = _one?.allowEditing === false ? false : true
          if (editFind) {
            _one = {
              ..._one,
              ...editFind,
              width: editFind.width || '150'
            }
            _allowEditing = editFind.allowEditing === false ? false : true
          } else if (numberEditFields.indexOf(_one.fieldCode) > -1) {
            if (_one.fieldCode == 'requireQuantity') {
              //需求数量展示整数
              _one = {
                ..._one,
                editConfig: {
                  type: 'number',
                  min: 1,
                  validateDecimalOnType: true,
                  decimals: 1,
                  format: 'N'
                }
              }
            } else if (priceFields.indexOf(_one.fieldCode) > -1) {
              _one = {
                ..._one,
                editConfig: {
                  ...PRICE_EDIT_CONFIG,
                  type: 'number'
                }
              }
            } else {
              //编辑状态，数字类型
              _one = {
                ..._one,
                editConfig: {
                  type: 'number',
                  min: 1
                }
              }
            }
          } else if (dateEditFields.indexOf(_one.fieldCode) > -1) {
            //编辑状态，日期类型
            _one = {
              ..._one,
              editType: 'datepickeredit',
              type: 'date',
              format: 'yyyy-MM-dd',
              editConfig: {
                type: 'date'
              }
            }
          } else if (booleanEditField.indexOf(_one.fieldCode) > -1) {
            //编辑状态，布尔类型
            _one = {
              formatter: fmtSelect,
              ..._one,
              editConfig: {
                type: 'select',
                fields: { value: 'value', text: 'text' },
                dataSource: [
                  { text: this.$t('是'), value: 1 },
                  { text: this.$t('否'), value: 0 }
                ],
                placeholder: this.$t('选择'),
                callback: handleSelectChange
              }
            }
          }

          if (
            topInfoFormData[_one.field] != undefined &&
            topInfoFormData[_one.field] != '' &&
            topInfoFormData[_one.field] != null
          ) {
            _allowEditing = false
          }
          _one.allowEditing = _allowEditing
          if (_one.fieldCode == 'conversionRate') {
            _one.edit = editInstance.create({
              getEditConfig: () => ({
                type: 'number',
                max: 999999999999,
                min: 0,
                precision: 2
              })
            })
          }
          if (_one.field == 'priceUnitName' && this.ktFlag == 1) {
            _one.allowEditing = false
            delete _one.edit
          }
          // 成本因子禁用字段
          let _costFactorDisableField = [
            'itemCode',
            'itemName',
            'unitCode',
            'unitName',
            'minQuotePercent',
            'minQuotePercent'
          ]
          if (
            this.detailInfo.sourcingObjType === 'cost_factor' &&
            _costFactorDisableField.includes(_one.field)
          ) {
            _one.allowEditing = false
            delete _one.edit
          }
          _columnData.push(_one)
        })
      }
      return _columnData
    },
    //行内进入事件
    actionBegin(args, type) {
      if (args.rowData) {
        // 处理deliverPlaceList
        if (args.rowData?.itemExtMap?.deliveryPlace) {
          let _deliveryPlace = args.rowData.itemExtMap.deliveryPlace
          args.rowData.itemExtMap.deliveryPlace = Array.isArray(_deliveryPlace)
            ? cloneDeep(_deliveryPlace)
            : _deliveryPlace.split(',')
        }
        const data = {
          referChannel: args.rowData.itemExtMap?.referChannel,
          stepQuoteType: args.rowData.stepQuoteType,
          sourcingObjType: this.detailInfo.sourcingObjType
        }
        sessionStorage.setItem('referItemParam', JSON.stringify(data))
      }
      // 新增时赋初始值
      if (args.requestType === 'save' && args.action == 'add') {
        let tempRecord = cloneDeep(this.detailInfo)
        let parentItem = this.submitTableData.find((item) => !item.parentRfxItemKey)
        // 组合结构直送地默认值处理(1503默认惠州)
        let _defaultDeliveryPlace =
          tempRecord.sourcingObjType === 'combination' && type === 'subitem'
            ? parentItem?.itemExtMap?.deliveryPlace
            : this.detailInfo.companyCode === '1503'
            ? [this.$t('惠州')]
            : []
        if (this.detailInfo.siteId) {
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: this.detailInfo.siteId,
              siteCode: this.detailInfo.siteCode
            })
          )
        }
        tempRecord.id = undefined
        const isUsePriceUnit = this.hasColumn('priceUnitName')
        if (tempRecord.sourcingObjType) {
          Object.assign(tempRecord, {
            itemLogisticsResponse: {
              transportType: this.detailInfo.sourcingObjType
            },
            itemExtMap: {
              referChannel:
                type !== 'subitem' && tempRecord.sourcingObjType === 'combination' ? 2 : 0,
              deliveryPlace: _defaultDeliveryPlace
            },
            itemDieResponse: {
              addUp: 1, //累计至本次  默认'是'
              shareFlag: 0 //是否分摊量 默认'否'
            },
            rfxItemKey: uuidv1(),
            priceUnitName: this.txCompanyList.includes(this.detailInfo.companyCode)
              ? '1'
              : this.detailInfo.rfxGeneralType === 1 // 如果是通采则设定默认值；
              ? this.ktFlag == 1 ||
                ['single_module', 'COMMON_OVERSEA_CKD'].includes(this.detailInfo.sourcingObjType) ||
                this.detailInfo.companyCode === '0602' //kt 、模具、海外通用询报价默认单位为1、越南公司
                ? '1'
                : '1000'
              : isUsePriceUnit // 非采分为配置了价格单位跟没有配置价格单位两种情况
              ? ''
              : '1'
          })
        }
        if (this.detailInfo.sourcingObjType === 'combination') {
          tempRecord.childItems = []
        }
        // 结构件、美工件阶梯字段设置默认值

        if (['structure_component', 'artist_component'].includes(this.detailInfo.sourcingObjType)) {
          let itemStages = [
            { remark: '', startValue: '1' },
            { remark: '', startValue: '500' },
            { remark: '', startValue: '2000' },
            { remark: '', startValue: '3000' }
          ]
          tempRecord['stepQuote'] = 1
          tempRecord['stepQuoteType'] = 3
          tempRecord['stepQuoteName'] = JSON.stringify(itemStages)
          tempRecord['itemStageList'] = cloneDeep(itemStages)
        }
        tempRecord[SYMBOL_PK] = uuidv1()
        tempRecord['groupId'] = uuidv1()
        // 是否阶梯默认为否
        if (['power_panel', 'module_out_going'].includes(this.detailInfo.sourcingObjType)) {
          tempRecord['stepQuote'] = 0
          tempRecord['stepQuoteType'] = null
        }
        args.rowData = {
          ...args.rowData,
          ...tempRecord,
          ...this.localTempRecord
        }
        args.data = { ...args.rowData, ...tempRecord, ...this.localTempRecord }
        // args.data.addId = args.data.addId || uuidv1();
      } else if (args.requestType === 'save' && args.action == 'edit') {
        // 部品模具时候，校验父级数据
        let isHierarchy = this.detailInfo.sourcingObjType === 'HIERARCHY' ? true : false
        if (type !== 'subitem' && isHierarchy) {
          const _flag = this.validParentData(args.data)
          if (!_flag) args.cancel = true
        }
        if (args.rowData.stepQuoteName && args.rowData.itemStageList) {
          args.data = Object.assign({}, args.data, {
            stepQuoteName: args.rowData.stepQuoteName,
            itemStageList: args.rowData.itemStageList
          })
        }
      } else if (args.requestType === 'beginEdit') {
        this.defineRowSiteInfo(args.rowData)
        if (type === 'subitem') {
          this.currentEditGroupId = args.rowData?.groupId
        }
      }
    },
    validParentData(data) {
      if (!data.itemCode && !data.itemName) {
        this.$toast({ content: this.$t('请选择父级物料信息'), type: 'warning' })
        return false
      }

      if (!data?.itemDieResponse?.dieType) {
        this.$toast({ content: this.$t('请选择父级模具类型'), type: 'warning' })
        return false
      }
      return true
    },
    //grid data-bound
    dataBound() {
      if (Array.isArray(this.childFields) && this.childFields.length) {
        //数据加载后，如果是父子结构，展开
        let _current = this.$refs.templateRef.getCurrentTabRef()
        if (_current?.grid) _current.grid.detailExpandAll()
      }
    },
    //行内操作，获取当前行'工厂信息'
    defineRowSiteInfo(row) {
      const defineSiteInfo = (rowData) => {
        if (rowData?.siteCode != undefined && rowData?.siteCode != '') {
          let organizationId = this.siteNameList.filter(
            (detail) => detail.siteCode == rowData.siteCode
          )[0].organizationId
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: organizationId,
              siteCode: rowData.siteCode
            })
          )
        } else {
          sessionStorage.removeItem('siteParam')
        }
      }

      if (row?.parentRfxItemKey && this.detailInfo.sourcingObjType !== 'combination') {
        //子层结构  获取工厂信息
        let _parent = this.submitTableData.find((item) => row.parentRfxItemKey == item.rfxItemKey)
        defineSiteInfo(_parent)
      } else {
        //父层结构  获取工厂信息
        defineSiteInfo(row)
      }
    },
    //行内操作，获取当前行'物料信息'
    defineRowItemInfo(row) {
      const defineItemInfo = (rowData) => {
        if (rowData?.itemCode != undefined && rowData?.itemCode != '') {
          sessionStorage.setItem(
            'dieItemParam',
            JSON.stringify({
              itemCode: rowData?.itemCode,
              dieType: rowData?.itemDieResponse?.dieType,
              priceClassify: [1, 2].includes(rowData?.itemDieResponse?.dieType) ? 1 : 2
            })
          )
        } else {
          sessionStorage.removeItem('dieItemParam')
        }
      }

      if (row?.parentRfxItemKey) {
        //子层结构  获取父级物料信息
        let _parent = this.submitTableData.find((item) => row.parentRfxItemKey == item.rfxItemKey)
        let isHierarchy = this.detailInfo.sourcingObjType === 'HIERARCHY' ? true : false
        if (isHierarchy) {
          defineItemInfo(row) // 部品模具 取当前行的信息，不传物料信息
        } else {
          defineItemInfo(_parent)
        }
      } else {
        //父层结构  获取物料信息
        defineItemInfo(row)
      }
    },

    async actionComplete(args, vm) {
      if (!args.action) {
        if (args.requestType == 'add') {
          // args.rowData.addId = uuidv1();
        } else if (args.requestType == 'beginEdit') {
          this.defineRowSiteInfo(args.rowData)
          this.defineRowItemInfo(args.rowData)
        } else if (args.requestType == 'save') {
          sessionStorage.removeItem('siteParam')
          sessionStorage.removeItem('dieItemParam')
          sessionStorage.removeItem('referItemParam')
        } else if (args.requestType == 'delete') {
          this.handleDeleteRow(args)
        }
        return
      }
      if (args.requestType == 'save') {
        //数据存储
        if (args.action == 'add') {
          if (this.isBatch) {
            this.clearRowSelection()
            setTimeout(() => {
              this.isBatch = false
            }, 2500)
            return
          }
          //新增操作
          // this.submitTableData = []
          if (!vm) {
            let dataSource = cloneDeep(this.submitTableData)
            if (args.previousData?.siteCode || args.previousData?.siteName) {
              args.rowData.siteCode = args.previousData.siteCode
              args.rowData.siteName = args.previousData.siteName
            }
            if (args.previousData?.priceUnitName) {
              args.rowData.priceUnitName = args.previousData.priceUnitName
            }
            if (this.detailInfo.sourcingObjType === 'module_out_buy') {
              args.rowData.itemExtMap.salesVouchers = 'empty' // 销售凭证默认空
            }
            dataSource.unshift(args.rowData)
            this.$set(this.pageConfig[0].grid, 'dataSource', dataSource)
            this.submitTableData.unshift(args.rowData)
          } else {
            let childDataSource = vm.childTable[0].grid.dataSource || []
            if (args.previousData?.siteCode || args.previousData?.siteName) {
              args.rowData.siteCode = args.previousData.siteCode
              args.rowData.siteName = args.previousData.siteName
            }
            if (this.detailInfo.sourcingObjType == 'combination') {
              args.data.parentRfxItemKey = this.newRecordsTag.data.rfxItemKey
              args.rowData.parentRfxItemKey = this.newRecordsTag.data.rfxItemKey
              childDataSource.unshift(args.rowData)
              this.$set(vm.childTable[0].grid, 'dataSource', childDataSource)
              this.submitTableData.push(args.rowData)
            } else {
              args.data.parentRfxItemKey = this.newRecordsTag.data.rfxItemKey
              args.rowData.parentRfxItemKey = this.newRecordsTag.data.rfxItemKey
              childDataSource.unshift(args.rowData)
              this.$set(vm.childTable[0].grid, 'dataSource', childDataSource)
              this.submitTableData.push(args.rowData)
            }
          }
          this.handleAddRow(args)
          return
        } else {
          //编辑操作
          if (!isEqual(args.data, args.previousData)) {
            // 整合编辑数据
            if (vm && this.detailInfo.sourcingObjType === 'HIERARCHY') {
              // 部品模具子级
              this.intergrateChildEditData([args.data], 'update', vm)
            } else {
              this.intergrateEditData(args.data, vm) // 父级 或 CKD子级
            }
          }
          // 部品模具时 根据父级模具类型带出子级数据
          if (!vm && this.detailInfo.sourcingObjType === 'HIERARCHY') {
            let _templateData = cloneDeep({ ...args.data })
            let base = null
            let modify = null
            if (args.data.itemCode && this.detailInfo.sourcingType === 'exist') {
              // 当询价类型已有时候 父级带出子级时通过价格记录的信息带出
              await this.getNewItemDie(args.data.itemCode, args.data?.itemDieResponse?.dieType)
              base = this.newItemDieArr?.find((item) => item.dieType === 1 || item.dieType === 2)
              modify = this.newItemDieArr?.find((item) => item.dieType === 3 || item.dieType === 4)
            }
            setValueByPath(_templateData, 'itemExtMap.referChannel', 0) //价格来源默认手工定价
            setValueByPath(_templateData, 'itemDieResponse.shareFlag', 0) //是否分摊默认为否
            setValueByPath(_templateData, 'priceUnitName', '1') //价格单位为1
            _templateData.parentRfxItemKey = args.data.rfxItemKey
            const _dieType = args.data?.itemDieResponse?.dieType
            let _data = []
            const _submitTableData = cloneDeep(this.submitTableData)
            // 失焦后重置子表
            _submitTableData.forEach((item) => {
              if (item.rfxItemKey === args.data.rfxItemKey) {
                let _childItems = item.childItems
                let _ids = _childItems?.map((item) => item.id)
                _ids && this.ids.push(..._ids)
                item.childItems = []
              }
            })
            this.submitTableData = cloneDeep(_submitTableData)
            _templateData.id = ''
            if ([3, 4].includes(_dieType)) {
              // 基础模改模、复制模改模
              for (let i = 0; i < 2; i++) {
                let _addId = uuidv4()
                let _rfxItemKey = uuidv4()
                let _d = cloneDeep(_templateData)
                _d.addId = _addId
                _d.rfxItemKey = _rfxItemKey
                if (i === 0) {
                  _d.itemDieResponse.dieType = _d.itemDieResponse.dieType === 3 ? 1 : 2
                  _d.itemExtMap.referChannel = 2
                  if (base) {
                    let res = this.setDieItemValue(_d, base)
                    _d = cloneDeep(res)
                  }
                  // 询价类型非已有，价格来源为价格记录时请求价格记录接口渲染子级第一条数据
                  if (args.data.itemCode && this.detailInfo.sourcingType !== 'exist') {
                    await this.getNewItemDie(args.data.itemCode, _d.itemDieResponse.dieType)
                    const _res = this.setDieItemValue(_d, this.newItemDieArr[0])
                    _d = cloneDeep(_res)
                  }
                }
                if (i === 1 && modify) {
                  let res = this.setDieItemValue(_d, modify)
                  _d = cloneDeep(res)
                }

                _data.push(_d)
              }
            } else {
              // 基础模具、复制模具
              if (base) {
                let res = this.setDieItemValue(_templateData, base)
                _templateData = cloneDeep(res)
              }
              _templateData.addId = uuidv4()
              _templateData.rfxItemKey = uuidv4()
              _data.push(_templateData)
            }
            this.intergrateChildEditData(_data)
            this.$bus.$emit(`subDataSet-${args.data.groupId}`, _data)
          }
        }
        // 清除选中行
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        this.isEdit = false
      }
    },
    // 获取模具编码最新记录信息
    async getNewItemDie(itemCode, dieType) {
      let params = {
        itemCodes: [itemCode],
        dieType,
        requestParams: { page: { current: 1, size: 20 } }
      }
      await this.$API.rfxExt.queryMoldByItemCode(params).then((res) => {
        if (res.code === 200) {
          let _res = res.data ? [...res.data] : []
          _res?.forEach((item) => {
            item.dieFormalCode = item.dieCode
          })
          this.newItemDieArr = [..._res]
        }
      })
    },
    setDieItemValue(templateData, data) {
      if (!data || data.length <= 0) return templateData
      const fieldMap = {
        'itemDieResponse.dieFormalCode': 'dieFormalCode', //正式模具编码
        'itemDieResponse.dieType': 'dieType', //模具类型
        // 'itemDieResponse.taxedUnitPrice': 'taxedUnitPrice', //未税单价
        // 'itemDieResponse.untaxedUnitPrice': 'untaxedUnitPrice', //含税单价
        // 'itemDieResponse.planSharePriceTaxed': 'planSharePriceTaxed', //规划分摊价（含税）
        // 'itemDieResponse.planSharePriceUntaxed': 'planSharePriceUntaxed', //规划分摊价（未税）
        // 'itemDieResponse.realSharePriceTaxed': 'realSharePriceTaxed', //实际分摊价（含税）
        // 'itemDieResponse.realSharePriceUntaxed': 'realSharePriceUntaxed', //实际分摊价（未税）
        // 'itemDieResponse.sharePriceTaxed': 'sharePriceTaxed', //分摊价格（含税）
        // 'itemDieResponse.sharePriceUntaxed': 'sharePriceUntaxed', //分摊价格（未税）
        'itemDieResponse.shareQuantity': 'shareQuantity', //实际分摊数量
        'itemDieResponse.planQuantity': 'planQuantity', //规划量
        supplierId: 'supplierId', //供应商ID
        supplierName: 'supplierName', //供应商名称
        supplierCode: 'supplierCode' //供应商编码
      }
      Object.entries(fieldMap).map(([field, key]) => {
        setValueByPath(templateData, field, data[key])
      })
      return templateData
    },
    // 新增的完成事件
    handleAddRow(args) {
      args.data.addId = args.data.addId || uuidv1() // 保证主数据的值存在
      let { tag, data } = this.newRecordsTag
      if (tag === 'parent') {
        args.data.parentRfxItemKey = ''
      } else {
        args.data.parentRfxItemKey = data?.rfxItemKey
      }
      let tempRecord = cloneDeep(this.detailInfo)
      tempRecord.id = undefined
      args.data = { ...tempRecord, ...args.data }
      // args.rowData = { ...tempRecord, ...args.data };
      this.resetNewRecordsTag()
      this.mergeDetailInfoCurrency(args.data)
      this.intergrateEditData(args.data)
      if (tag === 'parent') {
        this.clearRowSelection()
      } else {
        this.clearChildRowSelection()
      }
    },
    // 重置子级表格勾选
    clearChildRowSelection() {
      this.submitTableData.forEach((data) => {
        this.$bus.$emit(`subClearRowSelection-${data.id}`)
      })
    },
    // 合并单据币种
    mergeDetailInfoCurrency(data) {
      if (!this.detailInfo.currencyCode) {
        return
      }
      const currency = this.currencyNameData.find(
        ({ currencyCode }) => currencyCode === this.detailInfo.currencyCode
      )
      if (currency) {
        data.currencyCode = currency.currencyCode
        data.currencyName = currency.currencyName
      }
    },
    // 整合编辑数据，更新到提交数据里
    intergrateEditData(rowData, vm) {
      combination.intergrateEditDataBefore(rowData, vm)
      let _name = rowData.id ? 'id' : 'addId'
      let _index = this.submitTableData.findIndex((item) => rowData[_name] == item[_name])
      if (_index > -1) {
        this.$set(this.submitTableData, _index, rowData)
      } else {
        this.submitTableData.push(rowData)
      }
    },
    // 整合子级编辑的数据，更新到提交数据里（子级数据不作展平）
    intergrateChildEditData(rowDatas, type, vm) {
      if (!rowDatas || rowDatas?.length <= 0) return
      let _childItems = cloneDeep(rowDatas)
      let _parentRfxItemKey = _childItems[0].parentRfxItemKey
      let rfxItemKey = _childItems[0].rfxItemKey
      this.submitTableData.forEach((item) => {
        if (item.rfxItemKey === _parentRfxItemKey) {
          if (type === 'update') {
            // 数据更新
            let _findIndex = item.childItems?.findIndex((cItem) => cItem.rfxItemKey === rfxItemKey)
            item.childItems[_findIndex] = _childItems[0]
            vm.$set(vm.childTable[0].grid.dataSource, _findIndex, _childItems[0])
          } else {
            //数据添加
            item.childItems = _childItems
          }
        }
      })
    },
    // 子集数据展平
    flattenSubmitData(data) {
      if (!data || !data?.length) return []
      let _data = []
      data.map((item) => {
        _data.push(item)
        if (item.childItems) {
          _data = _data.concat(item.childItems)
        }
      })
      return _data
    },
    // 删除事件
    handleDeleteRow(args) {
      args.data.forEach((item) => {
        // 处理 submitTableData
        let findIndex = this.submitTableData.findIndex((i) => i.addId == item.addId)
        if (findIndex >= 0) {
          this.submitTableData.splice(findIndex, 1)
        }
        let findDataIndex = this.pageConfig[0].grid.dataSource.findIndex(
          (x) => x.addId == item.addId
        )
        if (findDataIndex >= 0) {
          this.pageConfig[0].grid.dataSource.splice(findIndex, 1)
        }
      })
    },
    serializeGridList(list) {
      let _list = []
      list.forEach((e) => {
        e.addId = e.id
        e.groupId = uuidv4()
        e.drawing = e.fileList ? JSON.stringify(e.fileList) : null //单独处理附件字段
        e.supplierDrawing = e.supplierFileList ? JSON.stringify(e.supplierFileList) : null //单独处理"供应商附件"字段
        e.stepQuoteName =
          e.itemStageList && e.itemStageList.length > 0 ? utils.cloneDeep(e.itemStageList) : null //单独处理阶梯报价
        //处理直送地
        e.itemExtMap.deliveryPlace =
          e.deliveryPlaceList && e.deliveryPlaceList?.length
            ? utils.cloneDeep(e.deliveryPlaceList)
            : null
        e.stepNum = e.stepValue
        // 处理阶梯类型
        e.stepQuoteType === -1 && (e.stepQuoteType = null)
        // 接口没有返回税率值, 需要前端匹配
        if (e.taxRateCode && !e.taxRateValue) {
          const taxRow = this.taxRateNameList.find((tax) => tax.taxItemCode === e.taxRateCode)
          taxRow && (e.taxRateValue = taxRow.taxRate)
        }
        // 判断金额型和比例型
        if (e.itemExtMap && e.itemExtMap.minQuoteRangeType == 1) {
          e.itemExtMap.minQuoteRange = new Decimal(e.itemExtMap.minQuoteRange)
            .mul(new Decimal(100))
            .toNumber()
        }

        // 销售凭证空值转换
        if (e.itemExtMap && !e.itemExtMap.salesVouchers) {
          e.itemExtMap.salesVouchers = 'empty'
        }

        if (e.id && e.costModelQuote && e.costModelId) {
          e.costEstimation = this.$t('成本测算')
        }

        if (Array.isArray(e?.childItems)) {
          e.childItems = this.serializeGridList(e.childItems)
          // 覆盖groupId
          e.childItems.forEach((item) => {
            item.groupId = e.gropuId
          })
        }
        _list.push(e)
      })
      return _list
    },
    //列表参数重新赋值
    resetAsyncConfigParams(type, rules) {
      if (
        !['resetDataByLocal', 'mounted', 'refreshDataByLocal', 'filterDataByLocal'].includes(type)
      ) {
        return
      }
      let params = {
        page: { current: 1, size: 1000 },
        defaultRules: [
          {
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            type: 'string',
            value: this.$route.query.rfxId || ''
          }
        ]
      }
      if (rules)
        params = Object.assign({}, params, {
          condition: 'and',
          rules: rules
        })
      this.$API.rfxRequireDetail.getRFXItem(params).then((res) => {
        if (res?.data?.records) {
          let _records = utils.cloneDeep(res.data.records)
          _records = this.setCostEstimation(_records)
          this.$set(this.pageConfig[0].grid, 'dataSource', _records)
          this.submitTableData = this.serializeGridList(_records)
          this.oldList = utils.cloneDeep(_records)
        } else {
          this.$set(this.pageConfig[0].grid, 'dataSource', [])
          this.submitTableData = []
          this.oldList = []
        }
      })
    },
    // 设置成本测算值
    setCostEstimation(list) {
      list.forEach((item) => {
        const { id, costModelQuote, costModelId } = item
        if (id && costModelQuote && costModelId) {
          item.costEstimation = this.$t('成本测算')
        }
      })
      return list
    },

    defineGridEditStatus() {
      if (
        this.detailInfo.transferStatus < 1 &&
        // 招投标、竞价: OA审批状态为审核中、审核通过 禁止编辑
        !(
          ['invite_bids', 'bidding_price'].includes(this.$route.query.source) &&
          [1, 2].includes(Number(this.detailInfo.approveStatus))
        )
      ) {
        this.$set(this.pageConfig[0].grid, 'allowEditing', true)
        this.$set(this.pageConfig[0].grid, 'editSettings', editSettings)

        if (Array.isArray(this.childFields) && this.childFields.length) {
          // 如果存在子层结构  隐藏导入操作
          this.$set(this.pageConfig[0], 'toolbar', combination.toolbar(this, toolbarNoImport))
        } else {
          this.$set(
            this.pageConfig[0],
            'toolbar',
            this.sourceType === 'rfq'
              ? toolbar(this.detailInfo.sourcingObjType == 'cost_factor')
              : this.sourceType === 'invite_bids' || this.sourceType === 'bidding_price'
              ? toolbarNoBatch()
              : toolbarNoImport
          )
        }
      } else {
        this.$set(this.pageConfig[0].grid, 'allowEditing', false)
        this.$set(this.pageConfig[0].grid, 'editSettings', notAllowedEditSettings)
        this.$set(this.pageConfig[0], 'toolbar', noEditToolbar)
      }
      if (this.calculation) {
        this.$set(this.pageConfig[0].grid.editSettings, 'allowEditing', false)
        this.$set(this.pageConfig[0], 'toolbar', [])
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      if (_current?.grid) {
        _current.grid.endEdit()
        _current.grid.hideSpinner()
        this.handleChildEndEdit()
      }
    },
    handleParentEndEdit(addId = null) {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      if (_current?.grid) _current.grid.endEdit()
      this.handleChildEndEdit(addId)
    },
    handleChildEndEdit(addId = null) {
      this.submitTableData?.forEach((data) => {
        if (data.addId !== addId) {
          this.$bus.$emit(`subPurchaseGridEndEdit-${data.addId}`)
        }
      })
    },
    setNewRecordsTag(tag = 'parent', data) {
      this.newRecordsTag = {
        tag,
        data
      }
    },
    resetNewRecordsTag() {
      this.newRecordsTag = {
        tag: null,
        data: null
      }
    },
    // 表格toolbar点击事件监听
    async handleClickToolBar(e, tag = 'parent', _data, vm) {
      if (e.toolbar.id == 'resetDataByLocal') {
        this.resetAsyncConfigParams('resetDataByLocal')
      }
      if (e.toolbar.id == 'refreshDataByLocal') {
        this.ids = []
        this.resetAsyncConfigParams('refreshDataByLocal')
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
      }
      if (e.toolbar.id == 'filterDataByLocal') {
        this.resetAsyncConfigParams('filterDataByLocal', e.rules.rules)
      }
      if (e.toolbar.id == 'Add') {
        this.endEdit()
        if (this.detailInfo.sourcingObjType === 'cost_factor') {
          // 成本因子采用弹框新增方式
          this.costFactoryDialogShow(e)
          return
        }
        this.localTempRecord = {}
        this.setNewRecordsTag(tag, _data)
        e.grid.addRecord({})
        setTimeout(() => {
          e.grid.selectRow(0)
          e.grid.startEdit()
        }, 800)
        return
      }
      if (e.toolbar.id == 'download') {
        this.downloadFiles()
      } else if (e.toolbar.id == 'save') {
        this.endEdit()
        this.$bus.$emit(`subEndEdit-${this.currentEditGroupId}`)
        setTimeout(() => {
          this.handleSaveDetails()
        }, 200)
        return
      } else if (e.toolbar.id == 'upload') {
        if (this.detailInfo.sourcingMode == 'rfq') {
          this.handleUpload()
        } else {
          this.handleUploadTwo()
        }
      } else if (e.toolbar.id == 'batchAdd') {
        if (this.detailInfo.siteCode) {
          sessionStorage.setItem(
            'siteParam',
            JSON.stringify({
              organizationId: this.detailInfo.siteId,
              siteCode: this.detailInfo.siteCode
            })
          )
          this.$dialog({
            modal: () =>
              import('@/components/NormalEdit/checkSelectItemCode/components/selectGrid'),
            data: {
              title: this.$t('选择物料'),
              rfxId: this.$route.query.rfxId,
              siteParam: JSON.parse(sessionStorage.getItem('siteParam')),
              sourcingType: this.detailInfo.sourcingType,
              multiple: true,
              source: this.$route?.query?.source,
              sourcingObjType: this.detailInfo.sourcingObjType,
              priceClassification: this.detailInfo.priceClassification
            },
            success: (data) => {
              const grid = this.$refs.templateRef.getCurrentTabRef().grid
              let dataSource = grid.$options.propsData.dataSource
              let existItemCodeArr = []
              for (let item of dataSource) {
                if (item.itemCode) {
                  existItemCodeArr.push(item.itemCode)
                }
              }
              for (let item of data) {
                if (existItemCodeArr.indexOf(item.itemCode) != -1) {
                  this.$toast({
                    content: this.$t('物料"' + item.itemName + '"在当前单据已存在,不能重复添加'),
                    type: 'warning'
                  })
                  continue
                }
                const row = {}
                if (this.detailInfo.sourcingObjType) {
                  Object.assign(row, {
                    itemLogisticsResponse: {
                      transportType: this.detailInfo.sourcingObjType
                    }
                  })
                }
                this.isBatch = true
                let tempRecord = cloneDeep(this.detailInfo)
                const isUsePriceUnit = this.hasColumn('priceUnitName')
                tempRecord.id = undefined
                Object.assign(row, tempRecord)
                row.addId = uuidv1()
                row.itemId = item.id
                row.itemCode = item.itemCode
                row.itemName = item.itemName
                row.spec = item.itemDescription
                row.material = item.materialTypeName
                row.unitId = item.baseMeasureUnitId
                row.unitCode = item.baseMeasureUnitCode
                row.unitName = item.baseMeasureUnitName
                row.categoryId = item.categoryId
                row.categoryCode = item.categoryCode
                row.categoryName = item.categoryName
                row.priceUnitName = item.purchaseUnitName
                row.purUnitId = item.purUnitId
                row.purUnitCode = item.purUnitCode
                row.purUnitName = item.purUnitName
                row.itemExtMap = {
                  //1503公司直送地赋值惠州
                  deliveryPlace:
                    this.detailInfo.sourcingObjType === 'common' &&
                    this.detailInfo.companyCode === '1503'
                      ? [this.$t('惠州')]
                      : []
                }
                row.priceUnitName = this.txCompanyList.includes(this.detailInfo.companyCode)
                  ? '1'
                  : this.detailInfo.rfxGeneralType === 1 // 如果是通采则设定默认值；
                  ? this.ktFlag == 1 ||
                    ['single_module', 'COMMON_OVERSEA_CKD'].includes(
                      this.detailInfo.sourcingObjType
                    ) ||
                    this.detailInfo.companyCode === '0602' //kt 、模具、海外通用询报价默认单位为1、越南公司
                    ? '1'
                    : '1000'
                  : isUsePriceUnit // 非采分为配置了价格单位跟没有配置价格单位两种情况
                  ? ''
                  : '1'
                this.localTempRecord = row
                this.submitTableData.push(row)
                e.grid.addRecord(row)
              }
              return
            }
          })
        } else {
          this.$toast({
            content: this.$t('单据未配置工厂'),
            type: 'warning'
          })
        }
        return
      }
      if (e.toolbar.id == 'delete') {
        if (tag === 'parent') {
          this.handleDeleteParentRows(e)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.clearSelection()
        } else {
          this.handleDeleteChildRows(e, tag, _data, vm)
        }
      }
    },
    // 成本因子弹框
    costFactoryDialogShow(e) {
      this.$dialog({
        modal: () => import('@/views/common/components/dialog/itemCodeDialog.vue'),
        data: {
          title: this.$t('成本因子'),
          valueSet: 'multi_cost_factor',
          companyCode: this.detailInfo.companyCode
        },
        success: async (data) => {
          const grid = this.$refs.templateRef.getCurrentTabRef().grid
          let dataSource = grid.$options.propsData.dataSource
          let existItemCodeArr = []
          for (let item of dataSource) {
            if (item.itemCode) {
              existItemCodeArr.push(item.itemCode)
            }
          }
          for (let item of data) {
            if (existItemCodeArr.indexOf(item.itemCode) != -1) {
              this.$toast({
                content: this.$t('成本因子"' + item.itemName + '"在当前单据已存在,不能重复添加'),
                type: 'warning'
              })
              continue
            }
            const row = {}
            if (this.detailInfo.sourcingObjType) {
              Object.assign(row, {
                itemLogisticsResponse: {
                  transportType: this.detailInfo.sourcingObjType
                }
              })
            }
            this.isBatch = true
            let tempRecord = cloneDeep(this.detailInfo)
            tempRecord.id = undefined
            Object.assign(row, tempRecord)
            row.addId = uuidv1()
            row.itemId = item.id // 成本因子id
            row.itemCode = item.itemCode // 成本因子code
            row.itemName = item.itemName // 成本因子名称
            // row.costFactorProperty = item.costFactorProperty //属性大类
            // row.costFactorGroup = item.costFactorGroup // 属性中类
            row.spec = item.costFactorSpecification //规格
            row.brand = item.costFactorBrand //品牌
            row.unitId = item.baseMeasureUnitId //单位id
            row.unitCode = item.baseMeasureUnitCode //单位code
            row.unitName = item.baseMeasureUnitName //单位名称
            row.priceUnitName = '1' //成本因子默认1 不可修改
            row.itemExtMap = {
              minPricePercent: item.minQuotePercent, // 价格下限 %
              maxPricePercent: item.maxQuotePercent // 价格上限 %
            }
            this.localTempRecord = row
            this.submitTableData.push(row)
            e.grid.addRecord(row)
          }
          return
        }
      })
    },
    handleDeleteParentRows(e) {
      let _selectGridRecords = []
      e.gridRef.dataSource.forEach((item) => {
        if (item.customChecked) {
          _selectGridRecords.push(item)
        }
      })
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      _selectGridRecords.map((item) => {
        if (item.id) {
          this.ids.push(item.id)
        }
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？')
        },
        success: () => {
          let ids = _selectGridRecords.map((item) => (item.addId ? item.addId : item.id))
          let dataSource = this.$refs.templateRef.getCurrentTabRef().grid.dataSource // 获取编辑的数据
          let newList = dataSource.filter((item) => {
            let id = item.addId ? item.addId : item.id
            return !ids.includes(id)
          })
          this.$set(this.pageConfig[0].grid, 'dataSource', newList)
          this.submitTableData = newList
          // 删除后保存
          setTimeout(() => {
            this.handleSaveDetails()
          }, 200)
        }
      })
      // this.$refs.templateRef.refreshCurrentGridData()
      // let _ids = []
      // _selectGridRecords.map((item) => {
      //   if (item.id) {
      //     _ids.push(item.id)
      //   }
      //   _ids = _ids.concat(
      //     this.submitTableData
      //       .filter(
      //         (e) =>
      //           e.parentRfxItemKey &&
      //           item.rfxItemKey &&
      //           e.parentRfxItemKey === item.rfxItemKey &&
      //           e.id
      //       )
      //       .map((e) => e.id)
      //   )
      // })
      // this.$dialog({
      //   data: {
      //     title: this.$t('删除'),
      //     message: this.$t('是否确认删除？'),
      //     confirm: () => {
      //       return new Promise((resolve) => {
      //         if (_ids.length) {
      //           resolve(
      //             this.$API.rfxRequireDetail.deleteRFXItem({
      //               idList: _ids,
      //               rfxId: this.$route.query.rfxId
      //             })
      //           )
      //         } else {
      //           resolve({ message: null })
      //         }
      //         e?.grid.deleteRecord()
      //         // 清除 submitTableData
      //         this.$refs.templateRef.refreshCurrentGridData()
      //       })
      //     }
      //   }
      // })
    },
    handleDeleteChildRows(e, tag, data_, vm) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      if (_selectGridRecords.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      _selectGridRecords.map((item) => {
        if (item.id) {
          this.ids.push(item.id)
        }
      })
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？')
        },
        success: () => {
          let ids = _selectGridRecords.map((item) => (item.addId ? item.addId : item.id))
          let dataSource = e.gridRef.ejsRef.getCurrentViewRecords()
          let newList = dataSource.filter((item) => {
            let id = item.addId ? item.addId : item.id
            return !ids.includes(id)
          })
          this.$set(vm.childTable[0].grid, 'dataSource', newList)
          this.submitTableData = newList
        }
      })
      // let _ids = []
      // _selectGridRecords.map((item) => {
      //   if (item.id) {
      //     _ids.push(item.id)
      //   }
      // })
      // this.$dialog({
      //   data: {
      //     title: this.$t('删除'),
      //     message: this.$t('是否确认删除？'),
      //     confirm: () => {
      //       return new Promise((resolve) => {
      //         if (_ids.length) {
      //           resolve(
      //             this.$API.rfxRequireDetail.deleteRFXItem({
      //               idList: _ids,
      //               rfxId: this.$route.query.rfxId
      //             })
      //           )
      //         } else {
      //           resolve({ message: null })
      //         }
      //         e?.grid.deleteRecord()
      //       })
      //     }
      //   }
      // })
    },
    downloadFiles() {
      let id = this.$route.query?.id || this.detailInfo.id
      let params = {
        page: {
          current: 1,
          size: 1000
        },
        defaultRules: [
          {
            field: 'rfx_item.rfxHeaderId',
            operator: 'equal',
            type: 'string',
            value: id
          }
        ]
      }
      this.$API.rfxRequireDetail.exportBuilder(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 重置表格勾选
    clearRowSelection() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      if (_current?.grid) _current.grid.clearRowSelection()
    },
    hasColumn(field) {
      return this.pageConfig[0].grid.columnData.find((e) => e.field === field)
    },
    childHasColumn(field) {
      return this.childFields.find((e) => e.fieldCode === field)
    },
    // 内容是否修改
    isContentChange() {
      let oldData = this.needToDiff(this.oldList)
      let newData = this.needToDiff(this.submitTableData)
      return !judgeDataIsSame(oldData, newData)
    },
    // 处理需要对比的数据
    needToDiff(data) {
      // 只对比unitCode字段
      const specialFields = ['unitId', 'unitName']
      const numFields = ['lineNo']
      let list = data?.map((item) => {
        let _obj = {}
        for (let i in item) {
          if (!specialFields.includes(i)) {
            _obj[i] = numFields.includes(i)
              ? item[i] || item[i] == '0'
                ? Number(item[i])
                : 0
              : item[i]
          }
        }
        return _obj
      })
      return list
    },
    // 明细保存
    async handleSaveDetails() {
      if (combination.handleSaveDetailsBefore(this)) {
        return
      }
      let _submitTableData = cloneDeep(this.submitTableData)
      // 展平数据
      if (this.detailInfo.sourcingObjType === 'HIERARCHY') {
        //CKD只有一行母料，不作该操作
        _submitTableData = this.flattenSubmitData(_submitTableData)
      }
      // CKD询报价，将主表数据放入子表一起返回
      if (this.detailInfo.sourcingObjType === 'combination') {
        // ckd场景暂不改动
        const dataSource = this.$refs.templateRef.getCurrentTabRef().grid.getCurrentViewRecords()
        dataSource.forEach((item) => {
          if (_submitTableData.findIndex((d) => d.addId === item.addId) === -1) {
            _submitTableData.push(item)
          }
        })
      }
      // 需求明细 必填校验
      _submitTableData.forEach((row, index) => {
        // 子集不作校验
        if (row.parentRfxItemKey) return
        // 校验------------------------
        // 校验1. 必填--提交才校验必填
        this.requiredCols.forEach((rc) => {
          if (rc.field.indexOf('.') > -1) {
            let _sp = rc.field.split('.')
            if (
              !row[_sp[0]] ||
              (!row[_sp[0]][_sp[1]] && row[_sp[0]][_sp[1]] !== 0) ||
              (row[_sp[0]][_sp[1]] && row[_sp[0]][_sp[1]]?.length <= 0)
            ) {
              this.$toast({
                content: this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`),
                type: 'error'
              })
              throw new Error(this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`))
            }
          } else {
            if (!row[rc.field] && row[rc.field] !== 0) {
              this.$toast({
                content: this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`),
                type: 'error'
              })
              throw new Error(this.$t(`请填写第${Number(index + 1)}行的${rc.headerText}`))
            }
          }
        })
      })
      for (let i = 0; i < _submitTableData.length; i++) {
        const row = _submitTableData[i]
        //价格下限
        const minQuotePercent = getValueByPath(row, 'itemExtMap.minQuotePercent')
        //价格上限
        const maxQuotePercent = getValueByPath(row, 'itemExtMap.maxQuotePercent')
        // 起价
        const startingPrice = getValueByPath(row, 'itemExtMap.startingPrice')
        // 临时模具编码
        const dieTempCode = getValueByPath(row, 'itemDieResponse.dieTempCode')
        // 正式模编码
        const dieFormalCode = getValueByPath(row, 'itemDieResponse.dieFormalCode')

        const isNullOrUndefined = (value) => typeof value === 'undefined' || value === null
        const itemDieFlag =
          (row.parentRfxItemKey &&
            this.childHasColumn('dieTempCode') &&
            this.childHasColumn('dieFormalCode')) ||
          (!row.parentRfxItemKey &&
            this.hasColumn('itemDieResponse.dieTempCode') &&
            this.hasColumn('itemDieResponse.dieFormalCode'))
        if (itemDieFlag && !dieTempCode && !dieFormalCode) {
          this.$toast({
            content: this.$t(`第${Number(i + 1)}行临时模具编码和正式模具编码不能同时为空`),
            type: 'error'
          })
          throw new Error(this.$t(`第${Number(i + 1)}行临时模具编码和正式模具编码不能同时为空`))
        }

        if (
          this.hasColumn('itemExtMap.minQuotePercent') &&
          !isNullOrUndefined(minQuotePercent) &&
          minQuotePercent > 0 &&
          this.hasColumn('itemExtMap.maxQuotePercent') &&
          !isNullOrUndefined(maxQuotePercent) &&
          maxQuotePercent > 0 &&
          minQuotePercent > maxQuotePercent
        ) {
          //同时设置了'价格上下限字段'，校验不符合的场景
          //上限、下限  都>0，就要比较大小
          this.$toast({
            content: this.$t(`第${Number(i + 1)}行价格上限和价格下限输入有误`),
            type: 'error'
          })
          throw new Error(this.$t(`第${Number(i + 1)}行价格上限和价格下限输入有误`))
        }

        if (
          this.hasColumn('itemExtMap.startingPrice') &&
          !isNullOrUndefined(startingPrice) &&
          startingPrice > 0
        ) {
          //同时设置了'起价'，校验不符合的场景
          if (
            this.hasColumn('itemExtMap.minQuotePercent') &&
            !isNullOrUndefined(minQuotePercent) &&
            minQuotePercent > 0 &&
            this.hasColumn('itemExtMap.maxQuotePercent') &&
            !isNullOrUndefined(maxQuotePercent) &&
            maxQuotePercent > 0 &&
            (startingPrice <= minQuotePercent || startingPrice > maxQuotePercent)
          ) {
            // 起价、上限、下限  都>0，就要比较大小
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行，'起价'需要介于价格上限、价格上限之间。`),
              type: 'error'
            })
            throw new Error(this.$t(`第${Number(i + 1)}行，'起价'需要介于价格上限、价格上限之间。`))
          } else if (
            this.hasColumn('itemExtMap.minQuotePercent') &&
            !isNullOrUndefined(minQuotePercent) &&
            minQuotePercent > 0 &&
            startingPrice <= minQuotePercent
          ) {
            // 起价、下限  都>0，就要比较大小
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行，'起价'需要高于'价格下限'`),
              type: 'error'
            })
            throw new Error(this.$t(`第${Number(i + 1)}行，'起价'需要高于'价格下限'`))
          } else if (
            this.hasColumn('itemExtMap.maxQuotePercent') &&
            !isNullOrUndefined(maxQuotePercent) &&
            maxQuotePercent > 0 &&
            startingPrice > maxQuotePercent
          ) {
            // 起价、上限  都>0，就要比较大小
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行，'起价'需要低于'价格上限'`),
              type: 'error'
            })
            throw new Error(this.$t(`第${Number(i + 1)}行，'起价'需要低于'价格上限'`))
          }
        }
      }

      return await this.handleSaveSingleTabData(cloneDeep(_submitTableData))
    },
    serializeSaveParams(_fieldDefines, _data) {
      // let _fieldList = [];
      // let _fieldDefines = this.pageConfig[0]["fieldDefines"];
      let _extSaveRequest = {} //扩展表字段存储  rfx_item_ext
      let _dieSaveRequest = {} //模具信息  字段存储  rfx_item_die
      let _logisticsSaveRequest = {} //物流信息 字段存储 rfx_item_logistics

      // 参考供应商的信息需要携带
      if (getValueByPath(_data, 'itemExtMap.referItemSupplierId')) {
        _extSaveRequest.referItemSupplierId = getValueByPath(
          _data,
          'itemExtMap.referItemSupplierId'
        )
        if (!_data.supplierId) {
          _data.supplierId = getValueByPath(_data, 'itemExtMap.referItemSupplierId')
          _data.supplierCode = getValueByPath(_data, 'itemExtMap.referItemSupplierCode')
          _data.supplierName = getValueByPath(_data, 'itemExtMap.referItemSupplierName')
        }
      }

      for (let i in _fieldDefines) {
        let _temp = _fieldDefines[i]
        // 之前的扩展字段逻辑
        // if (_fieldDefines[i]["tableField"] < 1) {
        //   _temp["fieldData"] = _data[_fieldDefines[i]["fieldCode"]];
        //   _temp.recordId = _data.id ? _data.id : null;
        //   _fieldList.push(_temp);
        //   delete _data[_fieldDefines[i]["fieldCode"]];
        // }
        if (dateEditFields.indexOf(_temp.fieldCode) > -1) {
          //日期类型

          if (_data[_temp['fieldCode']]) {
            _data[_temp['fieldCode']] = this.$utils.formatTime(_data[_temp['fieldCode']])
            console.log('日期类型序列化', _data[_temp['fieldCode']])
          }
        }
        if (_temp['tableName'] === 'rfx_item_ext') {
          if (_data['itemExtMap'] != undefined) {
            if (
              _temp['fieldCode'] === 'salesVouchers' &&
              _data['itemExtMap'][_temp['fieldCode']] === 'empty'
            ) {
              _extSaveRequest[_temp['fieldCode']] = '' // 销售凭证空值转换
            } else {
              _extSaveRequest[_temp['fieldCode']] = _data['itemExtMap'][_temp['fieldCode']]
            }
            delete _data['itemExtMap'][_temp['fieldCode']]
          }
        } else if (_temp['tableName'] === 'rfx_item_logistics') {
          if (_data['itemLogisticsResponse'] != undefined) {
            _logisticsSaveRequest[_temp['fieldCode']] =
              _data['itemLogisticsResponse'][_temp['fieldCode']]
            delete _data['itemLogisticsResponse'][_temp['fieldCode']]
          }
        } else if (_temp['tableName'] === 'rfx_item_die') {
          if (_data['itemDieResponse'] != undefined) {
            _dieSaveRequest[_temp['fieldCode']] = _data['itemDieResponse'][_temp['fieldCode']] ?? ''
            delete _data['itemDieResponse'][_temp['fieldCode']]
          }
        }
      }
      delete _data['itemExtMap']
      delete _data['itemLogisticsResponse']
      delete _data['itemDieResponse']
      _data['extSaveRequest'] = _extSaveRequest
      _data['dieSaveRequest'] = _dieSaveRequest
      _data['logisticsSaveRequest'] = _logisticsSaveRequest
    },
    async handleSaveSingleTabData(list) {
      if (Array.isArray(list) && list.length) {
        list.forEach((_data) => {
          delete _data.addId
          delete _data.childItems
          if (_data?.parentRfxItemKey) {
            //存在parentRfxItemKey，使用子层结构 做数据保存
            this.serializeSaveParams(utils.cloneDeep(this.childFields), _data)
          } else {
            this.serializeSaveParams(utils.cloneDeep(this.fieldDefines), _data)
          }
          // _data["fieldDataList"] = _fieldList;
          //附件字段  单独处理
          // _data["fileList"] = _data["drawing"]
          //   ? JSON.parse(_data.drawing)
          //   : null;
          // _data["drawing"];
          // 修复文件类型,从string改成对象
          if (_data['drawing'] && typeof _data['drawing'] == 'string') {
            _data['drawing'] = JSON.parse(_data['drawing'])
            if (_data['drawing'].length) {
              _data['drawing'].forEach((f) => {
                if (!f?.sysFileId) {
                  f.sysFileId = f.id
                  delete f.id
                }
              })
            }
          } else if (!_data['drawing']) {
            _data['drawing'] = []
          }
          _data['fileList'] = _data['drawing']
          _data['drawing'] = ''

          if (_data['stepQuoteName'] && typeof _data['stepQuoteName'] == 'string') {
            _data['stepQuoteName'] = JSON.parse(_data['stepQuoteName'])
          } else if (!_data['stepQuoteName']) {
            _data['stepQuoteName'] = []
          }
          _data['itemStageList'] = _data['stepQuoteName']
          _data['itemStageList'].forEach((x) => {
            x.stepType = 2
          })
          _data['stepQuoteName'] = ''
          // 直送地转换
          let _deliveryPlace = _data?.extSaveRequest?.deliveryPlace
          _data['deliveryPlaceList'] =
            _deliveryPlace && !Array.isArray(_deliveryPlace)
              ? _deliveryPlace.split(',')
              : _deliveryPlace

          _data.extSaveRequest.deliveryPlace = '' // 置空直送地（后端解析单个直送地报错）

          _data['rfxHeaderId'] = this.detailInfo.id
          _data['rfxHeaderCode'] = this.detailInfo.rfxCode
          _data['rfxHeaderName'] = this.detailInfo.rfxName
          _data['businessTypeId'] = this.detailInfo.businessTypeId
          _data['businessTypeCode'] = this.detailInfo.businessTypeCode
          _data['businessTypeName'] = this.detailInfo.businessTypeName

          // 选择参考物料信息附带相关信息
          const referInfoList = _data.extStageSaveRequests || []
          const extStageSaveRequests = []
          referInfoList.forEach((item) => {
            extStageSaveRequests.push({
              ..._data.extSaveRequest,
              ...item
            })
          })
          _data['extStageSaveRequests'] = extStageSaveRequests
        })
      }
      for (let item of list) {
        if (item.extSaveRequest.requireDate != undefined) {
          item.extSaveRequest.requireDate = Date.parse(new Date(item.extSaveRequest.requireDate))
        }
        if (item.extSaveRequest.requireEndDate != undefined) {
          item.extSaveRequest.requireEndDate = Date.parse(
            new Date(item.extSaveRequest.requireEndDate)
          )
        }
      }
      let flag = false
      list.forEach((x, i) => {
        if (
          x.extSaveRequest.adviseMinPurQuantity != undefined &&
          x.extSaveRequest.adviseMinPurQuantity != null &&
          x.extSaveRequest.adviseMinPurQuantity != '' &&
          x.extSaveRequest.adviseMinPackageQuantity != undefined &&
          x.extSaveRequest.adviseMinPackageQuantity != null &&
          x.extSaveRequest.adviseMinPackageQuantity != ''
        ) {
          let adviseMinPurQuantity = x.extSaveRequest.adviseMinPurQuantity
          let adviseMinPackageQuantity = x.extSaveRequest.adviseMinPackageQuantity
          let number = adviseMinPurQuantity / adviseMinPackageQuantity
          if (
            Math.floor(number) !== number &&
            !this.txCompanyList.includes(this.$route.query?.companyCode)
          ) {
            this.$toast({
              content: this.$t(`第${i + 1}行最小采购量需是最小包装数量的整数倍`),
              type: 'warning'
            })
            flag = true
          }
        }
        if (x.extSaveRequest && x.extSaveRequest.minQuoteRangeType == 1) {
          x.extSaveRequest.minQuoteRange = new Decimal(x.extSaveRequest.minQuoteRange)
            .div(new Decimal(100))
            .toNumber()
        } else if (x.extSaveRequest && x.extSaveRequest.minQuoteRangeType == 0) {
          if (
            x.extSaveRequest.maxQuotePercent > 0 &&
            x.extSaveRequest.minQuotePercent > 0 &&
            x.extSaveRequest.minQuoteRange >
              x.extSaveRequest.maxQuotePercent - x.extSaveRequest.minQuotePercent
          ) {
            // 如果价格上限、下限，都有值，且>0，校验'最小竞价幅度'
            this.$toast({
              content: this.$t(`第${Number(i + 1)}行最小竞价幅度应小于价格上限减去价格下限`),
              type: 'error'
            })
            flag = true
          }
        }
        if (x.extSaveRequest.minQuotePercent === null) x.extSaveRequest.minQuotePercent = ''
        if (x.extSaveRequest.maxQuotePercent === null) x.extSaveRequest.maxQuotePercent = ''
        if (x.extSaveRequest.minQuoteRange === null) x.extSaveRequest.minQuoteRange = ''
        if (x.extSaveRequest.startingPrice === null) x.extSaveRequest.startingPrice = ''
      })
      if (flag) return

      let _params = {
        deleteIdList: { idList: this.ids },
        rfxId: this.$route.query.rfxId,
        rfxItems: list
      }

      let res = await this.$API.rfxRequireDetail.saveRFXItem(_params).catch(() => {
        this.ids = []
      })
      if (res.code === 200) {
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
        this.$refs.templateRef.refreshCurrentGridData()
        this.ids = []
      }
      return res
    },
    // 删除操作
    handleDelete(ids) {
      this.$API.rfxRequireDetail.deleteRFXItem({
        idList: ids,
        rfxId: this.$route.query.rfxId
      })
    },
    // 上传（显示弹窗）
    handleUpload() {
      this.requestUrls = {
        templateUrlPre: 'rfxRequireDetail',
        templateUrl: 'exportRfxItem',
        uploadUrl: 'importRfxItem',
        rfxId: this.$route.query.rfxId
      }
      this.showUploadExcel(true)
    },

    handleUploadTwo() {
      this.requestUrls = {
        templateUrlPre: 'rfxRequireDetail',
        templateUrl: 'exportBiddingAndTenderRfxItem',
        uploadUrl: 'importBiddingAndTenderRfxItem',
        rfxId: this.$route.query.rfxId
      }
      this.showUploadExcel(true)
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>
<style lang="scss">
.hierarchy-template .grid-container .mt-data-grid .e-grid .e-content {
  min-height: 60px !important;
}
</style>
<style lang="scss" scoped>
.full-height {
  /deep/.e-spinner-pane {
    display: none;
  }
  /deep/.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd) {
    vertical-align: middle;
  }
  /deep/ .e-table {
    border-left: 1px solid #e0e0e0;
    .e-emptyrow {
      td {
        border: 1px solid #e0e0e0;
        border-top: none;
      }
    }
  }
  /deep/ .e-detailindentcell {
    border-right: none;
  }
  /deep/ .e-detailcell {
    .toolbar-container {
      height: 30px;
    }
  }
}
// /deep/ .frozenColumns {
//   .template-wrap {
//     .e-grid .e-table {
//       & thead th:first-child {
//         position: sticky;
//         left: 0px;
//         z-index: 1;
//       }
//       & thead th:nth-child(2) {
//         position: sticky;
//         left: 50px;
//         z-index: 1;
//       }
//       & thead th:nth-child(3) {
//         position: sticky;
//         left: 50px;
//         z-index: 1;
//       }
//       & thead th:nth-child(4) {
//         position: sticky;
//         left: 200px;
//         z-index: 1;
//       }
//       & thead th:nth-child(5) {
//         position: sticky;
//         left: 350px;
//         z-index: 1;
//       }
//       & tbody td:first-child {
//         position: sticky;
//         left: 0px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background: #fff !important;
//       }
//       & tbody td:nth-child(2) {
//         position: sticky;
//         left: 50px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background: #fff !important;
//       }
//       & tbody td:nth-child(3) {
//         position: sticky;
//         left: 50px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background: #fff !important;
//       }
//       & tbody td:nth-child(4) {
//         position: sticky;
//         left: 200px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background: #fff !important;
//       }
//       & tbody td:nth-child(5) {
//         position: sticky;
//         left: 350px;
//         z-index: 1;
//         border-right: 1px solid var(--plugin-dg-shadow-color);
//         background: #fff !important;
//       }
//     }
//   }
// }
// /deep/ .mt-data-grid {
//   .e-gridcontent {
//     .e-table {
//       tbody {
//         .e-editedrow {
//           .e-editcell {
//             overflow: visible !important;
//             form {
//               .e-table {
//                 tbody {
//                   tr:nth-child(2n-1) {
//                     background: #f6f7fb;
//                     td:first-child {
//                       position: sticky;
//                       left: 0px;
//                       z-index: 1;
//                       border-right: 1px solid var(--plugin-dg-shadow-color);
//                       background-color: #f6f7fb;
//                     }
//                     td:nth-child(2) {
//                       position: sticky;
//                       left: 50px;
//                       z-index: 1;
//                       border-right: 1px solid var(--plugin-dg-shadow-color);
//                       background-color: #f6f7fb;
//                     }
//                     td:nth-child(3) {
//                       position: sticky;
//                       z-index: 1;
//                       border-right: 1px solid var(--plugin-dg-shadow-color);
//                       background-color: #f6f7fb;
//                     }
//                     td:nth-child(4) {
//                       position: sticky;
//                       left: 200px;
//                       z-index: 1;
//                       border-right: 1px solid var(--plugin-dg-shadow-color);
//                       background-color: #f6f7fb;
//                     }
//                   }
//                 }
//               }
//             }
//           }
//         }
//         tr:nth-child(2n-1) {
//           background: #f6f7fb;
//           td:first-child {
//             position: sticky;
//             left: 0px;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #f6f7fb;
//           }
//           td:nth-child(2) {
//             position: sticky;
//             left: 50px;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #f6f7fb;
//           }
//           td:nth-child(3) {
//             position: sticky;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #f6f7fb;
//           }
//           td:nth-child(4) {
//             position: sticky;
//             left: 200px;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #f6f7fb;
//           }
//         }
//         tr:nth-child(2n) {
//           background: #fff;
//           td:first-child {
//             position: sticky;
//             left: 0px;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #fff;
//           }
//           td:nth-child(2) {
//             position: sticky;
//             left: 50px;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #fff;
//           }
//           td:nth-child(3) {
//             position: sticky;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #fff;
//           }
//           td:nth-child(4) {
//             position: sticky;
//             left: 200px;
//             z-index: 1;
//             border-right: 1px solid var(--plugin-dg-shadow-color);
//             background-color: #fff;
//           }
//         }
//       }
//     }
//   }
// }
</style>
