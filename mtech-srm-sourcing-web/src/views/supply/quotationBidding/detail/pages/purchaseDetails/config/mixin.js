import CustomAgGrid from '@/components/CustomAgGrid'
import clickoutside from '@/directive/clickoutside'
import cellFile from '@/components/AgCellComponents/cellFile'
import cellLink from '@/components/AgCellComponents/cellLink'
import cellDialog from '@/components/AgCellComponents/cellDialog'
import { formatTime } from '@/utils/utils'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
import { columnData as rfxItem } from '@/views/common/columnData/ag/item'
import { columnData as biddingItem } from '@/views/common/columnData/ag/biddingItem'
import { columnData as itemExt } from '@/views/common/columnData/ag/itemExt'
import { columnData as itemLogistics } from '@/views/common/columnData/ag/itemLogistics'
import { columnData as biddingItemLogistics } from '@/views/common/columnData/ag/biddingItemLogistics'
import { columnData as itemDie } from '@/views/common/columnData/ag/itemDie'
import { columnData as itemLogisticsSea } from '@/views/common/columnData/ag/itemLogisticsSea'
import { columnData as itemLogisticsRailway } from '@/views/common/columnData/ag/itemLogisticsRailway'
import { columnData as itemLogisticsTrunk } from '@/views/common/columnData/ag/itemLogisticsTrunk'
import {
  tableNameMap,
  childGridReadonlyFields,
  notAllowEditFields,
  notSubmitFields,
  logisticsEditFields,
  logisticsNotAllowEditFields,
  logisticsNumberEditFields,
  logisticsIntEditFields,
  logisticsTableNameMap
} from '@/views/common/columnData/ag/supConstant'
import cloneDeep from 'lodash/cloneDeep'
import { v4 as uuidv4 } from 'uuid'
import { utils } from '@mtech-common/utils'
import { Decimal32 } from '../../../utils/utils'
import Decimal from 'decimal.js'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
import { stepNotMergeField } from '@/views/common/columnData/constant' //阶梯不合并字段
import * as combination from '@/views/supply/quotationBidding/detail/utils/combination'

import {
  inputCurrency,
  inputBidPurUnit,
  inputBidTaxRate,
  inputPrice,
  inputQuote,
  inputQuantity,
  inputEffectiveDate,
  judgePurAndQuantiry,
  judgeUnitExist,
  judgeUnitConsistent,
  inputShipMode,
  inputSupplyType
} from '@/views/supply/quotationBidding/detail/utils/ag/aggridInputHandlers'

export default {
  directives: { clickoutside: clickoutside },
  inject: ['reload'],
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    fieldDefines: {
      type: Array,
      default: () => {
        return []
      }
    },
    childFields: {
      type: Array,
      default: () => []
    },
    fieldStructures: {
      type: Array,
      default: () => []
    },
    //可编辑字段
    submitField: {
      type: Array,
      default: () => {
        return []
      }
    },
    parentRequired: {
      type: Array,
      default: () => {
        return []
      }
    },
    childRequired: {
      type: Array,
      default: () => {
        return []
      }
    },
    quotedPriceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 币种编码
    currencyList: {
      type: Array,
      default: () => []
    },
    // 税率
    taxList: {
      type: Array,
      default: () => []
    },
    // 采购单位
    purUnitList: {
      type: Array,
      default: () => []
    },
    rfxCountdown: {
      type: Object,
      default: () => ({})
    },
    newPrice: {
      type: Number,
      default: 0
    },
    strategyConfig: {
      type: Array,
      default: () => []
    },
    ktFlag: {
      type: Number,
      default: () => {}
    }
  },
  watch: {
    moduleType: {
      immediate: true,
      handler(v) {
        this.$set(this.downTemplateParams, 'tabType', v)
      }
    },
    $route(to, from) {
      if (from.name === 'offer-purchase-cost') {
        // 由成本分析页面返回，则刷新当前页面
        this.refresh()
      }
    }
  },
  components: {
    CustomAgGrid,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default,
    // eslint-disable-next-line
    cellFile, // 单元格文件查看
    // eslint-disable-next-line
    cellLink, // 单元格点击跳转
    // eslint-disable-next-line
    cellDialog
  },
  data() {
    return {
      agGrid: null,
      tabSource: [],
      moduleType: this.quotedPriceData.transferStatus != 32 ? 0 : 2,
      searchConfig: [],
      toolbar: [],
      tableData: [],
      columns: [],
      rowSelections: null,
      isStopEditingOnBlur: false,
      //维护数组
      actionObj: [],
      sourceInfo: [],
      oldList: [], //判断是否保存旧数据
      requestUrls: {},
      downTemplateName: this.$t('采购明细模板'),
      downTemplateParams: {
        pageFlag: false,
        rfxId: this.$route.query.rfxId,
        tabType: 0
      },
      lastTotal: 0,
      currentTotal: 0,
      parentRequiredList: [],
      childRequiredList: [],
      dictItems: {},
      annualLogisticsItemConfig: {},
      txCompanyList: ['2M01', '2S06']
    }
  },
  computed: {
    // 是否可以报价
    canQuote() {
      return [31, 32, 41].includes(Number(this.quotedPriceData.transferStatus)) // 报价中 议价中 商务投标中
    },
    abateFlag() {
      // 议价标识
      return this.moduleType == 2 ? true : false
    },
    bidTips() {
      if (!Array.isArray(this.strategyConfig) || !this.strategyConfig.length) {
        return ''
      }
      let _priceControl = this.strategyConfig[0]['priceControl']
      const STR1 = this.$t('说明：本次寻源不限制报价行数，请勾选您需要提交报价的行')
      const STR2 = this.$t('说明：本次是整单报价，您需要对所有进行报价')
      const STR3 = this.$t('说明：本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行')
      const STR4 = this.$t('说明：请勾选您需要提交报价的行')
      let _tip = ''
      if (this.moduleType === 0) {
        //当前报价
        switch (_priceControl) {
          case 'unlimited':
            _tip = STR1
            break
          case 'all':
            _tip = STR2
            break
          case 'first_all':
            //firstPrice  1 首次 0 非首次
            if (this.quotedPriceData.firstPrice == 1) {
              _tip = STR2
            } else {
              _tip = STR3
            }
            break
          default:
            _tip = ''
            break
        }
      } else if (this.moduleType === 2) {
        _tip = STR4
      } else {
        _tip = ''
      }
      return _tip
    },
    transferStatus() {
      return this.quotedPriceData?.transferStatus //流程状态
    },
    status() {
      return this.quotedPriceData?.status // xx
    },
    joinStatus() {
      return this.quotedPriceData?.joinStatus // 是否参与
    }
  },
  beforeDestroy() {
    this.$bus.$off('procurementRefreshPage')
  },
  beforeMount() {
    this.context = { componentParent: this }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.init()
    // 点击topInfo按钮后，更新表格
    this.$bus.$on(`procurementRefreshPage`, () => {
      this.reload() // 参与后直接刷页面
    })
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.getSupplierDictionary(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<< 事件操作 >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // tab - 切换页签
    handleSelectTab(idx) {
      this.moduleType = this.tabSource[idx].moduleType
      // 初始数据
      this.initSearchConfig()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // ag - 初始监听
    onGridReady(params) {
      this.agGrid = params
    },
    // ag - 获取rowId
    getRowId(params) {
      return params.data.addId
    },
    // ag - toolbar点击监听
    handleClickToolbar(args) {
      let _id = args.toolbar.id
      switch (_id) {
        case 'Save': // 保存
          this.handleSave()
          break
        case 'ReQuote': // 重新报价
          this.handleReQuote()
          break
        case 'Import': // 导入
          this.handleImport()
          break
        case 'Export': // 导出
          this.handleExport()
          break
        default:
          break
      }
    },
    // ag - 监听select选择框
    onRowSelected(e) {
      if (!e.data.isFirstLine) return
      let isSelected = e.node.selected
      let itemGroupId = e.data.itemGroupId
      //如果勾选的是阶梯第一行数据,同步其他行
      this.agGrid.api.forEachNode((node) => {
        if (node.data.itemGroupId === itemGroupId && !node.data.isFirstLine) {
          node.setSelected(isSelected)
        }
      })
    },
    // ag - 编辑框监听
    cellValueChanged(params) {
      if (params.oldValue === params.newValue) return
      inputCurrency(params) //币种联动
      inputBidPurUnit(params, this.agGrid, this.columns) //订单单位联动
      inputQuote(params) //报价方式、报价属性联动
      inputBidTaxRate(params, this.agGrid, this.columns) //税率联动 params:当前单元格参数，agGrid:aggrid对象，columns:列配置
      inputQuantity(params) //最小采购量、最小包装量联动
      inputEffectiveDate(params, this.quotedPriceData) //有效期联动监听(1503公司)
      inputPrice(params, this.agGrid, this.columns) //价格计算
      inputShipMode(params) //付款条件联动
      inputSupplyType(params) //屏供应方式联动
      this.getCurrentTotal(params) //本次总金额
    },
    // 编辑框监听 - 阶梯同步其他行数据
    syncOtherStepLine(params) {
      const field = params.column.colId
      const itemGroupId = params.data.itemGroupId
      const isFirstLine = params.data.isFirstLine
      if (!isFirstLine || stepNotMergeField.includes(field) || !itemGroupId) return
      // 查询同组阶梯数据
      this.agGrid.api.forEachNode((node) => {
        if (!node.data.isFirstLine && node.data.itemGroupId === itemGroupId) {
          node.setDataValue(field, params.value)
        }
      })
    },
    // ag - 点击事件监听
    clickoutside() {
      this.agGrid.api.stopEditing()
    },
    // btn - 保存
    async handleSave(isAutoSave) {
      this.agGrid.api.stopEditing()
      // 保存后待数据同步执行完后再执行(延时1s)
      await this.delay(500)
      // 全量数据保存
      let _dataSource = cloneDeep(this.tableData)
      this.saveComFn(_dataSource, 'save', isAutoSave)
    },
    // btn - 提交
    handleSubmit() {
      // 所有数据
      const _allDataSource = cloneDeep(this.tableData)
      // 选中的数据
      const _selctDataSource = this.agGrid.api.getSelectedRows()
      // 提交数据 议价、报价-无限制、报价-首次整单报价（首次）、勾选数据，其他情况下全量（此处采购明细需要保持在报价|议价页面）
      const _flag =
        this.moduleType === 2 ||
        (this.moduleType === 0 && this.strategyConfig[0]['priceControl'] === 'unlimited') ||
        (this.moduleType === 0 &&
          this.strategyConfig[0]['priceControl'] === 'first_all' &&
          this.quotedPriceData.firstPrice == 0)
      const _dataSource = _flag ? _selctDataSource : _allDataSource
      // 校验提交数据
      let _submitFlag = this.validSubmitData(_dataSource, _flag)
      if (!_submitFlag) return
      this.saveComFn(_dataSource, 'submit')
    },
    // 保存 - 保存、提交公共函数
    saveComFn(data, type, isAutoSave) {
      // 校验数据
      let _valid = this.validData(data)
      if (!_valid?.flag) return
      // 数据组合
      let _data = this.combineData(data)
      // 保存参数组合
      let params = {
        abateFlag: this.moduleType == 2 ? true : false, // 议价标识
        bidPriceItems: _data,
        rfxId: this.quotedPriceData.rfxId,
        submitStatus: type === 'submit' ? 1 : 0,
        tenantId: this.quotedPriceData.tenantId,
        newPrice: this.moduleType == 2 ? this.newPrice : null // 议价时提交
      }
      // if(this.quotedPriceData.sourcingScenarios === 'sea_transport_annual'){
      //   params.rfxAnnualLogisticsSeaDTO
      // }
      // 判断单位是否一致(不一致弹框提示)
      if (!_valid.isUnitConsistent) {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('采购单位和基本单位不一致，请确认转换率是否正确')
          },
          success: () => {
            // 提示确认后后保存提交
            this.saveData(params, isAutoSave)
          }
        })
        return
      }
      // 保存提交
      this.saveData(params, isAutoSave)
    },
    // 保存 - 提交数据校验
    validSubmitData(data, isNeedSelect) {
      let _priceControl = this.strategyConfig[0]['priceControl']
      //如果需要勾选数据
      if (isNeedSelect && (!data || !data?.length)) {
        let _msg =
          this.moduleType === 2
            ? this.$t('请勾选您需要提交报价的行')
            : _priceControl === 'unlimited'
            ? this.$t('本次寻源不限制报价行数，请勾选您需要提交报价的行')
            : this.$t('本次报价无限制，您可以报任意多行，请勾选您需要提交报价的行')
        this.$toast({
          content: _msg,
          type: 'warning'
        })
        return false
      }
      // TODO:待调整
      if ([31, 32, 41].includes(this.transferStatus)) {
        let { checked, checkedMsg } = this.validRequiredFields(data)
        if (!checked) {
          this.$toast({
            content: `采购明细Tab，${checkedMsg}`,
            type: 'error'
          })
          return false
        }
      }
      return true
    },
    // 保存 - 必填字段校验（暂不校验子集）
    validRequiredFields(data) {
      let _parentRequired = utils.cloneDeep(this.parentRequiredList)
      let checked = true,
        checkedMsg = ''
      for (let i = 0; i < data.length; i++) {
        let p = data[i]
        let { res, msg } = this.checkFields(p, _parentRequired)
        if (!res) {
          checked = false
          checkedMsg = msg
          break
        }
      }
      return { checked, checkedMsg }
    },
    // 保存 - 校验字段
    checkFields(obj, fields) {
      let res = true,
        msg = ''
      for (let i = 0; i < fields.length; i++) {
        let tableName = fields[i]['tableName']
        let field = fields[i]['fieldCode']
        let dto = tableNameMap[tableName]['key']
        if (dto === 'annualLogisticsTrunkItem') {
          if (!obj[field] && obj[field] !== 0) {
            const headerName = fields[i]['headerName']
            res = false
            if (headerName) {
              msg = `字段：${headerName}，为必填项`
            }
            break
          }
        } else {
          if (!obj[dto][field] && obj[dto][field] !== 0) {
            res = false
            let _field = this.columns.find((e) => e.fieldCode === field)
            if (_field?.fieldName) {
              msg = `字段：${_field.fieldName}，为必填项`
            }
            break
          }
        }
      }
      return { res, msg }
    },
    // 保存 - 数据校验
    validData(data) {
      let flag = true
      let isUnitConsistent = true
      if (!data || !data?.length) return false
      data.forEach((item) => {
        let minPurQuantity = item.biddingItemDTO.minPurQuantity
        let minPackageQuantity = item.biddingItemDTO.minPackageQuantity
        // 校验最小采购量&最小包装量
        if (
          judgePurAndQuantiry(minPurQuantity, minPackageQuantity) &&
          !this.txCompanyList.includes(this.$route.query?.companyCode)
        ) {
          this.$toast({
            content: this.$t('最小采购量需是最小包装数量的整数倍'),
            type: 'warning'
          })
          flag = false
          return
        }
        // 校验采购单位与基本单位是否一致
        if (judgeUnitExist(this.columns) && !judgeUnitConsistent(item)) {
          isUnitConsistent = false
          return
        }
      })
      return { flag, isUnitConsistent }
    },
    // 保存 - 数据组装
    combineData(data) {
      const biddingKey = logisticsTableNameMap[this.quotedPriceData.sourcingScenarios]?.resKey
      return data.map((item) => {
        let _obj = {}
        // submitField字段处理
        this.submitField.forEach((field) => {
          let _fieldCode = field.fieldCode
          if (_fieldCode === 'supplierDrawing') {
            //如果供应商附件 处理附件id 为sysFileId
            _obj['supplierFileList'] = this.processDrawingData(item[_fieldCode])
            return
          }

          _obj[_fieldCode] =
            _fieldCode in item['biddingItemDTO']
              ? item['biddingItemDTO'][_fieldCode]
              : item[_fieldCode]
          if (_fieldCode === 'screenPriceMonth') {
            let _code = item['biddingItemDTO'][_fieldCode]
            _obj[_fieldCode] = _code
              ? typeof _code === 'string'
                ? _code.substring(0, 6)
                : formatTime(new Date(item['biddingItemDTO'][_fieldCode]), 'Ymm')
              : ''
          }
          // 优先取报价行 || 明细行
        })
        // 处理物流阶梯字段
        if (item[biddingKey]?.dynamicFields) {
          for (let i in item[biddingKey]?.dynamicFields) {
            item[biddingKey]?.dynamicFields[i].forEach((dynamic) => {
              dynamic.untaxedUnitPrice = item[`${dynamic.fieldCode}_untaxedUnitPrice`]
            })
          }
        }
        //其他字段处理
        let _otherFields = {
          biddingId: item['biddingItemDTO']?.biddingId || null,
          biddingItemId: item['biddingItemDTO']?.biddingItemId || null,
          rfxItemId: item?.rfxItemId || null,
          priceUnitName: item?.priceUnitName,
          itemStages: item?.itemStages,
          biddingItemLogisticsDTO: item['biddingItemLogisticsDTO'], //物流参数
          rfxAnnualLogisticsSeaDTO: item['rfxAnnualLogisticsSeaDTO'], //物流海运年月需求 - 海运
          annualLogisticsRailwayItemDTO: item['annualLogisticsRailwayItemDTO'], //物流铁运年月需求 - 铁运
          annualLogisticsTrunkItem: item['annualLogisticsTrunkItem'] //物流铁运年月需求 - 干线
        }
        _obj = Object.assign({}, _obj, _otherFields)
        return _obj
        // *特殊处理,如果未配置bidTaxRateValue，根据bidTaxRateCode来取（建议策略配置该字段）,暂不作处理，跟进
      })
    },
    // 保存 -数据组装 - 处理附件数据
    processDrawingData(files) {
      if (!files) return []
      let _files = JSON.parse(files)
      _files.forEach((file) => {
        if (file?.sysFileId) return file
        file.sysFileId = file.id
        delete file.id
      })
      return _files
    },
    // 保存 - 接口提交
    saveData(params, isAutoSave) {
      this.$API.supplyQdetail.priceSave(params).then((res) => {
        if (res.code == 200) {
          if (!isAutoSave) {
            // 自动保存不用提示
            this.$toast({
              content: this.$t('操作成功'),
              type: 'success'
            })
            //重刷表格
            this.initTableData()
            this.$emit('mainparticipateButton', 'getDataOnly') // 仅获取数据，不切换页签
          }
        }
      })
    },
    // 保存 - 序列化数据 (暂不处理父子结构)
    serializeSaveParams(list) {
      let res = []
      list.forEach((e) => {
        let _sub = e?.childItems
        delete e.childItems
        res.push(e)
        if (Array.isArray(_sub)) {
          res = res.concat(_sub)
        }
      })
      return res
    },
    // btn - 重新报价
    handleReQuote() {
      this.$emit('update:newPrice', 1)
      // this.fieldDefines.forEach((item) => { //重新报价编辑逻辑控制todo
      //   // //可以编辑
      //   const allowEditingTbs = ['mt_supplier_bidding_item', 'mt_supplier_bidding_item_logistics']
      //   if (allowEditingTbs.includes(item.tableName)) {
      //     item.allowEditing = true
      //   } else {
      //     item.allowEditing = false
      //   }
      // })
      if (this.moduleType == 0) {
        this.tableData = this.resetQuoteParams(this.tableData)
      }
      this.$toast({
        content: this.$t('请调整物料价格，提交报价！'),
        type: 'warning'
      })
    },
    // 重新报价 - 重置参数
    resetQuoteParams(list) {
      if (!Array.isArray(list)) return []
      list.forEach((e) => {
        e['biddingItemDTO'].biddingId = 0
        e['biddingItemDTO'].biddingItemId = 0
        e['biddingItemDTO'].submitStatus = 0
      })
      return list
    },
    // btn - 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'rfxRequireDetail',
        templateUrl: 'supExportPriceTemplete',
        uploadUrl: `supImportPriceTemplete`,
        rfxId: this.$route.query.rfxId
      }
      this.showUploadExcel(true)
    },
    // btn - 导出(物流报价单导出)
    async handleExport() {
      this.$store.commit('startLoading')
      const transferStatus = this.quotedPriceData?.transferStatus
      let res = await this.$API.supplierTender
        .logisticsExport({
          rfxId: this.$route.query?.rfxId,
          // 报价标识 0 当前报价 1 历史报价 2 议价
          tabType: transferStatus === 31 ? 0 : transferStatus === 32 ? 2 : 1,
          queryBuilderDTO: {
            page: {
              current: 1,
              size: 200
            }
          }
        })
        .catch(() => {})
      let buffer = null
      res?.data && (buffer = res.data)
      if (buffer instanceof Blob) {
        if (buffer.type === 'application/json') {
          try {
            const json = JSON.parse(await buffer.text())
            if (json.msg) {
              this.$toast({ content: json.msg, type: 'warning' })
            }
          } catch (error) {
            console.warn(error)
          }
        } else if (buffer.type === 'application/pdf') {
          const url = URL.createObjectURL(buffer)
          window.open(url)
        } else if (buffer.type === 'application/vnd.ms-excel') {
          const blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel'
          }) //设置type类型，这里是excel
          const downloadElement = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          downloadElement.style.display = 'none'
          downloadElement.href = href
          const fileName = res.headers['content-disposition'].split('filename=')[1]
          downloadElement.download = decodeURI('' + fileName)
          document.body.appendChild(downloadElement)
          downloadElement.click()
          document.body.removeChild(downloadElement)
          window.URL.revokeObjectURL(href)
        }
      }
      this.$store.commit('endLoading')
    },
    // 导入 - 弹框显示
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadInitBalanceRef.uploadData = [] // 清空数据
        this.$refs.uploadInitBalanceRef.$refs.uploader.files = []
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadInitBalanceRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入 - 上传确认
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.initTableData()
      // this.$refs.templateRef.refreshCurrentGridData()
    },
    // btn - 刷新
    refresh() {
      this.initTableData()
    },
    // btn - 查询
    search(params) {
      this.initTableData(params)
    },
    // <<<<<<<<<<<<<<<<<<<<<<<<<<  初始化  >>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 初始化 - 页面
    async init() {
      await this.initDictItems()
      this.initTab()
      this.initSearchConfig()
      this.initTotalInfo()
      this.initToolbar()
      this.initGridColumns()
      this.initTableData()
    },
    // 初始化 - 字典数据
    async initDictItems() {
      await this.$API.masterData
        .getSupplierDictionarys([
          {
            code: 'SHIP_MODE',
            type: 'string'
          },
          {
            code: 'SUPPLY_TYPE',
            type: 'string'
          },
          {
            code: 'LOGISTICS_ANNUAL_POL_QUOTE_UNIT',
            type: 'string'
          }
        ])
        .then((res) => {
          this.dictItems = res.data || {}
        })
    },
    // 初始化 - tab页签
    initTab() {
      let _tabSource = [
        { title: this.$t('议价'), moduleType: 2 },
        { title: this.$t('当前报价'), moduleType: 0 },
        { title: this.$t('历史报价'), moduleType: 1 }
      ]
      if (this.quotedPriceData.transferStatus != 32) {
        //非议价状态没有议价tab页
        _tabSource.shift()
      }
      this.tabSource = _tabSource
    },
    // 初始化 - 查询区域
    initSearchConfig() {
      let _config = [
        {
          field: 'itemCode',
          label: this.$t('物料编码')
        },
        {
          field: 'rfx_item.itemName',
          label: this.$t('物料名称')
        },
        {
          field: 'rfx_item.temporaryItemCode',
          label: this.$t('临时物料编码')
        },
        {
          field: 'rfx_item.categoryCode',
          label: this.$t('品类编码')
        }
      ]
      this.searchConfig = _config
    },
    // 初始化 - toolbar
    initToolbar() {
      if (this.moduleType === 1) {
        // 历史报价 不显示
        this.toolbar = []
        return
      }
      let _toolbar = [
        {
          id: 'Save',
          icon: 'icon_solid_Save',
          title: this.$t('保存')
        },
        {
          id: 'ReQuote',
          icon: 'icon_solid_edit',
          title: this.$t('重新报价')
        },
        {
          id: 'Import',
          icon: 'icon_solid_Import',
          title: this.$t('导入')
        }
      ]
      this.toolbar = this.status === 1 && this.joinStatus === 1 ? _toolbar : []
    },
    // 初始化 - 汇总信息（上次总金额，本次总金额）
    initTotalInfo() {
      this.getLastTotal()
    },
    // 汇总信息 - 获取本次总金额(未税总金额之和)
    getCurrentTotal(params) {
      const field = params.column.colId
      if (field !== 'biddingItemDTO.taxedUnitPrice') return
      let _dataSource = cloneDeep(this.tableData)
      let _currentTotal = 0
      _dataSource.forEach((item) => {
        if (item.biddingItemDTO.taxedUnitPrice)
          _currentTotal = _currentTotal + Number(item.biddingItemDTO.taxedUnitPrice)
      })
      this.currentTotal = _currentTotal.toFixed(2)
    },
    // 汇总信息 - 获取上次总金额
    async getLastTotal() {
      let _params = {
        rfxId: this.$route.query.rfxId,
        tabType: this.moduleType
      }
      this.$API.supplyQdetail
        .getRfxBidAmount(_params)
        .then((res) => {
          if (res.code == 200) {
            this.lastTotal = res?.data?.lastTotalAmount || 0
            this.lastTotal = this.lastTotal.toFixed(this.ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
            this.currentTotal = this.lastTotal
          }
        })
        .catch(() => {
          this.lastTotal = 0
          this.lastTotal = this.lastTotal.toFixed(this.ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
          this.currentTotal = this.lastTotal
        })
    },
    // 初始化 - 表头
    async initGridColumns() {
      this.getLogisticsItemConfig && (await this.getLogisticsItemConfig()) //特殊处理 - 物流弹框
      // 添加表头固定字段
      let _columnData = this.setFixedColumns(utils.cloneDeep(this.fieldDefines))
      // 设置表头属性
      _columnData = this.defineGridColumns(_columnData)
      // 物流海运表头特殊设置
      if (this.quotedPriceData.sourcingScenarios === 'sea_transport_annual') {
        _columnData = await this.setAnnualLogisticsItemConfig(_columnData) // 物流海运字段较多，需要设置为双表头结构
      }
      // 物流干线表头特殊设置
      if (this.quotedPriceData.sourcingScenarios === 'trunk_transport_annual') {
        _columnData = await this.setLogisticsDynamicConfig(_columnData) // 物流海运字段较多，需要设置为双表头结构
      }
      this.columns = cloneDeep(_columnData)
    },

    // 表头 - 获取表头是否可编辑
    getGridEditAble() {
      // 未参与
      if (this.quotedPriceData.joinStatus === 0) return false
      // 倒计时内
      if (Number(this.rfxCountdown.countDown) <= Date.now()) return false
      // 议价中 & 议价页签可编辑
      if (this.moduleType === 2 && this.quotedPriceData.transferStatus === 32) return true
      // 报价中|| 商务投标中 采购明细可以编辑
      if (this.moduleType === 0 && [31, 41].includes(this.quotedPriceData.transferStatus))
        return true
      return false
    },
    // 表头 - 列字段处理
    defineGridColumns(fieldDefines, isChild = false) {
      if (!Array.isArray(fieldDefines) || fieldDefines.length < 1) {
        return []
      }
      let _gridEditAble = this.getGridEditAble()
      _gridEditAble =
        _gridEditAble &&
        !['sea_transport', 'air_transport'].includes(this.quotedPriceData.sourcingObjType) // 物流(临时需求)时，空运和海运不可编辑，弹框操作，陆运可以操作
      let _requiredFields = []
      let _columnData = []
      // 不同tableName对应不同配置文件，特殊字段属性在此配置，如果没配置，则为普通字段
      const tableColumnMap = {
        // {字段所在tableName: 字段配置文件 : {prefix: '字段所属dto（一般采方供方dto不同，前端公用一套配置文件）'}}
        mt_supplier_rfx_item: rfxItem({
          isSup: true
        }),
        mt_supplier_bidding_item: biddingItem({
          prefix: 'biddingItemDTO.',
          companyCode: this.quotedPriceData.companyCode,
          ktFlag: this.ktFlag,
          quotedPriceData: this.quotedPriceData,
          currencyList: this.currencyList,
          taxList: this.taxList,
          purUnitList: this.purUnitList,
          dictItems: this.dictItems,
          isFc: this.quotedPriceData.rfxGeneralType === 2
        }),
        mt_supplier_rfx_item_ext: itemExt({
          prefix: 'itemExtMap.'
        }),
        mt_supplier_bidding_item_logistics: biddingItemLogistics({
          prefix: 'biddingItemLogisticsDTO.',
          dictItems: this.dictItems,
          currencyList: this.currencyList
        }),
        mt_supplier_rfx_item_logistics: itemLogistics({
          prefix: 'rfxItemLogisticsDTO.',
          dictItems: this.dictItems
        }),
        mt_supplier_rfx_item_die: itemDie({
          prefix: 'rfxItemDieDTO.'
        }),
        // mt_rfx_annual_logistics_sea_item: itemLogisticsSea({
        //   prefix: 'rfxAnnualLogisticsSeaDTO.'
        // }),
        mt_rfx_annual_logistics_sea_bidding_item: itemLogisticsSea({
          prefix: 'rfxAnnualLogisticsSeaDTO.',
          dictItems: this.dictItems
        }),
        // logistics_railway_item: itemLogisticsRailway({
        //   prefix: 'annualLogisticsRailwayItemDTO.',
        //   dictItems: this.dictItems
        // }),
        bidding_annual_railway: itemLogisticsRailway({
          prefix: 'annualLogisticsRailwayItemDTO.',
          dictItems: this.dictItems
        }),
        annual_logistics_transport: itemLogisticsTrunk({
          prefix: 'annualLogisticsRailwayItemDTO.', //
          dictItems: this.dictItems
        })
      }
      //TODO: ??? 逻辑待确认
      let filterFields = fieldDefines.filter(
        (field) =>
          ![
            'mt_supplier_rfx_item.currencyCode', // 币种
            'mt_supplier_rfx_item.currencyName',
            'mt_supplier_rfx_item.taxRateCode', // 税率
            'mt_supplier_rfx_item.taxRateName',
            'mt_supplier_rfx_item.taxRateValue',
            'mt_supplier_rfx_item.purUnitCode', // 采购单位
            'mt_supplier_rfx_item.purUnitName',
            'mt_supplier_rfx_item.conversionRate' // 转换率
          ].includes(`${field.tableName}.${field.fieldCode}`)
      )
      filterFields.forEach((item) => {
        let tableName = item.tableName || 'mt_supplier_rfx_item'
        let name = tableNameMap[tableName]?.key
        let field = name ? `${name}.${item.fieldCode}` : `${item.fieldCode}`
        let headerName = item.headerName || item.fieldName
        let editable = false

        // 与配置文件中字段进行匹配，添加属性
        let column = tableColumnMap[tableName]?.find((e) => e.field === field) || {}
        let columnEditAble =
          'editable' in column && typeof column.editable === 'boolean' && column.editable //取配置中的为boolean的editable
        // 特殊处理 - 物流弹框字段处理
        this.setLogisticsItemAttr && this.setLogisticsItemAttr(tableName, item.fieldCode, column)
        /**
         * 字段整是否可编辑整理
         * 1.mt_supplier_bidding_item 非组合结构场景，childGridReadonlyFields等字段子读，其他可编辑
         * 2.mt_supplier_bidding_item_logistics 物流信息可编辑
         * 3.rfx_file 文件信息 供应商附件可编辑
         * 4.合格供应商禁用币种、税率、报价属性、报价生效方式字段
         * 5.物流海运/铁运年约需求 mt_rfx_annual_logistics_sea_bidding_item ，bidding_annual_railway 指定字段可编辑
         */

        let editFlag =
          (tableName === 'mt_supplier_bidding_item' &&
            (combination.isCombination(this.quotedPriceData) ||
              !childGridReadonlyFields.includes(field))) ||
          tableName === 'mt_supplier_bidding_item_logistics' ||
          ([
            'mt_rfx_annual_logistics_sea_bidding_item',
            'bidding_annual_railway',
            'annual_logistics_transport'
          ].includes(tableName) &&
            logisticsEditFields.includes(item.fieldCode)) ||
          (tableName === 'rfx_file' && item.fieldCode === 'supplierDrawing')
        if (!notAllowEditFields.includes(field) && editFlag) {
          // 编辑字段
          editable = true
        }
        if (!notSubmitFields.includes(field) && editFlag) {
          // 提交数据
          this.submitField.push(item)
        }
        if (
          this.quotedPriceData.supplierRange === 'category_qualified' &&
          [
            'biddingItemDTO.bidCurrencyCode',
            'biddingItemDTO.bidCurrencyName',
            'biddingItemDTO.quoteAttribute',
            'biddingItemDTO.quoteMode',
            'biddingItemDTO.bidTaxRateCode',
            'biddingItemDTO.bidTaxRateName',
            'biddingItemDTO.bidTaxRateValue'
          ].includes(field)
        ) {
          editable = false
        }
        if (
          [
            'mt_supplier_bidding_item',
            'mt_rfx_annual_logistics_sea_bidding_item',
            'bidding_annual_railway',
            'annual_logistics_transport'
          ].includes(tableName) &&
          item.required === 1
        ) {
          _requiredFields.push(item) // 通用报价、物流设置必填字段
        }
        // 配置属性覆盖
        let col = {
          width: 130, //默认值130
          editable,
          cellClass: () => {
            return !editable && !columnEditAble ? 'singleCellDisable' : ''
          },
          hide: item.hide,
          ...column,
          field,
          headerName,
          tableName,
          fieldCode: item.fieldCode,
          fieldName: headerName,
          required: item.required,
          fieldDesc: item.fieldDesc
        }
        // 特殊处理 - 海运年约需求、铁运年月需求数字框处理
        if (logisticsNumberEditFields.includes(item.fieldCode)) {
          col = {
            ...col,
            option: 'customEdit',
            editConfig: {
              type: 'number',
              props: {
                ...PRICE_EDIT_CONFIG
              }
            }
          }
        }
        if (logisticsIntEditFields.includes(item.fieldCode)) {
          col = {
            ...col,
            option: 'customEdit',
            editConfig: {
              type: 'number',
              props: {
                min: 0,
                max: 100,
                precision: '0'
              }
            }
          }
        }
        // 特殊处理 - 成本分析
        if (['costAnalysis'].includes(item.fieldCode)) {
          col.editable = false
          col.cellRenderer = 'cellLink'
          col.cellRendererParams = {
            isAutoSave: true,
            handleable: _gridEditAble, //“可操作”（不可编辑），取editable避免从历史报价进入成本分析页面
            rfxId: this.$route.query.rfxId,
            abateFlag: this.quotedPriceData.transferStatus === 32 // 还价标志：议价为true，否则为false
          }
        }
        // 特殊处理 - 附件查看
        if (['drawing', 'supplierDrawing'].includes(item.fieldCode)) {
          col.editable = false
          col.cellRenderer = 'cellFile'
          col.cellRendererParams = {
            handleable: editable && item.fieldCode === 'supplierDrawing' //供应商附件“可操作”（不可编辑）
          }
        }
        // 特殊处理 - 柜量
        if (['cabinetQuantity'].includes(item.fieldCode)) {
          col.cellRenderer = 'cellDialog'
          col.editable = false
          col.cellRendererParams = {
            position: 'rfxAnnualLogisticsSeaDTO',
            handleable: true, //“可操作”（不可编辑）（针对于附件查看类字段）
            rfxId: this.$route.query.rfxId
          }
        }
        // 统一处理 如果非编辑状态下表格,则置空背景和禁用编辑
        if (!_gridEditAble) {
          col.editable = false
          col.cellClass = ''
        }
        _columnData.push(col)
      })
      if (isChild) {
        this.childRequiredList = _requiredFields
      } else {
        this.parentRequiredList = _requiredFields
      }
      // 添加勾选框
      if (this.isSelectControl()) {
        _columnData.unshift({ option: 'checkboxSelection', width: 55 })
      }
      return _columnData
    },
    // 表头 - 物流双表头字段处理
    async setAnnualLogisticsItemConfig(columns) {
      let res = await this.$API.supplyQdetail.getLogisticsConfig({
        rfxId: this.$route.query.rfxId,
        tabType: 0
      })
      if (res.code === 200) {
        this.annualLogisticsItemConfig = [...res.data]
      }
      let _columns = this.groupColumns(columns, this.annualLogisticsItemConfig)
      return _columns
    },
    // 物流 - 获取阶梯列配置
    async setLogisticsDynamicConfig(_columnData) {
      let _gridEditAble = this.getGridEditAble()
      let dynamicConfig = []
      const res = await this.$API.rfxDetail.getLogisticsDynamicConfig({
        id: this.$route.query.rfxId
      })
      if (res.code === 200) {
        const _data = { ...res.data } || {}
        for (let i in _data) {
          _data[i].forEach((item) => {
            dynamicConfig.push({
              headerName: item.stepName,
              children: [
                {
                  headerName: this.$t('数量'),
                  width: 80,
                  field: `${item.stepCode}_fieldData`,
                  fieldCode: `${item.stepCode}_fieldData`
                },
                {
                  headerName: this.$t('未税单价'),
                  width: 120,
                  field: `${item.stepCode}_untaxedUnitPrice`,
                  fieldCode: `${item.stepCode}_untaxedUnitPrice`,
                  editable: _gridEditAble,
                  required: 1,
                  option: 'customEdit',
                  editConfig: {
                    type: 'number',
                    props: {
                      min: 0,
                      precision: 2
                    }
                  }
                }
              ]
              // tableName: logisticsTableNameMap[this.quotedPriceData.sourcingScenarios]['tableName']
            })
            this.parentRequiredList.push({
              tableName: logisticsTableNameMap[this.quotedPriceData.sourcingScenarios]['tableName'],
              headerName: this.$t('未税单价'),
              field: `${item.stepCode}_untaxedUnitPrice`,
              fieldCode: `${item.stepCode}_untaxedUnitPrice`
            })
          })
        }
      }
      // 找到字段三的索引
      const index = _columnData.findIndex((column) => column.fieldCode === 'totalQty')
      // 将 stepList 插入到字段三之前
      if (index !== -1) {
        _columnData.splice(index, 0, ...dynamicConfig)
      }
      return _columnData
    },
    groupColumns(columns, obj) {
      const result = []
      const groupMap = new Map()

      obj.forEach((group) => {
        groupMap.set(group.fieldGroup, group.fields)
      })
      const addedFieldCodes = new Set()
      columns.forEach((column) => {
        const groupEntry = Array.from(groupMap.entries()).find(([_, fields]) =>
          fields.some((field) => field.fieldCode === column.fieldCode)
        )
        if (groupEntry) {
          const [groupName] = groupEntry
          let groupItem = result.find((item) => item.fieldName === groupName)
          if (!groupItem) {
            groupItem = { headerName: groupName, fieldName: groupName, children: [] }
            result.push(groupItem)
          }
          let editable = !logisticsNotAllowEditFields.includes(column.fieldCode)
          let cellClass = !editable ? 'singleCellDisable' : ''
          groupItem.children.push({ ...column, editable, cellClass })
          addedFieldCodes.add(column.fieldCode)
        } else {
          result.push({ ...column })
        }
      })

      // 过滤掉已添加到分组中的字段
      return result.filter((item) => !addedFieldCodes.has(item.fieldCode))
    },
    // 表头 - 列固定字段处理(此处需与接口获取数据一致) tableName => 基础字段: mt_supplier_rfx_item(默认) 报价明细：mt_supplier_bidding_item
    setFixedColumns(columnData) {
      let _preColumns = [
        {
          fieldCode: 'rowIndex',
          fieldName: this.$t('序号')
        },
        // {
        //   fieldCode: 'lineNo',
        //   fieldName: this.$t('行号')
        // },
        {
          fieldCode: 'priceStatus', // 'biddingItemDTO.priceStatus'
          fieldName: this.$t('状态'),
          tableName: 'mt_supplier_bidding_item'
        }
      ]
      // 如果没配未税总价、和含税总价，则固定配置，价格计算时候会更新这两个字段
      let _find = columnData.find((item) =>
        ['untaxedTotalPrice', 'taxedTotalPrice'].includes(item.fieldCode)
      )
      if (!_find) {
        _preColumns.push(
          // 添加默认字段（价格计算时候会默认计算）,此处不配做价格计算时候会报错，填充不了值
          {
            fieldCode: 'untaxedTotalPrice', // 'biddingItemDTO.untaxedTotalPrice'
            fieldName: this.$t('未税总价'),
            hide: true,
            tableName: 'mt_supplier_bidding_item'
          },
          {
            fieldCode: 'taxedTotalPrice', // 'biddingItemDTO.taxedTotalPrice'
            fieldName: this.$t('含税总价'),
            hide: true,
            tableName: 'mt_supplier_bidding_item'
          }
        )
      }

      let _columnData = _preColumns.concat(columnData)
      if (this.moduleType === 2) {
        //如果是议价tab,添加’议价理由‘
        _columnData.splice(2, 0, {
          fieldCode: 'abateReason', // 'biddingItemDTO.abateReason'
          fieldName: this.$t('议价理由'),
          tableName: 'mt_supplier_bidding_item'
        })
      }
      return _columnData
    },
    // 表头 - 工具 - 判断是否需要选择框
    isSelectControl() {
      let isSelect = false
      let _priceControl = this.strategyConfig[0]['priceControl']
      if (this.quotedPriceData.joinStatus === 0) return false // 未参与不显示select框
      if (_priceControl) {
        //如果设置了报价规则，才执行。未设置报价规则，跳过
        if (this.moduleType === 2) {
          //议价Tab，必须执行勾选数据
          isSelect = true
        } else if (this.moduleType === 0) {
          //无限制   首次整单报价(非首次，使用勾选数据)
          if (
            _priceControl === 'unlimited' ||
            // (_priceControl === 'first_all' && this.lastTotal !== 0)
            (_priceControl === 'first_all' && this.quotedPriceData.firstPrice === 0)
          ) {
            isSelect = true
          }
        }
      }
      return isSelect
    },
    // 初始化 - 表格数据
    async initTableData(rules) {
      this.$store.commit('startLoading')
      this.tableData = []
      let params = this.mergeParams(rules)
      const res = await this.$API.supplyQdetail.tenderItems(params).catch(() => {})
      if (res) {
        let records = cloneDeep(res.data?.records || [])
        let list = await this.serializeList(records)
        this.tableData = [...list]
        this.$store.commit('endLoading')
      }
    },
    // 表格数据 - 拼接请求参数
    mergeParams(rules) {
      let params = {
        rfxId: this.$route.query.rfxId,
        tabType: this.moduleType,
        queryBuilderDTO: {
          page: {
            current: 1,
            size: 10000
          }
        }
      }
      if (rules) {
        params.queryBuilderDTO = Object.assign(params.queryBuilderDTO, rules)
      }
      return params
    },
    // 表格数据 - 初始处理数据
    async serializeList(list) {
      this.$emit('update:newPrice', 0)
      this.serializeGridList(list)
      // this.setSharePriceUntaxed(list) // 前端初始不参与计算分摊后单价未税
      await this.initSourceInfo(list)
      this.actionObj = list
      this.oldList = utils.cloneDeep(list)
      return list
    },
    // 表格数据 - 序列化数据
    serializeGridList(list) {
      const biddingKey = logisticsTableNameMap[this.quotedPriceData.sourcingScenarios]?.resKey
      list.forEach((row, index) => {
        row.addId = uuidv4()
        row.rowIndex = index + 1 //分页后另作处理 TODO
        if (row.biddingItemDTO && typeof row.biddingItemDTO === 'object') {
          // 获取采方税率 ?? 单据税率
          const taxRateCode = row.taxRateCode || this.quotedPriceData?.taxRateCode
          // 获取采方币种 ?? 单据币种
          const currencyCode =
            row?.biddingItemDTO?.currencyCode || this.quotedPriceData?.currencyCode

          if (
            taxRateCode &&
            !row.biddingItemDTO.bidTaxRateCode &&
            !row.biddingItemDTO.bidTaxRateValue
          ) {
            const taxRow = this.taxList.find((e) => e.taxItemCode === taxRateCode)
            if (taxRow) {
              row.biddingItemDTO.bidTaxRateCode = taxRow.taxItemCode
              row.biddingItemDTO.bidTaxRateName = taxRow.taxItemName
              row.biddingItemDTO.bidTaxRateValue = taxRow.taxRate
            }
          }

          if (row.biddingItemDTO.bidTaxRateCode && !row.biddingItemDTO.bidTaxRateValue) {
            const taxRow = this.taxList.find(
              (e) => e.taxItemCode === row.biddingItemDTO.bidTaxRateCode
            )
            if (taxRow) {
              row.biddingItemDTO.bidTaxRateCode = taxRow.taxItemCode
              row.biddingItemDTO.bidTaxRateName = taxRow.taxItemName
              row.biddingItemDTO.bidTaxRateValue = taxRow.taxRate
            }
          }

          if (
            currencyCode &&
            (!row.biddingItemDTO.bidCurrencyCode || !row.biddingItemDTO.bidCurrencyName)
          ) {
            const findRow = this.currencyList.find((e) => e.currencyCode === currencyCode)
            if (findRow) {
              row.biddingItemDTO.bidCurrencyCode = findRow.currencyCode
              row.biddingItemDTO.bidCurrencyName = findRow.currencyName
            }
          }

          if (!row.biddingItemDTO.bidPurUnitCode) {
            row.biddingItemDTO.bidPurUnitName = ''
          }

          // 获取采方采购单位
          if (
            row.purUnitCode &&
            (!row.biddingItemDTO.bidPurUnitCode || !row.biddingItemDTO.bidPurUnitName)
          ) {
            const findRow = this.purUnitList.find((e) => e.unitCode === row.purUnitCode)
            if (findRow) {
              row.biddingItemDTO.bidPurUnitCode = findRow.unitCode
              row.biddingItemDTO.bidPurUnitName = findRow.unitName
            }
          }

          // 转换率
          if (!row.biddingItemDTO.bidConversionRate && row.conversionRate) {
            row.biddingItemDTO.bidConversionRate = row.conversionRate
          }

          // 整机模组外发，单价未税=运费+加工费+加工费材料费
          if (this.quotedPriceData.sourcingObjType === 'module_out_going') {
            const { transportCost, processCost, processPartyMaterialCost, bidTaxRateValue } =
              row.biddingItemDTO

            // 单价（未税）
            row.biddingItemDTO.untaxedUnitPrice =
              transportCost + processCost + processPartyMaterialCost || 0

            // 单价（含税）
            let taxedUnitPrice = 0
            const rate = new Decimal32(bidTaxRateValue || 0).add(1).toString()
            if (bidTaxRateValue >= 0) {
              taxedUnitPrice = new Decimal32(row.biddingItemDTO.untaxedUnitPrice)
                .mul(rate)
                .toFixed(this.ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
              row.biddingItemDTO.taxedUnitPrice = taxedUnitPrice
            }
            row.biddingItemDTO.taxedUnitPrice = taxedUnitPrice
          }
          // 价格单位
          row.biddingItemDTO.priceUnitName = row.priceUnitName || ''
        }
        row.stepNum = row.stepValue
        row.drawing = row.drawings ? JSON.stringify(row.drawings) : null //单独处理附件字段
        row.stepQuoteName =
          row.itemStageList && row.itemStageList.length > 0
            ? JSON.stringify(row.itemStageList)
            : null //单独处理阶梯报价
        row.supplierDrawing = row.supplierFileList ? JSON.stringify(row.supplierFileList) : null //单独处理"供应商附件"字段
        if (row.itemExtMap && row.itemExtMap.minQuoteRangeType == 1) {
          row.itemExtMap.minQuoteRange = new Decimal(row.itemExtMap.minQuoteRange)
            .mul(new Decimal(100))
            .toNumber()
        }
        if (row[biddingKey]?.dynamicFields) {
          for (let i in row[biddingKey]?.dynamicFields) {
            row[biddingKey]?.dynamicFields[i].forEach((item) => {
              const _fieldData = `${item.fieldCode}_fieldData`
              const _fieldUntaxedUnitPrice = `${item.fieldCode}_untaxedUnitPrice`
              row[_fieldData] = item.fieldData
              row[_fieldUntaxedUnitPrice] = item.untaxedUnitPrice
            })
          }
          // const logisticsData = this.serializeLogisiticsData(row[biddingKey]?.dynamicFields)
          // row = { ...row, ...logisticsData }
        }
        let _subItems = row['childItems'] // 阶梯暂无childItems
        if (Array.isArray(_subItems)) {
          this.serializeGridList(_subItems)
        }
      })
    },
    // 表格数据 - 处理物流阶梯数据
    serializeLogisiticsData(data) {
      let logisticsData = {}
      for (let i in data) {
        data[i].forEach((item) => {
          const _fieldData = `${item.fieldCode}_fieldData`
          const _fieldUntaxedUnitPrice = `${item.fieldCode}_untaxedUnitPrice`
          logisticsData[_fieldData] = item.fieldData
          logisticsData[_fieldUntaxedUnitPrice] = item.untaxedUnitPrice
        })
      }
      return logisticsData
    },
    // 表格数据 - 初始化处理 供应商信息, 关联报价属性、报价方式
    async initSourceInfo(dataSource) {
      if (!this.$route.query.rfxId || !this.quotedPriceData) {
        return
      }
      const categoryCodeList = dataSource.filter((e) => e.categoryCode).map((e) => e.categoryCode)
      const res = await this.$API.supplierTender
        .getSourceInfo({
          siteCode: this.quotedPriceData.siteCode,
          categoryCodeList,
          rfxId: this.$route.query.rfxId
        })
        .catch(() => {})
      if (!res || !Array.isArray(res.data)) {
        return
      }
      this.sourceInfo = this.sourceInfoConverter(res.data)
      // 合并报价方式、报价属性
      dataSource.forEach((ds) => {
        if (typeof ds.biddingItemDTO === 'object') {
          const sourceInfoRow = this.sourceInfo.find((e) => e.categoryCode === ds.categoryCode)
          //
          if (sourceInfoRow) {
            // quoteAttribute offerAttribute=报价属性 如果匹配到报价属性, 则不可编辑
            if (!ds.biddingItemDTO.quoteAttribute && sourceInfoRow.offerAttribute) {
              ds.biddingItemDTO.quoteAttribute = sourceInfoRow.offerAttribute
            }
            // quoteMode priceEffectiveMode=报价方式 如果匹配到报价方式，则不可编辑
            if (!ds.biddingItemDTO.quoteMode && sourceInfoRow.priceEffectiveMode) {
              ds.biddingItemDTO.quoteMode = sourceInfoRow.priceEffectiveMode
            }
          }
        }
      })
    },
    // 表格数据 - 转换报价属性 价格生效方式
    sourceInfoConverter(sourceInfo) {
      for (const row of sourceInfo) {
        const offerAttributeMap = {
          1: 'mailing_price',
          2: 'standard_price'
        }
        const priceEffectiveModeMap = {
          5: 'in_warehouse',
          1: 'order_date'
        }
        priceEffectiveModeMap['NULL'] = 'out_warehouse'
        row.offerAttribute = offerAttributeMap[row.offerAttribute]
        row.priceEffectiveMode = priceEffectiveModeMap[row.priceEffectiveMode]
      }
      return sourceInfo
    },
    // 表格数据 - 设置上次分摊价格
    setSharePriceUntaxed(list) {
      // sharePriceUntaxed  分摊后单价（未税）
      // untaxedUnitPrice   单价（未税）
      // realSharePriceUntaxed  实际分摊价（未税
      //分摊价=父不含税单价+（子分摊为是实际分摊价）
      list.forEach((row) => {
        let _sharePriceUntaxed = 0
        _sharePriceUntaxed += +row.biddingItemDTO?.untaxedUnitPrice ?? 0
        let _subItems = row['childItems']
        if (Array.isArray(_subItems)) {
          _subItems.forEach((e) => {
            _sharePriceUntaxed += +e.biddingItemDTO?.realSharePriceUntaxed ?? 0
          })
        }
        row.biddingItemDTO.sharePriceUntaxed = _sharePriceUntaxed
      })
    },
    // 表格数据 - 工具 - 获取税率
    getRate(e) {
      const bidTaxRateCode = e.biddingItemDTO.bidTaxRateCode
      const bidTaxRateValue = e.biddingItemDTO.bidTaxRateValue || '0'
      if (bidTaxRateCode) {
        // 通过税率编码获取
        let dataSource = this.taxList
        let fields = { value: 'taxItemCode', text: '__text' }
        if (Array.isArray(dataSource) && typeof fields === 'object') {
          const row = dataSource.find((e) => e[fields?.value || 'value'] === bidTaxRateCode)
          return row ? row?.taxRate : 0
        }
      }
      return bidTaxRateValue
    },
    // 表格 - 工具 - 延时提交
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    }
  }
}
