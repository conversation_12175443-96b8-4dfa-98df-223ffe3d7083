import { isNullOrUndefined } from '@/utils/is'
import { utils } from '@mtech-common/utils'
import { PRICE_FRACTION_DIGITS } from '@/constants/editConfig'
import { Decimal32, isSafeNumber, microTask } from './utils'

/**
 * 供方税率选择
 */
export const inputBidTaxRate = (ctx, { field, value }) => {
  const getValue = ctx.getValueByField
  const setValue = ctx.setValueByField

  if (['biddingItemDTO.bidTaxRateCode', 'biddingItemDTO.bidTaxRateName'].includes(field)) {
    const { dataSource, fields } = ctx.getOptions(field)
    const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
    setValue('biddingItemDTO.bidTaxRateCode', row.taxItemCode)
    setValue('biddingItemDTO.bidTaxRateName', row.taxItemName)
    setValue('biddingItemDTO.bidTaxRateValue', row.taxRate)
    const _ktFlag = sessionStorage.getItem('purDetailKtFlag')
    const sourcingObjType = sessionStorage.getItem('purDetailKtFlag')
    if ((_ktFlag && _ktFlag == 1) || sourcingObjType === 'single_module') {
      const vTaxedUnitPrice = getValue('biddingItemDTO.taxedUnitPrice')
      inputPrice(ctx, {
        field: 'biddingItemDTO.taxedUnitPrice',
        value: vTaxedUnitPrice,
        oldValue: vTaxedUnitPrice
      })
    } else {
      const vUntaxedUnitPrice = getValue('biddingItemDTO.untaxedUnitPrice')
      inputPrice(ctx, {
        field: 'biddingItemDTO.untaxedUnitPrice',
        value: vUntaxedUnitPrice,
        oldValue: vUntaxedUnitPrice
      })
    }
  } else if (['biddingItemDTO.bidTaxRateValue'].includes(field)) {
    setValue('biddingItemDTO.bidTaxRateCode', null)
    setValue('biddingItemDTO.bidTaxRateName', null)
  }
}

/**
 * 报价方式、报价属性联动编辑
 */
export const inputQuote = (ctx, { field, value }) => {
  const setValue = ctx.setValueByField
  const quoteMap = {
    // 报价方式
    'biddingItemDTO.quoteMode': {
      fieldsValue: 'quoteMode',
      link: {
        field: 'biddingItemDTO.quoteAttribute',
        fieldsValue: 'quoteAttribute'
      }
    },
    // 报价属性
    'biddingItemDTO.quoteAttribute': {
      fieldsValue: 'quoteAttribute',
      link: {
        field: 'biddingItemDTO.quoteMode',
        fieldsValue: 'quoteMode'
      }
    }
  }
  if (Object.keys(quoteMap).includes(field)) {
    const quoteMapItem = quoteMap[field]
    const { dataSource, fields } = ctx.getOptions(field)
    const quote = dataSource.find((e) => e[fields?.value || 'value'] === value)
    if (quote.value == 'standard_price') {
      setValue(quoteMapItem.link.field, 'in_warehouse')
    } else if (quote.value == 'mailing_price') {
      setValue(quoteMapItem.link.field, 'out_warehouse')
    } else if (quote.value == 'in_warehouse') {
      setValue(quoteMapItem.link.field, 'standard_price')
    } else if (quote.value == 'out_warehouse') {
      setValue(quoteMapItem.link.field, 'mailing_price')
    }
  }
}

/**
 * 币种编码联动编辑
 */
export const inputCurrency = (ctx, { field, value }) => {
  const setValue = ctx.setValueByField
  const currencyMap = {
    // 总计费用币种编码
    'biddingItemLogisticsDTO.totalFeeCurrencyCode': {
      fieldsValue: 'currencyCode',
      link: {
        field: 'biddingItemLogisticsDTO.totalFeeCurrencyName',
        fieldsValue: 'currencyName'
      }
    },
    'biddingItemLogisticsDTO.totalFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.totalFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },
    // 航空运费币种编码
    'biddingItemLogisticsDTO.airFeeCurrencyCode': {
      fieldsValue: 'currencyCode',
      link: {
        field: 'biddingItemLogisticsDTO.airFeeCurrencyName',
        fieldsValue: 'currencyName'
      }
    },
    'biddingItemLogisticsDTO.airFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.airFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 运费币种编码
    'biddingItemLogisticsDTO.transportFeeCurrencyCode': {
      fieldsValue: 'currencyCode',
      link: {
        field: 'biddingItemLogisticsDTO.transportFeeCurrencyName',
        fieldsValue: 'currencyName'
      }
    },
    'biddingItemLogisticsDTO.transportFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.transportFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 起运港杂费币种编码
    'biddingItemLogisticsDTO.startPortFeeCode': {
      fieldsValue: 'currencyCode',
      link: {
        field: 'biddingItemLogisticsDTO.startPortFeeName',
        fieldsValue: 'currencyName'
      }
    },
    'biddingItemLogisticsDTO.startPortFeeName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.startPortFeeCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 目的港杂费币种编码
    'biddingItemLogisticsDTO.endPortFeeCode': {
      fieldsValue: 'currencyCode',
      link: {
        field: 'biddingItemLogisticsDTO.endPortFeeName',
        fieldsValue: 'currencyName'
      }
    },
    'biddingItemLogisticsDTO.endPortFeeName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.endPortFeeCode',
        fieldsValue: 'currencyCode'
      }
    },

    // 代理费币种编码
    'biddingItemLogisticsDTO.agencyFeeCurrencyCode': {
      fieldsValue: 'currencyCode',
      link: {
        field: 'biddingItemLogisticsDTO.agencyFeeCurrencyName',
        fieldsValue: 'currencyName'
      }
    },
    'biddingItemLogisticsDTO.agencyFeeCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemLogisticsDTO.agencyFeeCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    },

    'biddingItemDTO.bidCurrencyCode': {
      fieldsValue: 'currencyCode',
      link: {
        field: 'biddingItemDTO.bidCurrencyName',
        fieldsValue: 'currencyName'
      }
    },
    'biddingItemDTO.bidCurrencyName': {
      fieldsValue: 'currencyName',
      link: {
        field: 'biddingItemDTO.bidCurrencyCode',
        fieldsValue: 'currencyCode'
      }
    }
  }
  if (Object.keys(currencyMap).includes(field)) {
    const currencyMapItem = currencyMap[field]
    const { dataSource, fields } = ctx.getOptions(field)
    const currency = dataSource.find((e) => e[fields?.value || 'value'] === value)
    if (currency) {
      setValue(currencyMapItem.link.field, currency[currencyMapItem.link.fieldsValue])
    }
  }
}

/**
 * 获取税率值 biddingItemDTO.bidTaxRateValue
 * @returns 税率值
 */
function getBidTaxRateValue(ctx) {
  const getValue = ctx.getValueByField
  const bidTaxRateCode = getValue('biddingItemDTO.bidTaxRateCode')
  const bidTaxRateValue = getValue('biddingItemDTO.bidTaxRateValue') || '0'
  if (bidTaxRateCode) {
    // 通过税率编码获取
    const { dataSource, fields } = ctx.getOptions('biddingItemDTO.bidTaxRateCode')
    if (Array.isArray(dataSource) && typeof fields === 'object') {
      const row = dataSource.find((e) => e[fields?.value || 'value'] === bidTaxRateCode)
      return row.taxRate
    }
  }
  return bidTaxRateValue
}

/**
 * 价格计算
 */
export const inputPrice = (ctx, { field, oldValue, value }) => {
  // biddingItemDTO.taxedTotalPrice	总价（含税）
  // biddingItemDTO.untaxedTotalPrice	总价（未税）
  // biddingItemDTO.taxedUnitPrice	单价（含税）
  // biddingItemDTO.untaxedUnitPrice	单价（未税）
  // biddingItemDTO.bidTaxRateValue 税率值
  // requireQuantity 需求数量 mt_supplier_rfx_item_ext
  let ktFlag = 0
  let _ktFlag = sessionStorage.getItem('purDetailKtFlag')
  let _companyCode = sessionStorage.getItem('companyCode')
  let _rfxGeneralType = sessionStorage.getItem('rfxGeneralType')
  let _buType = sessionStorage.getItem('buType')
  if (_ktFlag && _ktFlag == 1) {
    ktFlag = 1
  }
  // 此处ktFlag 只做是否5位标识
  const txCompanyList = JSON.parse(sessionStorage.getItem('txCompanyList') || '["2M01", "2S06"]')
  txCompanyList.includes(_companyCode) && (ktFlag = 1)
  _rfxGeneralType == 2 && (ktFlag = 1)
  _buType == 'GF' && (ktFlag = 1)
  const rowData = ctx.rowData
  const getValue = ctx.getValueByField
  const setValue = ctx.setValueByField
  const checkNumbers = []
  const listenFields = [
    'biddingItemDTO.untaxedUnitPrice',
    'biddingItemDTO.priceUnitName',
    'biddingItemDTO.taxedUnitPrice'
  ]

  if (rowData?.biddingItemDTO && listenFields.includes(field)) {
    if (!isSafeNumber(value)) {
      value !== oldValue &&
        microTask.then(() => {
          // 还原输入
          setValue(field, oldValue)
        })
      return
    }
    // 币种 如果是越南盾或者日元四舍五入省去小数点
    let currencyCode = getValue('biddingItemDTO.bidCurrencyCode')
    let isFixedZero = ['JPY', 'VND'].includes(currencyCode)
    // 价格单位
    let priceUnitName = getValue('biddingItemDTO.priceUnitName')
    priceUnitName =
      isNullOrUndefined(priceUnitName) || Number(priceUnitName) === 0 ? 1 : Number(priceUnitName)
    // 单价（未税）
    let untaxedUnitPrice = getValue('biddingItemDTO.untaxedUnitPrice') || '0'

    // 单价（含税）
    let taxedUnitPrice = getValue('biddingItemDTO.taxedUnitPrice') || '0'

    // 税率值
    const bidTaxRateValue = getBidTaxRateValue(ctx)
    const rate = new Decimal32(bidTaxRateValue).add(1).toString()

    if (bidTaxRateValue >= 0) {
      // 修改单价未税
      if (field === 'biddingItemDTO.untaxedUnitPrice') {
        taxedUnitPrice = new Decimal32(untaxedUnitPrice)
          .mul(rate)
          .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
        taxedUnitPrice = isFixedZero ? Math.round(taxedUnitPrice) : taxedUnitPrice
        setValue('biddingItemDTO.taxedUnitPrice', taxedUnitPrice)

        //以下为实际分摊价格，计算   shareQuantity实际分摊数量
        const shareQuantity = getValue('rfxItemDieDTO.shareQuantity')
        if (shareQuantity > 0) {
          let _taxed = new Decimal32(taxedUnitPrice / shareQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          _taxed = isFixedZero ? Math.round(_taxed) : _taxed
          let _untaxed = new Decimal32(untaxedUnitPrice / shareQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          _untaxed = isFixedZero ? Math.round(_untaxed) : _untaxed
          setValue('biddingItemDTO.realSharePriceTaxed', _taxed)
          setValue('biddingItemDTO.realSharePriceUntaxed', _untaxed)
        } else {
          setValue('biddingItemDTO.realSharePriceTaxed', 0)
          setValue('biddingItemDTO.realSharePriceUntaxed', 0)
        }

        //以下为规划分摊价格，计算   planQuantity实际分摊数量
        const planQuantity = getValue('rfxItemDieDTO.planQuantity')
        if (planQuantity > 0) {
          let _taxed = new Decimal32(taxedUnitPrice / planQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          _taxed = isFixedZero ? Math.round(_taxed) : _taxed
          let _untaxed = new Decimal32(untaxedUnitPrice / planQuantity).toFixed(
            ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS
          )
          _untaxed = isFixedZero ? Math.round(_untaxed) : _untaxed
          setValue('biddingItemDTO.planSharePriceTaxed', _taxed)
          setValue('biddingItemDTO.planSharePriceUntaxed', _untaxed)
        } else {
          setValue('biddingItemDTO.planSharePriceTaxed', 0)
          setValue('biddingItemDTO.planSharePriceUntaxed', 0)
        }
      } else if (field === 'biddingItemDTO.taxedUnitPrice') {
        // 修改单价含税
        untaxedUnitPrice = new Decimal32(taxedUnitPrice)
          .div(rate)
          .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
        untaxedUnitPrice = isFixedZero ? Math.round(untaxedUnitPrice) : untaxedUnitPrice
        setValue('biddingItemDTO.untaxedUnitPrice', untaxedUnitPrice)
      }
    } else {
      // 修改单价未税
      if (field === 'biddingItemDTO.untaxedUnitPrice') {
        setValue('biddingItemDTO.taxedUnitPrice', 0)
      } else if (field === 'biddingItemDTO.taxedUnitPrice') {
        // 修改单价含税
        setValue('biddingItemDTO.untaxedUnitPrice', 0)
      }

      //以下为实际分摊价格，计算
      setValue('biddingItemDTO.realSharePriceTaxed', 0)
      setValue('biddingItemDTO.realSharePriceUntaxed', 0)
      //以下为规划分摊价格，计算
      setValue('biddingItemDTO.planSharePriceTaxed', 0)
      setValue('biddingItemDTO.planSharePriceUntaxed', 0)
    }

    // 数量
    let requireQuantity = rowData?.itemExtMap?.requireQuantity
    if (requireQuantity > 0) {
      // 单价（未税）- 真 单价 / 价格单位 * 需求数量
      const untaxedUnitPriceReal = new Decimal32(untaxedUnitPrice)
        .div(priceUnitName)
        .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
      // 单价（含税）- 真 单价 / 价格单位 * 需求数量
      const taxedUnitPriceReal = new Decimal32(taxedUnitPrice)
        .div(priceUnitName)
        .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
      if (untaxedUnitPrice) {
        let untaxedTotalPrice = new Decimal32(requireQuantity)
          .mul(untaxedUnitPriceReal)
          .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
        untaxedTotalPrice = isFixedZero ? Math.round(untaxedTotalPrice) : untaxedTotalPrice
        setValue('biddingItemDTO.untaxedTotalPrice', untaxedTotalPrice)
        checkNumbers.push(untaxedTotalPrice)
      }
      if (taxedUnitPrice) {
        let taxedTotalPrice = new Decimal32(requireQuantity)
          .mul(taxedUnitPriceReal)
          .toFixed(ktFlag == 1 ? 5 : PRICE_FRACTION_DIGITS)
        taxedTotalPrice = isFixedZero ? Math.round(taxedTotalPrice) : taxedTotalPrice
        setValue('biddingItemDTO.taxedTotalPrice', taxedTotalPrice)
        checkNumbers.push(taxedTotalPrice)
      }
    }
  }

  if (oldValue !== value) {
    if (checkNumbers.find((val) => !isSafeNumber(val))) {
      microTask.then(() => {
        // 还原输入
        setValue(field, oldValue)
        // 重新计算
        inputPrice(ctx, { field, oldValue, value: oldValue })
      })
    }
  }
}

// 陆运总费计算：
// completeVehiclePrice 整车总价（1天压夜）=
// + landTransportFeePrice 陆运费报价
// + friendshipDrivingFee 友谊关代驾费
// + nightDrivingFee 代驾压夜费
// + nightCarFee 车辆滞留压夜费
// + declarationFee 报关费
export const inputCompleteVehiclePrice = (ctx, { field }) => {
  // logisticsDTO.setLandTransportFeePrice(Convert.toBigDecimal(logisticsDTO.getLandTransportFeePrice(), BigDecimal.ZERO));
  // logisticsDTO.setFriendshipDrivingFee(Convert.toBigDecimal(logisticsDTO.getFriendshipDrivingFee(), BigDecimal.ZERO));
  // logisticsDTO.setNightCarFee(Convert.toBigDecimal(logisticsDTO.getNightCarFee(), BigDecimal.ZERO));
  // logisticsDTO.setDeclarationFee(Convert.toBigDecimal(logisticsDTO.getDeclarationFee(), BigDecimal.ZERO));
  // logisticsDTO.setDoc(Convert.toBigDecimal(logisticsDTO.getDoc(), BigDecimal.ZERO));

  // //整车总价=陆运费报价+友谊关代驾费+代驾压夜费+代驾压夜费+报关费
  // logisticsDTO.setCompleteVehiclePrice(
  //   logisticsDTO.getLandTransportFeePrice()
  //   .add(logisticsDTO.getFriendshipDrivingFee())
  //   .add(logisticsDTO.getNightCarFee())
  //   .add(logisticsDTO.getNightDrivingFee())
  //   .add(logisticsDTO.getDeclarationFee()));
  // logisticsDTO.setCompleteVehiclePrice(logisticsDTO.getCompleteVehiclePrice().setScale(4, RoundingMode.HALF_DOWN));

  const getValue = ctx.getValueByField
  const setValue = ctx.setValueByField

  const prefix = 'biddingItemLogisticsDTO.'
  const fFriendshipDrivingFee = prefix + 'friendshipDrivingFee'
  const fNightDrivingFee = prefix + 'nightDrivingFee'
  const fNightCarFee = prefix + 'nightCarFee'
  const fDeclarationFee = prefix + 'declarationFee'
  const fCompleteVehiclePrice = prefix + 'completeVehiclePrice'
  const fLandTransportFeePrice = prefix + 'landTransportFeePrice'

  if (
    [
      fFriendshipDrivingFee,
      fNightDrivingFee,
      fNightCarFee,
      fDeclarationFee,
      fLandTransportFeePrice
    ].includes(field)
  ) {
    const nFriendshipDrivingFee = getValue(fFriendshipDrivingFee) || 0
    const nNightDrivingFee = getValue(fNightDrivingFee) || 0
    const nNightCarFee = getValue(fNightCarFee) || 0
    const nDeclarationFee = getValue(fDeclarationFee) || 0
    const nLandTransportFeePrice = getValue(fLandTransportFeePrice) || 0

    const nCompleteVehiclePrice = new Decimal32(nLandTransportFeePrice)
      .plus(nFriendshipDrivingFee)
      .plus(nNightDrivingFee)
      .plus(nNightCarFee)
      .plus(nDeclarationFee)
      .toFixed(PRICE_FRACTION_DIGITS)

    setValue(fCompleteVehiclePrice, nCompleteVehiclePrice)
    return true
  }
}

// 海运
// seaTransportFeeTotal 海运费总价 =
// twentyGpQuantity 20GP可提供数量 * twentyGpFee 20GP海运费用
// + fortyGpQuantity 40GP可提供数量 * fortyGpFee 40GP海运费用
// + fortyHcQuantity 40HC可提供数量 * fortyHcFee 40HC海运费用
// + fortyFiveHcQuantity 45HC可提供数量 * fortyFiveHcFee 45HC海运费用

export const inputSeaTransportFeeTotal = (ctx, { field }) => {
  const getValue = ctx.getValueByField
  const setValue = ctx.setValueByField
  const prefix = 'biddingItemLogisticsDTO.'

  const fTwentyGpQuantity = prefix + 'twentyGpQuantity'
  const fTwentyGpFee = prefix + 'twentyGpFee'

  const fFortyGpQuantity = prefix + 'fortyGpQuantity'
  const fFortyGpFee = prefix + 'fortyGpFee'

  const fFortyHcQuantity = prefix + 'fortyHcQuantity' // 40HC
  const fFortyHcFee = prefix + 'fortyHcFee'

  const fFortyFiveHcQuantity = prefix + 'fortyFiveHcQuantity' // 45HC
  const fFortyFiveHcFee = prefix + 'fortyFiveHcFee'

  const fSeaTransportFeeTotal = prefix + 'seaTransportFeeTotal'

  // 海运
  // totalFee =
  // seaTransportFeeTotal 海运费总价 + doc + seal + eir + otherFee 其他费用
  const fTotalFee = prefix + 'totalFee'

  const fDoc = prefix + 'doc'
  const fSeal = prefix + 'seal'
  const fEir = prefix + 'eir'
  // const fOtherFee = prefix + "otherFee";
  const nDoc = getValue(fDoc) || 0
  const nSeal = getValue(fSeal) || 0
  const nEir = getValue(fEir) || 0
  // const nOtherFee = getValue(fOtherFee) || 0;

  if (
    ![
      fTwentyGpQuantity,
      fTwentyGpFee,
      fFortyGpQuantity,
      fFortyGpFee,
      fFortyHcQuantity,
      fFortyHcFee,
      fFortyFiveHcQuantity,
      fFortyFiveHcFee,
      fDoc,
      fSeal,
      fEir
      // fOtherFee,
    ].includes(field)
  ) {
    return
  }

  const nTwentyGpQuantity = getValue(fTwentyGpQuantity) || 0
  const nTwentyGpThcFee = getValue(fTwentyGpFee) || 0
  const nFortyGpQuantity = getValue(fFortyGpQuantity) || 0
  const nFortyGpThcFee = getValue(fFortyGpFee) || 0
  const nFortyHcQuantity = getValue(fFortyHcQuantity) || 0
  const nFortyHcFee = getValue(fFortyHcFee) || 0
  const nFortyFiveHcQuantity = getValue(fFortyFiveHcQuantity) || 0
  const nFortyFiveHcFee = getValue(fFortyFiveHcFee) || 0

  const nSeaTransportFeeTotal = new Decimal32(
    new Decimal32(nTwentyGpQuantity).mul(nTwentyGpThcFee).toFixed(PRICE_FRACTION_DIGITS)
  )
    .plus(new Decimal32(nFortyGpQuantity).mul(nFortyGpThcFee).toFixed(PRICE_FRACTION_DIGITS))
    .plus(new Decimal32(nFortyHcQuantity).mul(nFortyHcFee).toFixed(PRICE_FRACTION_DIGITS))
    .plus(new Decimal32(nFortyFiveHcQuantity).mul(nFortyFiveHcFee).toFixed(PRICE_FRACTION_DIGITS))
    .toFixed(PRICE_FRACTION_DIGITS)

  setValue(fSeaTransportFeeTotal, nSeaTransportFeeTotal)

  const nTotalFee = new Decimal32(nSeaTransportFeeTotal)
    .plus(nDoc)
    .plus(nSeal)
    .plus(nEir)
    // .plus(nOtherFee)
    .toFixed(PRICE_FRACTION_DIGITS)

  setValue(fTotalFee, nTotalFee)
}

// 空运价格计算
export const inputAirTransport = utils.debounce(async (self, ctx) => {
  const setValue = ctx.setValueByField

  if (self.quotedPriceData.sourcingObjType !== 'air_transport') {
    return
  }

  const prefix = 'biddingItemLogisticsDTO.'
  const fTotalFee = prefix + 'totalFee'

  const res = await self.$API.supplyQdetail
    .caluatePrice({
      ...ctx.rowData,
      rfxId: self.rfxId
    })
    .catch(() => {})

  if (res?.data?.totalFee) {
    const { totalFee } = res.data
    setValue(fTotalFee, totalFee)
  }
}, 800)
