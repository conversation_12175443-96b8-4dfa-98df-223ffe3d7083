import { createEditInstance, Formatter } from '@/utils/ej/dataGrid/index'
import { fmtDatetime } from '@/utils/ej/dataGrid/formatter'
import { useFiltering } from '@/utils/ej/select'
import { makeTextFields, filteringByText } from 'ROUTER_COMMON/columnData/utils'
import { PRICE_EDIT_CONFIG } from '@/constants/editConfig'
import requiredHeader from '@/components/headerTemplate'
// 涉及单价和总价，后端预留4位小数。前端限制最多输入12个9（整数位），前端输入小数不改
const MAX_SAFE_INTEGER = 999999999999

import { getValueByPath, getEndDate } from '@/utils/obj'
import { i18n, permission } from '@/main.js'
const submitToolbar = [{ id: 'submit', icon: 'icon_solid_Createorder', title: i18n.t('提交') }]
export const columnData = ({ self }) => {
  const editInstance = createEditInstance()
    .onInput((ctx, { field, value }) => {
      //根据本币名称带出其他信息
      if (field === 'localCurrencyName') {
        const { dataSource, fields } = ctx.getOptions(field)
        const row = dataSource.find((e) => e[fields?.value || 'value'] === value)
        if (row) {
          ctx.setValueByField('localCurrencyCode', row.currencyCode)
        }
        self.fillPriceInfo(ctx)
      }

      if (field === 'minPurQuantity' || field === 'minPackageQuantity') {
        setTimeout(() => {
          let adviseMinPurQuantity = ctx.rowData.minPurQuantity //采购
          let adviseMinPackageQuantity = ctx.rowData.minPackageQuantity //包装
          let number = adviseMinPurQuantity / adviseMinPackageQuantity
          if (Math.floor(number) != number) {
            self.$bus.$emit(`contentDialog`)
          }
        }, 1500)
      }
      if (field === 'directDestOriginalPrice') {
        self.debounceFillPriceInfo(ctx)
      }
    })
    .onChange((ctx, { field }, event) => {
      if (field === 'purUnitCode') {
        if (event.itemData) {
          ctx.setValueByField('purUnitName', event.itemData.unitName)
        } else {
          ctx.setValueByField('purUnitName', '')
        }
      }
    })

  return [
    {
      width: '60',
      type: 'checkbox',
      allowEditing: false
    },
    {
      field: 'createTime',
      headerText: i18n.t('发布时间'),
      allowEditing: false,
      formatter: fmtDatetime,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-date-time-picker',
          format: 'yyyy-MM-dd HH:mm:ss',
          'time-stamp': true,
          'show-clear-button': false,
          disabled: true,
          readonly: true
        })
      })
    },
    {
      field: 'completeFlag',
      headerText: i18n.t('是否已提交'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          readonly: true,
          disabled: true,
          dataSource: [
            { text: i18n.t('未提交'), value: 0 },
            { text: i18n.t('已提交'), value: 1 }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('未提交')
          case 1:
            return i18n.t('已提交')
          default:
            return cellVal
        }
      },
      ignore: true
    },
    {
      field: 'approveStatus',
      headerText: i18n.t('审批状态'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          readonly: true,
          disabled: true,
          dataSource: [
            { text: i18n.t('未审批'), value: 0 },
            { text: i18n.t('已拒绝'), value: -1 },
            { text: i18n.t('已通过'), value: 1 }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (Number(cellVal)) {
          case 0:
            return i18n.t('未通过')
          case -1:
            return i18n.t('已拒绝')
          case 1:
            return i18n.t('已通过')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'supplierItemCode',
      headerText: i18n.t('供应商物料编码'),
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'text',
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'rfxCode',
      headerText: i18n.t('招标编号'),
      allowEditing: false
    },
    {
      field: 'biddingCode',
      headerText: i18n.t('投标编号'),
      allowEditing: false
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      allowEditing: false
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      allowEditing: false
    },
    {
      field: 'unitName',
      headerText: i18n.t('单位'),
      allowEditing: false
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('订单单位名称'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      }),
      allowEditing: false
    },
    {
      field: 'purUnitCode',
      headerText: i18n.t('订单单位编码'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'select',
          'show-clear-button': true,
          fields: makeTextFields('unitCode'),
          'allow-filtering': true,
          filtering: useFiltering(filteringByText),
          dataSource: []
        })
      })
    },
    {
      field: 'conversionRate',
      headerText: i18n.t('转换率'),
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'number',
          min: 0,
          max: MAX_SAFE_INTEGER,
          precision: 2
        })
      })
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      allowEditing: false
    },
    {
      field: 'purGroupName',
      headerText: i18n.t('采购组织'),
      allowEditing: false
    },
    {
      field: 'bidTaxRateValue',
      headerText: i18n.t('税率'),
      allowEditing: false
    },
    {
      field: 'currencyExchangeValue',
      headerText: i18n.t('汇率'),
      width: 100,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      }),
      allowEditing: false
    },
    {
      field: 'bidCurrencyName',
      headerText: i18n.t('原币'),
      allowEditing: false
    },
    {
      field: 'bidCurrencyCode',
      headerText: i18n.t('原币编码'),
      allowEditing: false
    },
    {
      field: 'localCurrencyName',
      headerText: i18n.t('本币名称'),
      width: 200,
      headerTemplate: () => {
        return { template: requiredHeader }
      },
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'mt-select',
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag,
          dataSource: self.currencyDataSource,
          fields: { value: 'currencyName', text: 'currencyName' },
          placeholder: i18n.t('本币名称'),
          'allow-filtering': true,
          filtering: useFiltering(function (e) {
            if (typeof e.text === 'string' && e.text) {
              e.updateData(
                self.currencyDataSource.filter((f) => f?.currencyName.indexOf(e.text) > -1)
              )
            } else {
              e.updateData(self.currencyDataSource)
            }
          })
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        return self.unitDataSource?.find((e) => e.value === cellVal)?.text ?? cellVal
      }
    },
    {
      field: 'localCurrencyCode',
      width: 200,
      headerText: i18n.t('本币编码'),
      // visible: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      }),
      allowEditing: false
    },
    {
      field: 'quoteAttribute',
      headerText: i18n.t('报价属性'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          readonly: true,
          disabled: true,
          dataSource: [
            { text: i18n.t('标准价'), value: 'standard_price' },
            { text: i18n.t('寄售价'), value: 'mailing_price' }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (cellVal) {
          case 'standard_price':
            return i18n.t('标准价')
          case 'mailing_price':
            return i18n.t('寄售价')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'quoteMode',
      headerText: i18n.t('行报价生效方式号'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'mt-select',
          readonly: true,
          disabled: true,
          dataSource: [
            { text: i18n.t('按入库'), value: 'in_warehouse' },
            { text: i18n.t('按出库'), value: 'out_warehouse' },
            { text: i18n.t('按订单日期'), value: 'order_date' }
          ]
        })
      }),
      formatter: ({ field }, item) => {
        const cellVal = getValueByPath(item, field)
        switch (cellVal) {
          case 'in_warehouse':
            return i18n.t('按入库')
          case 'out_warehouse':
            return i18n.t('按出库')
          case 'order_date':
            return i18n.t('按订单日期')
          default:
            return cellVal
        }
      }
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('单价(原币)'),
      allowEditing: false
    },
    {
      field: 'untaxedLocalUnitPrice',
      headerText: i18n.t('单价(本币）'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'directDestLocalPrice',
      headerText: i18n.t('直送地价格(本币）'),
      allowEditing: false,
      edit: editInstance.create({
        getEditConfig: () => ({
          type: 'text',
          readonly: true,
          disabled: true
        })
      })
    },
    {
      field: 'directDestOriginalPrice',
      headerText: i18n.t('直送地(原币)'),
      allowEditing: true,
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          ...PRICE_EDIT_CONFIG,
          type: 'number',
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'quoteEffectiveStartDate',
      headerText: i18n.t('有效期从'),
      allowEditing: true,
      headerTemplate: () => {
        return { template: requiredHeader }
      },
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        onInput: (ctx, { value }) => {
          let { rowData } = ctx
          if (value && ['1503', '0602'].includes(rowData.companyCode)) {
            let _endDate = getEndDate(value)
            ctx.setValueByField('quoteEffectiveEndDate', _endDate)
          }
          if (value && self.txCompanyList.includes(rowData.companyCode)) {  // 通讯公司特殊处理
            // 判断是否为非采 (rfxGeneralType: 1=通采, 2=非采)
            const isNonProcurement = rowData.rfxGeneralType === 2

            if (isNonProcurement) {
              // 非采：报价有效期至为报价有效期从的半年后日期
              const startDate = new Date(value)
              const endDate = new Date(startDate)
              endDate.setMonth(startDate.getMonth() + 6) // 设置为半年后
              const formattedEndDate = endDate.getFullYear() + '-' +
                String(endDate.getMonth() + 1).padStart(2, '0') + '-' +
                String(endDate.getDate()).padStart(2, '0')
              ctx.setValueByField('quoteEffectiveEndDate', formattedEndDate)
            } else {
              // 通采：按原有逻辑设置为2099-12-31
              ctx.setValueByField('quoteEffectiveEndDate', '2099-12-31')
            }
          }
        },
        getEditConfig: ({ rowData }) => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false,
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'quoteEffectiveEndDate',
      headerText: i18n.t('有效期至'),
      allowEditing: true,
      headerTemplate: () => {
        return { template: requiredHeader }
      },
      formatter: Formatter.createFmtDatetime('YYYY-MM-DD'),
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'date',
          format: 'yyyy-MM-dd',
          'time-stamp': true,
          'show-clear-button': false,
          disabled:
            !!rowData.completeFlag ||
            rowData.quoteEffectiveEndDateEditAble === 0 ||
            rowData.companyCode == '1503',
          readonly:
            !!rowData.completeFlag ||
            rowData.quoteEffectiveEndDateEditAble === 0 ||
            rowData.companyCode == '1503'
        })
      })
    },
    {
      field: 'leadTime',
      headerText: 'L/T',
      headerTemplate: () => {
        return { template: requiredHeader }
      },
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'number',
          min: 1,
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'unconditionalLeadTime',
      headerText: i18n.t('无条件(L/T)'),
      headerTemplate: () => {
        return { template: requiredHeader }
      },
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'number',
          min: 1,
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'minPackageQuantity',
      headerText: i18n.t('最小包装数'),
      headerTemplate: () => {
        return { template: requiredHeader }
      },
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'number',
          min: 1,
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'minPurQuantity',
      headerText: i18n.t('最小采购量'),
      headerTemplate: () => {
        return { template: requiredHeader }
      },
      edit: editInstance.create({
        getEditConfig: ({ rowData }) => ({
          type: 'number',
          min: 1,
          disabled: !!rowData.completeFlag,
          readonly: !!rowData.completeFlag
        })
      })
    },
    {
      field: 'deliveryPlace',
      headerText: i18n.t('直送地'),
      allowEditing: false
    }
  ]
}

export const pageConfig = (url, afterAsyncDataFunc) => [
  {
    title: i18n.t('未提交'),
    useToolTemplate: false,
    toolbar: submitToolbar,
    gridId: permission.gridId['supply']['priceInformation'],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal',
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      },
      columnData: [],
      asyncConfig: {
        url,
        serializeList: afterAsyncDataFunc,
        defaultRules: [
          {
            field: 'completeFlag',
            label: '是否已提交',
            operator: 'contains',
            type: 'string',
            value: '0'
          }
        ]
      }
    }
  },
  {
    title: i18n.t('已提交'),
    useToolTemplate: false,
    toolbar: [],
    gridId: permission.gridId['supply']['priceInformation1'],
    grid: {
      allowFiltering: true,
      lineIndex: true,
      editSettings: {
        allowAdding: false,
        allowEditing: false,
        allowDeleting: false,
        mode: 'Normal',
        allowEditOnDblClick: false,
        showConfirmDialog: false,
        showDeleteConfirmDialog: true,
        newRowPosition: 'Top'
      },
      columnData: [],
      asyncConfig: {
        url,
        serializeList: afterAsyncDataFunc,
        defaultRules: [
          {
            field: 'completeFlag',
            label: '是否已提交',
            operator: 'contains',
            type: 'string',
            value: '1'
          }
        ]
      }
    }
  }
]
