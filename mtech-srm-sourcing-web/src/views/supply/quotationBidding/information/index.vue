<!--供方-寻源协同-报价 主页面 -->
<template>
  <div id="app">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      v-if="pageConfig[0].grid.columnData.length > 0"
    />
  </div>
</template>

<script>
import { pageConfig, columnData } from './config'
import { addArrTextField } from 'ROUTER_COMMON/columnData/utils'
import debounce from 'lodash.debounce'
export default {
  components: {},
  data() {
    return {
      pageConfig: pageConfig(this.$API.quotationList.fetchInformation, this.updateTabTitle),
      currencyDataSource: [],
      debounceFillPriceInfo: null,
      txCompanyList: ['2M01', '2S06']
    }
  },
  async mounted() {
    await this.getTxCompanyList()
    this.$bus.$on('contentDialog', () => {
      if (!this.txCompanyList.includes(this.$route.query?.companyCode)) {
        this.$toast({
          content: this.$t('最小采购量需是最小包装数量的整数倍'),
          type: 'warning'
        })
      }
    })
    this.initColumnData()
    this.debounceFillPriceInfo = debounce(this.fillPriceInfo, 1000)
  },
  methods: {
    async getTxCompanyList() {
      let params = {
        dictCode: 'TXComCode'
      }
      const res = await this.$API.masterData.getSupplierDictionary(params)
      if (res.code === 200) {
        this.txCompanyList = res.data.map((item) => item.itemCode)
        // 存储到sessionStorage供工具函数使用
        sessionStorage.setItem('txCompanyList', JSON.stringify(this.txCompanyList))
      }
    },
    // 结束编辑状态
    endEdit() {
      let _current = this.$refs.templateRef.getCurrentTabRef()
      _current?.grid.endEdit()
    },
    updateTabTitle(records) {
      records.forEach((e) => {
        if (Number(e.quoteEffectiveEndDate) === 0) {
          e.quoteEffectiveEndDate = null
        }
        if (Number(e.quoteEffectiveStartDate) === 0) {
          e.quoteEffectiveStartDate = null
        }
      })
      return records
    },
    checkRecords(rows) {
      for (let i = 0; i < rows.length; i++) {
        if (rows[i].unconditionalLeadTime < rows[i].leadTime) {
          this.$toast({
            content: this.$t(`第${i + 1}条提交数据有误:无条件LT不能小于LT`),
            type: 'warning'
          })
          return false
        }
      }
      return true
    },
    async handleClickToolBar(e) {
      if (e.toolbar.id == 'submit') {
        this.endEdit()
        let flag = false
        let timeError = false
        let packing = false
        let index = null
        let leadTimeErr = false
        let _selectGridRecords = e.gridRef.getMtechGridRecords()
        if (!this.checkRecords(_selectGridRecords)) {
          return
        }
        _selectGridRecords.map((i, n) => {
          if (i.completeFlag === 1 && i.approveStatus !== -1) {
            flag = true
          }
          if (Number(i.quoteEffectiveEndDate) < Number(i.quoteEffectiveStartDate)) {
            timeError = true
          }
          if (
            i.minPurQuantity / i.minPackageQuantity !==
            Math.floor(i.minPurQuantity / i.minPackageQuantity)
          ) {
            packing = true
            index = n
            return
          }
          if (i.unconditionalLeadTime < i.leadTime) {
            leadTimeErr = true
            index = n
          }
        })
        if (_selectGridRecords.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        } else if (flag) {
          this.$toast({ content: this.$t('不能重复提交'), type: 'warning' })
        } else if (timeError) {
          this.$toast({
            content: this.$t('有效期开始时间不能大于结束时间'),
            type: 'warning'
          })
        } else if (packing && !this.txCompanyList.includes(this.$route.query?.companyCode)) {
          this.$toast({
            content: this.$t(`第${index + 1}条提交数据最小采购量需是最小包装数量的整数倍`),
            type: 'warning'
          })
        } else if (leadTimeErr) {
          this.$toast({
            content: this.$t(`第${index}条提交数据无条件L/T必须>=L/T`),
            type: 'warning'
          })
        } else {
          this.$API.quotationList.submitInformation(_selectGridRecords).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      }
    },
    async initData() {
      const [currencyDataSource] = await Promise.all([
        this.$API.masterData.queryAllCurrency()
      ]).catch(() => {})
      if (!currencyDataSource?.data) {
        return
      }
      //币种
      this.currencyDataSource = currencyDataSource.data
    },
    async initColumnData() {
      await this.initData()
      this.$set(
        this.pageConfig[0].grid,
        'columnData',
        columnData({
          self: this
        })
      )
      this.$set(
        this.pageConfig[1].grid,
        'columnData',
        columnData({
          self: this
        })
      )
    },
    // 获取价格信息
    async queryPriceInfo(row) {
      return await this.$API.quotationList.validAppendInfoList([row])
    },
    // 填充价格信息
    fillPriceInfo(ctx) {
      this.queryPriceInfo(ctx.rowData).then((res) => {
        if (res.code === 200) {
          let _res = res.data[0]
          ctx.setValueByField('currencyExchangeValue', _res.currencyExchangeValue)
          ctx.setValueByField('untaxedLocalUnitPrice', _res.untaxedLocalUnitPrice)
          ctx.setValueByField('directDestLocalPrice', _res.directDestLocalPrice)
        }
      })
    },
    //行内离开事件
    actionComplete(e) {
      if (e.requestType === 'beginEdit') {
        this.$API.pur
          .getPurUnits({
            rfxId: e.rowData.rfxId
          })
          .then((res) => {
            // e.row.editInstance.setOptions("purUnitName", {
            //   dataSource: addArrTextField(res.data, "unitCode", "unitName"),
            // });
            e.row.editInstance.setOptions('purUnitCode', {
              dataSource: addArrTextField(res.data, 'unitCode', 'unitName')
            })
          })
      }
    }
  }
}
</script>
<style>
.OfferBid-status-class {
  margin-bottom: 5px;
}
.OfferBid-status0 {
  width: 56px;
  height: 20px;
  background: #f4f4f4;
  border-radius: 2px;
  padding: 4px;
  color: #9a9a9a;
}
.OfferBid-status1 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
.OfferBid-status2 {
  width: 56px;
  height: 20px;
  background: #eef2f9;
  border-radius: 2px;
  padding: 4px;
  color: #6386c1;
}
</style>
<style>
.user-content {
  color: red;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}
#app {
  padding: 0;
  height: 100%;
  width: 100%;
}
.test-btn {
  position: fixed;
  bottom: 10px;
  z-index: 2;
  left: 50px;
}
</style>
