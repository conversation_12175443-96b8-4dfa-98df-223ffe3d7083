

## 终止返利协议


**接口地址**:`/api/analysis/tenant/rebateHeader/stopAgreement`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "associatedVoucherNumber": "",
  "headerId": 0,
  "reason": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|req|req|body|true|返利协议终止请求对象|返利协议终止请求对象|
|&emsp;&emsp;associatedVoucherNumber|关联协议单号||false|string||
|&emsp;&emsp;headerId|返利协议单ID||false|integer(int64)||
|&emsp;&emsp;reason|协议终止原因||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|通用返回统一封装对象«string»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|响应编码|integer(int32)|integer(int32)|
|data|数据|string||
|errorStackTrace|错误堆栈信息|string||
|msg|提示信息|string||
|success|是否成功|boolean||
|traceId|追踪ID|string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": "",
	"errorStackTrace": "",
	"msg": "",
	"success": true,
	"traceId": ""
}
```